# Generated by Django 4.2.20 on 2025-05-26 14:19

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import django_multitenant.mixins


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('core', '0156_skumaster_mandate_scan'),
        ('outbound', '0078_auditentry_auditmaster_auditdetail'),
        ('wms_base', '0042_scriptusers_scriptmodel'),
        ('inventory', '0063_stockdetail_idx_positive_so_picking'),
    ]

    operations = [
        migrations.CreateModel(
            name='ReplenishmentClassification',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('creation_date', models.DateTimeField(auto_now_add=True)),
                ('updation_date', models.DateTimeField(auto_now=True)),
                ('avg_sales_day', models.FloatField(default=0)),
                ('avg_sales_day_value', models.FloatField(default=0)),
                ('cumulative_contribution', models.FloatField(default=0)),
                ('classification', models.CharField(default='', max_length=64)),
                ('min_stock_qty', models.FloatField(default=0)),
                ('max_stock_qty', models.FloatField(default=0)),
                ('replenishment_qty', models.FloatField(default=0)),
                ('reserved', models.FloatField(default=0)),
                ('suggested_qty', models.FloatField(default=0)),
                ('avail_qty', models.FloatField(default=0)),
                ('sku_avail_qty', models.FloatField(default=0)),
                ('sku_pen_po_qty', models.FloatField(default=0)),
                ('sku_pen_putaway_qty', models.FloatField(default=0)),
                ('modified_suggested_qty', models.FloatField(default=0)),
                ('remarks', models.CharField(default='', max_length=64)),
                ('status', models.IntegerField(default=1)),
                ('account', models.ForeignKey(blank=True, db_constraint=False, on_delete=django.db.models.deletion.CASCADE, related_name='%(class)ss_account', to='wms_base.userprofile')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created_by', to=settings.AUTH_USER_MODEL)),
                ('dest_location', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='dest_location', to='inventory.locationmaster')),
                ('seller', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='seller', to='outbound.sellermaster')),
                ('sku', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='core.skumaster')),
                ('source_stock', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='source_stock', to='inventory.stockdetail')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated_by', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'REPLENISHMENT_CLASSIFICATION',
                'index_together': {('classification', 'sku', 'status'), ('sku', 'status')},
            },
            bases=(models.Model, django_multitenant.mixins.TenantModelMixin),
        ),
    ]
