# python imports
import pandas as pd
from datetime import datetime, timedelta

# django imports
from django.utils import timezone
from django.db.models import F

# wms base imports
from wms_base.models import User

# core imports
from core.models import SKUMaster
from core_operations.views.common.main import get_multiple_misc_values

# inbound imports
from inbound.models import (
    SKUSupplier, SupplierMaster
)

# inventory imports
from inventory.models import (
    StockDetail, ReplenishmentClassification
)
from .base import ReplenishmentMixin


class STOReplenishment(ReplenishmentMixin):
    def __init__(self, user_id, suppliers=[], automate=False, log=None) -> None:
        self.suppliers = suppliers
        self.log = log if log else None
        super().__init__(user_id, automate)

    def _set_ars_warehouse(self, central_warehouse, ars_warehouses):
        """
        Set the central warehouse and ARS warehouses for replenishment.
        Args:
            central_warehouse (dict): Dictionary containing central warehouse details.
            ars_warehouses (list): List of ARS warehouses.
        """
        self.central_warehouse = central_warehouse[0] if isinstance(central_warehouse, list) else central_warehouse
        self.ars_warehouses = ars_warehouses if isinstance(ars_warehouses, list) else [ars_warehouses]
        self.ars_warehouse_ids = {}
        self.ars_warehouses_dict = {}
        try:
            self.ars_warehouses_objs = User.objects.filter(username__in=self.ars_warehouses)
            if not self.ars_warehouses_objs.exists():
                self.log.error(f"No valid ARS warehouses found for the provided usernames {ars_warehouses}")
                raise ValueError("No valid ARS warehouses found for the provided usernames.")

            # Set ARS warehouse IDs and usernames
            for ars_warehouse in self.ars_warehouses_objs:
                self.ars_warehouse_ids.setdefault(ars_warehouse.id, []).append(ars_warehouse.id)
                self.ars_warehouses_dict.setdefault(ars_warehouse.id, []).append(ars_warehouse.username)

            self.central_warehouse = User.objects.get(username=self.central_warehouse)
        except User.DoesNotExist as e:
            raise ValueError(f"Invalid warehouse details provided: {e}")

    def _set_misc_values(self, misc_values=None):
        """
        Set miscellaneous values for replenishment.
        Args:
            misc_values (dict): Dictionary containing miscellaneous values.
        """
        # If misc_values is None, fetch default values
        self.misc_values_dict = {}
        misc_types = ["bulk_zones_list", "pick_zones_list", "closing_stock_zones"]
        if misc_values is None:
            self.misc_values_dict = get_multiple_misc_values(misc_types, self.user_id)
        self.misc_values_dict.update(misc_values)

    def _execute_replenishment_workflow(self, average_days):
        user_id = self.user_id
        self.run_store_replenishment_workflow(user_id, average_days)

        # Execute the replenishment workflow for the central warehouse and ARS warehouses
        self._execute_central_wh_replenishment_workflow(
            central_warehouse=self.central_warehouse,
            ars_warehouses=self.ars_warehouses_dict.get(self.user_id, [self.user_id])
        )

    def _execute_central_wh_replenishment_workflow(self, central_warehouse=None, ars_warehouses=None):
        """
        Execute the replenishment workflow for the central warehouse.
        Args:
            average_days (int): The average days for replenishment calculation.
        """
        ars_warehouse_ids = self.ars_warehouse_ids.get(self.user_id, [])
        self.prepare_replenishment(central_warehouse, ars_warehouse_ids)
        self.set_replenishment(central_warehouse, ars_warehouses)

    def st_replenishment_workflow(self, average_days):
        return self._execute_replenishment_workflow(average_days)

    def get_replenishment_sku_ids(self, user_id):
        """
        Get SKU IDs for replenishment based on user and suppliers.
        Args:
            user_id (int): The ID of the user for whom to fetch SKUs.
        Returns:
            list: List of SKU IDs that are active and match the suppliers.
        """
        if not user_id:
            return []

        # Fetch active SKUs for the user
        sku_ids = list(SKUMaster.objects.filter(user=user_id, status=1).values_list('id', flat=True))
        if not sku_ids:
            return []

        # If no suppliers are provided, return all active SKUs
        # # Filter SKUs by suppliers if provided
        if self.suppliers:
            sku_ids = list(
                SKUSupplier.objects.filter(
                    sku__in=sku_ids,
                    supplier__name__in=self.suppliers,
                    supplier__status=1
                ).values_list(
                    'sku_id', flat=True
                ).distinct())
        return sku_ids

    def get_sku_classification(self, users):
        return ReplenishmentClassification.objects.filter(
                sku__user__in=users,
                creation_date__gte=timezone.now().date(),
                replenishment_qty__gt=0)

    def set_replenishment(self, central_warehouse=None, ars_warehouses=None):
        return super().set_replenishment(central_warehouse, ars_warehouses)
    
    def process_po_supplier_data(self, store_whs_full_df):
        """ Process purchase order supplier data for child warehouses.
        This method retrieves supplier data for child warehouses and merges it
        """

        ars_warehouse_ids = self.ars_warehouse_ids.get(self.user_id, [])
        po_supplier_df = pd.DataFrame(
            SupplierMaster.objects.filter(
                user__in=ars_warehouse_ids,
                supplier_type="internal",
                supplier_id=self.central_warehouse
            ).values(
                'id'
            ).annotate(
                child_user_id=F("user"),
                po_supplier_id=F("supplier_id"),
                supplier_id=F("id"),
                supplier_state=F("state"),
                supplier_type=F("supplier_type")
            )
        )
        store_whs_full_df = store_whs_full_df.rename(columns={
            'user_id': 'child_user_id',
            'sku_id': 'child_sku_id'
        })
        store_whs_full_df = store_whs_full_df.merge(po_supplier_df, how="left")
        return store_whs_full_df

    def merge_with_master_sku(self, child_whs_full_df):
        """
        Merge child warehouse data with master SKU data.

        This method retrieves supplier data for SKUs and merges it with child
        warehouse data. It also calculates lead time and expected delivery dates.

        Args:
            child_whs_full_df (pandas.DataFrame): DataFrame with child warehouse data

        Returns:
            pandas.DataFrame: Enhanced DataFrame with supplier and delivery information
        """
        child_whs_full_df = self.process_po_supplier_data(child_whs_full_df)
        # Retrieve supplier data for SKUs

        sku_suppliers_df = pd.DataFrame(
            SKUSupplier.objects.filter(
                supplier__id__in=child_whs_full_df['supplier_id'],
                sku__id__in=child_whs_full_df['child_sku_id']
            ).values(
                'lead_time', 'sku__id', 'supplier__id')
            )

        # Rename columns to match child warehouse data
        sku_suppliers_df = sku_suppliers_df.rename(columns={
            'sku__id': 'child_sku_id',
            'supplier__id': 'supplier_id'
        })

        # Merge with child warehouse data
        if not sku_suppliers_df.empty:
            child_whs_full_df = child_whs_full_df.merge(sku_suppliers_df, on=['child_sku_id', 'supplier_id'], how='left')
        else:
            child_whs_full_df['lead_time'] = 0

        # Fill missing lead times with zero
        child_whs_full_df['lead_time'] = child_whs_full_df['lead_time'].fillna(0)

        # Calculate expected delivery date based on lead time
        child_whs_full_df['exp_delivery_date'] = child_whs_full_df['lead_time'].apply(
            lambda lead_time: (timezone.now().today() + timedelta(days=lead_time)).strftime("%m/%d/%Y"))

        return child_whs_full_df