import ast
from datetime import datetime
from abc import ABC, abstractmethod

from django.core.management import BaseCommand, CommandError

#wms base
from wms_base.wms_utils import init_logger
from wms_base.models import User

#core imports
from core.models import MiscDetail

log = init_logger('logs/base_stock_transfer_orders.log')


class BaseStockTransferCommand(BaseCommand, ABC):
    """
    Base class for Stock Transfer Order commands
    
    This abstract base class provides common functionality for both
    auto_stock_transfer_orders.py and ars_stock_transfer_orders.py
    """
    
    help = "Base Stock Transfer Orders"
    
    def add_arguments(self, parser):
        """
        Define command line arguments
        """
        parser.add_argument('extra_params', type=str, default=None)
    
    def handle(self, *args, **options):
        """
        Entry point for the command
        
        Args:
            *args: Variable length argument list
            **options: Arbitrary keyword arguments including 'input_dict'
        """
        try:
            extra_data = ast.literal_eval(options['extra_params'])
            self.ars_warehouses = extra_data.get('ars_warehouses', [])
            ars_warehouses_objs = User.objects.filter(username__in=self.ars_warehouses)
            self.central_warehouse = extra_data.get('central_warehouse', {})
            log.info('ARS Calculation started for warehouses: {0}, time: {1}'.format(self.ars_warehouses, datetime.now()))
        except Exception as e:
            log.debug('ARS Calculation failed for warehouses: {0}, error: {1}'.format(self.ars_warehouses, str(e)))
            raise CommandError('ARS Calculation failed for warehouses: {0}, error: {1}'.format(self.ars_warehouses, str(e)))

        # Extract configuration from input dictionary
        self.average_days = extra_data.get('average_days', 14)
        self.calculation_type = extra_data.get('calculation_type', "abc")
        self.suppliers = extra_data.get('suppliers', [])

        # Initialize logger
        self.log = self.initialize_logger()

        # Get configuration values and process each warehouse
        user_ids = list(ars_warehouses_objs.values_list('id', flat=True))
        self.get_misc_values(user_ids)

        if not self.central_warehouse:
            raise CommandError("Central warehouse is not provided in the input parameters.")
        if not self.ars_warehouses:
            raise CommandError("ARS warehouses are not provided in the input parameters.")

        for ars_wh in ars_warehouses_objs:
            self.user = ars_wh
            self.username = str(ars_wh.username)
            self.central_warehouse = self.central_warehouse
            self.process_stock_transfer_orders()

    @abstractmethod
    def initialize_logger(self):
        """
        Initialize the logger for the specific command
        
        Returns:
            Logger instance
        """
        pass

    def process_stock_transfer_orders(self):
        """
        Process stock transfer orders for a warehouse
        
        This method orchestrates the stock transfer process based on the
        configured calculation type.
        """
        # Log start of process
        command_name = self.get_command_name()
        self.log.info(f"{self.username} {command_name}: Start time - {datetime.now()} Calculation Type = {self.calculation_type}")

        # Execute appropriate calculation method
        if str(self.calculation_type).lower() == "abc":
            self.abc_calculation()
        else:
            raise NotImplementedError("Calculation type not implemented")

        # Log completion of process
        self.log.info(f"{self.username} {command_name} Completed for {self.username} at {datetime.now()}")
    
    def get_misc_values(self, user_ids):
        """
        Get all needed misc values from the database
        
        Retrieves configuration values from MiscDetail table and stores them
        in self.switch_values dictionary.
        """
        self.switch_values = {}
        misc_types = [
            'bulk_zones_list',
            'pick_zones_list'
        ]
        # Query MiscDetail for configuration values
        misc_data = list(MiscDetail.objects.filter(
            user__in=user_ids, misc_type__in=misc_types).values('user', 'misc_type', 'misc_value'))
        
        # Store values in switch_values dictionary
        for misc in misc_data:
            user_id = misc.get('user')
            self.switch_values.setdefault(user_id, {})
            self.switch_values[user_id][misc.get('misc_type', '')] = misc.get('misc_value', '')

    @abstractmethod
    def abc_calculation(self):
        """
        Implement ABC classification-based inventory movement
        
        This method should be implemented by subclasses to handle the
        specific replenishment logic.
        """
        pass

    @abstractmethod
    def get_command_name(self):
        """
        Get the name of the command for logging purposes
        
        Returns:
            str: Command name
        """
        pass

    def process_abc_calculation(self, replenishment_cls):
        try:
            ars_sto_rep = replenishment_cls(user_id=self.user.id, automate=True, suppliers=self.suppliers, log=self.log)
            ars_sto_rep._set_misc_values(self.switch_values.get(self.user.id, {}))
            ars_sto_rep._set_ars_warehouse(self.central_warehouse, self.user.username)
            ars_sto_rep._execute_replenishment_workflow(self.average_days)
        except Exception as e:
            log.debug(f"Error in ABC calculation for user {self.user.username}: {str(e)}")