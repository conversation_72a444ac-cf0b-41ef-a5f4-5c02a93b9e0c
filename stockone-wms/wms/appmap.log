[2025-04-28 05:32:52,658] INFO _appmap.configuration: file: /application/appmap.yml
[2025-04-28 05:32:52,659] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-04-28 05:32:52,659] INFO _appmap.configuration: env: environ({'HOSTNAME': '138c3f1451dd', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DEBUG': '1', 'HOME': '/root', 'DB_NAME': 'stockone', 'PYTHONUNBUFFERED': '1', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'CONN_MAX_AGE': '300', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'DB_REPLICA_PASS': 'stockone^123', 'TASK_USAGE': '0', 'ENTRYPOINT_FLAG': 'true', 'AWS_S3_REGION_NAME': 'ap-south-1', 'TERM': 'xterm', 'DB_REPLICA_HOST': 'db1', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'DB_REPORTS_PASS': 'stockone^123', 'DB_REPLICA_USER': 'stockone', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'LANG': 'C.UTF-8', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_REPORTS_HOST': 'db1', 'DB_REPORTS_USER': 'stockone', 'PYTHON_VERSION': '3.12.10', 'DB_PASS': 'stockone^123', 'AWS_ID': '********************', 'DB_REPLICA_NAME': 'stockone', 'USE_S3': 'TRUE', 'PWD': '/application', 'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'DB_HOST': 'db1', 'DB_USER': 'stockone', 'DB_REPORTS_NAME': 'stockone'})
[2025-04-28 05:32:52,663] INFO _appmap.env: appmap enabled: False
[2025-04-28 05:33:00,358] INFO _appmap.configuration: file: /application/appmap.yml
[2025-04-28 05:33:00,358] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-04-28 05:33:00,358] INFO _appmap.configuration: env: environ({'HOSTNAME': '138c3f1451dd', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DEBUG': '1', 'HOME': '/root', 'DB_NAME': 'stockone', 'PYTHONUNBUFFERED': '1', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'CONN_MAX_AGE': '300', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'DB_REPLICA_PASS': 'stockone^123', 'TASK_USAGE': '0', 'ENTRYPOINT_FLAG': 'true', 'AWS_S3_REGION_NAME': 'ap-south-1', 'TERM': 'xterm', 'DB_REPLICA_HOST': 'db1', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'DB_REPORTS_PASS': 'stockone^123', 'DB_REPLICA_USER': 'stockone', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'LANG': 'C.UTF-8', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_REPORTS_HOST': 'db1', 'DB_REPORTS_USER': 'stockone', 'PYTHON_VERSION': '3.12.10', 'DB_PASS': 'stockone^123', 'AWS_ID': '********************', 'DB_REPLICA_NAME': 'stockone', 'USE_S3': 'TRUE', 'PWD': '/application', 'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'DB_HOST': 'db1', 'DB_USER': 'stockone', 'DB_REPORTS_NAME': 'stockone'})
[2025-04-28 05:33:00,361] INFO _appmap.env: appmap enabled: False
[2025-04-28 05:33:03,099] INFO _appmap.configuration: file: /application/appmap.yml
[2025-04-28 05:33:03,099] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-04-28 05:33:03,099] INFO _appmap.configuration: env: environ({'HOSTNAME': '138c3f1451dd', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DEBUG': '1', 'HOME': '/root', 'DB_NAME': 'stockone', 'PYTHONUNBUFFERED': '1', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'CONN_MAX_AGE': '300', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'DB_REPLICA_PASS': 'stockone^123', 'TASK_USAGE': '0', 'ENTRYPOINT_FLAG': 'true', 'AWS_S3_REGION_NAME': 'ap-south-1', 'TERM': 'xterm', 'DB_REPLICA_HOST': 'db1', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'DB_REPORTS_PASS': 'stockone^123', 'DB_REPLICA_USER': 'stockone', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'LANG': 'C.UTF-8', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_REPORTS_HOST': 'db1', 'DB_REPORTS_USER': 'stockone', 'PYTHON_VERSION': '3.12.10', 'DB_PASS': 'stockone^123', 'AWS_ID': '********************', 'DB_REPLICA_NAME': 'stockone', 'USE_S3': 'TRUE', 'PWD': '/application', 'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'DB_HOST': 'db1', 'DB_USER': 'stockone', 'DB_REPORTS_NAME': 'stockone'})
[2025-04-28 05:33:03,103] INFO _appmap.env: appmap enabled: False
[2025-04-28 05:33:05,863] INFO _appmap.configuration: file: /application/appmap.yml
[2025-04-28 05:33:05,863] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-04-28 05:33:05,863] INFO _appmap.configuration: env: environ({'HOSTNAME': '138c3f1451dd', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DEBUG': '1', 'HOME': '/root', 'DB_NAME': 'stockone', 'PYTHONUNBUFFERED': '1', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'CONN_MAX_AGE': '300', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'DB_REPLICA_PASS': 'stockone^123', 'TASK_USAGE': '0', 'ENTRYPOINT_FLAG': 'true', 'AWS_S3_REGION_NAME': 'ap-south-1', 'TERM': 'xterm', 'DB_REPLICA_HOST': 'db1', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'DB_REPORTS_PASS': 'stockone^123', 'DB_REPLICA_USER': 'stockone', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'LANG': 'C.UTF-8', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_REPORTS_HOST': 'db1', 'DB_REPORTS_USER': 'stockone', 'PYTHON_VERSION': '3.12.10', 'DB_PASS': 'stockone^123', 'AWS_ID': '********************', 'DB_REPLICA_NAME': 'stockone', 'USE_S3': 'TRUE', 'PWD': '/application', 'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'DB_HOST': 'db1', 'DB_USER': 'stockone', 'DB_REPORTS_NAME': 'stockone'})
[2025-04-28 05:33:05,866] INFO _appmap.env: appmap enabled: False
[2025-04-28 05:33:10,352] INFO _appmap.configuration: file: /application/appmap.yml
[2025-04-28 05:33:10,352] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-04-28 05:33:10,352] INFO _appmap.configuration: env: environ({'HOSTNAME': '138c3f1451dd', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DEBUG': '1', 'HOME': '/root', 'DB_NAME': 'stockone', 'PYTHONUNBUFFERED': '1', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'CONN_MAX_AGE': '300', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'DB_REPLICA_PASS': 'stockone^123', 'TASK_USAGE': '0', 'ENTRYPOINT_FLAG': 'true', 'AWS_S3_REGION_NAME': 'ap-south-1', 'TERM': 'xterm', 'DB_REPLICA_HOST': 'db1', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'DB_REPORTS_PASS': 'stockone^123', 'DB_REPLICA_USER': 'stockone', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'LANG': 'C.UTF-8', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_REPORTS_HOST': 'db1', 'DB_REPORTS_USER': 'stockone', 'PYTHON_VERSION': '3.12.10', 'DB_PASS': 'stockone^123', 'AWS_ID': '********************', 'DB_REPLICA_NAME': 'stockone', 'USE_S3': 'TRUE', 'PWD': '/application', 'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'DB_HOST': 'db1', 'DB_USER': 'stockone', 'DB_REPORTS_NAME': 'stockone'})
[2025-04-28 05:33:10,355] INFO _appmap.env: appmap enabled: False
[2025-04-28 05:33:10,444] INFO _appmap.env: appmap enabled: False
[2025-04-28 05:33:10,451] INFO _appmap.env: appmap enabled: False
[2025-04-28 05:33:37,814] INFO _appmap.configuration: file: /application/appmap.yml
[2025-04-28 05:33:37,814] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-04-28 05:33:37,815] INFO _appmap.configuration: env: environ({'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'PYTHONUNBUFFERED': '1', 'DB_REPLICA_USER': 'stockone', 'HOSTNAME': '138c3f1451dd', 'PYTHON_VERSION': '3.12.10', 'DB_REPORTS_USER': 'stockone', 'DB_REPLICA_HOST': 'db1', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'PWD': '/application', 'USE_S3': 'TRUE', 'DB_USER': 'stockone', 'HOME': '/root', 'LANG': 'C.UTF-8', 'DB_REPLICA_NAME': 'stockone', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'TERM': 'xterm', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_HOST': 'db1', 'AWS_ID': '********************', 'SHLVL': '1', 'DB_REPORTS_NAME': 'stockone', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DB_NAME': 'stockone', 'TASK_USAGE': '0', 'DB_REPORTS_PASS': 'stockone^123', 'DB_REPORTS_HOST': 'db1', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'DB_REPLICA_PASS': 'stockone^123', 'AWS_S3_REGION_NAME': 'ap-south-1', 'DEBUG': '1', 'DB_PASS': 'stockone^123', 'CONN_MAX_AGE': '300', 'ENTRYPOINT_FLAG': 'true', '_': '/usr/local/bin/python'})
[2025-04-28 05:33:37,820] INFO _appmap.env: appmap enabled: False
[2025-04-28 05:33:57,026] INFO _appmap.configuration: file: /application/appmap.yml
[2025-04-28 05:33:57,026] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-04-28 05:33:57,026] INFO _appmap.configuration: env: environ({'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'PYTHONUNBUFFERED': '1', 'DB_REPLICA_USER': 'stockone', 'HOSTNAME': '138c3f1451dd', 'PYTHON_VERSION': '3.12.10', 'DB_REPORTS_USER': 'stockone', 'DB_REPLICA_HOST': 'db1', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'PWD': '/application', 'USE_S3': 'TRUE', 'DB_USER': 'stockone', 'HOME': '/root', 'LANG': 'C.UTF-8', 'DB_REPLICA_NAME': 'stockone', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'TERM': 'xterm', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_HOST': 'db1', 'AWS_ID': '********************', 'SHLVL': '1', 'DB_REPORTS_NAME': 'stockone', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DB_NAME': 'stockone', 'TASK_USAGE': '0', 'DB_REPORTS_PASS': 'stockone^123', 'DB_REPORTS_HOST': 'db1', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'DB_REPLICA_PASS': 'stockone^123', 'AWS_S3_REGION_NAME': 'ap-south-1', 'DEBUG': '1', 'DB_PASS': 'stockone^123', 'CONN_MAX_AGE': '300', 'ENTRYPOINT_FLAG': 'true', '_': '/usr/local/bin/python'})
[2025-04-28 05:33:57,031] INFO _appmap.env: appmap enabled: False
[2025-04-28 05:34:04,176] INFO _appmap.configuration: file: /application/appmap.yml
[2025-04-28 05:34:04,177] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-04-28 05:34:04,177] INFO _appmap.configuration: env: environ({'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'PYTHONUNBUFFERED': '1', 'DB_REPLICA_USER': 'stockone', 'HOSTNAME': '138c3f1451dd', 'PYTHON_VERSION': '3.12.10', 'DB_REPORTS_USER': 'stockone', 'DB_REPLICA_HOST': 'db1', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'PWD': '/application', 'USE_S3': 'TRUE', 'DB_USER': 'stockone', 'HOME': '/root', 'LANG': 'C.UTF-8', 'DB_REPLICA_NAME': 'stockone', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'TERM': 'xterm', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_HOST': 'db1', 'AWS_ID': '********************', 'SHLVL': '1', 'DB_REPORTS_NAME': 'stockone', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DB_NAME': 'stockone', 'TASK_USAGE': '0', 'DB_REPORTS_PASS': 'stockone^123', 'DB_REPORTS_HOST': 'db1', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'DB_REPLICA_PASS': 'stockone^123', 'AWS_S3_REGION_NAME': 'ap-south-1', 'DEBUG': '1', 'DB_PASS': 'stockone^123', 'CONN_MAX_AGE': '300', 'ENTRYPOINT_FLAG': 'true', '_': '/usr/local/bin/python'})
[2025-04-28 05:34:04,183] INFO _appmap.env: appmap enabled: False
[2025-04-28 06:24:22,394] INFO _appmap.configuration: file: /application/appmap.yml
[2025-04-28 06:24:22,394] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-04-28 06:24:22,394] INFO _appmap.configuration: env: environ({'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'PYTHONUNBUFFERED': '1', 'DB_REPLICA_USER': 'stockone', 'HOSTNAME': '138c3f1451dd', 'PYTHON_VERSION': '3.12.10', 'DB_REPORTS_USER': 'stockone', 'DB_REPLICA_HOST': 'db1', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'PWD': '/application', 'USE_S3': 'TRUE', 'DB_USER': 'stockone', 'HOME': '/root', 'LANG': 'C.UTF-8', 'DB_REPLICA_NAME': 'stockone', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'TERM': 'xterm', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_HOST': 'db1', 'AWS_ID': '********************', 'SHLVL': '1', 'DB_REPORTS_NAME': 'stockone', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DB_NAME': 'stockone', 'TASK_USAGE': '0', 'DB_REPORTS_PASS': 'stockone^123', 'DB_REPORTS_HOST': 'db1', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'DB_REPLICA_PASS': 'stockone^123', 'AWS_S3_REGION_NAME': 'ap-south-1', 'DEBUG': '1', 'DB_PASS': 'stockone^123', 'CONN_MAX_AGE': '300', 'ENTRYPOINT_FLAG': 'true', '_': '/usr/local/bin/python'})
[2025-04-28 06:24:22,409] INFO _appmap.env: appmap enabled: False
[2025-04-28 06:39:26,118] INFO _appmap.env: appmap enabled: False
[2025-04-28 11:35:12,860] INFO _appmap.configuration: file: /application/appmap.yml
[2025-04-28 11:35:12,861] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-04-28 11:35:12,861] INFO _appmap.configuration: env: environ({'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'PYTHONUNBUFFERED': '1', 'DB_REPLICA_USER': 'stockone', 'HOSTNAME': '138c3f1451dd', 'PYTHON_VERSION': '3.12.10', 'DB_REPORTS_USER': 'stockone', 'DB_REPLICA_HOST': 'db1', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'PWD': '/application', 'USE_S3': 'TRUE', 'DB_USER': 'stockone', 'HOME': '/root', 'LANG': 'C.UTF-8', 'DB_REPLICA_NAME': 'stockone', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'TERM': 'xterm', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_HOST': 'db1', 'AWS_ID': '********************', 'SHLVL': '1', 'DB_REPORTS_NAME': 'stockone', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DB_NAME': 'stockone', 'TASK_USAGE': '0', 'DB_REPORTS_PASS': 'stockone^123', 'DB_REPORTS_HOST': 'db1', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'DB_REPLICA_PASS': 'stockone^123', 'AWS_S3_REGION_NAME': 'ap-south-1', 'DEBUG': '1', 'DB_PASS': 'stockone^123', 'CONN_MAX_AGE': '300', 'ENTRYPOINT_FLAG': 'true', '_': '/usr/local/bin/python'})
[2025-04-28 11:35:12,886] INFO _appmap.env: appmap enabled: False
[2025-04-28 11:36:28,294] INFO _appmap.configuration: file: /application/appmap.yml
[2025-04-28 11:36:28,294] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-04-28 11:36:28,294] INFO _appmap.configuration: env: environ({'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'PYTHONUNBUFFERED': '1', 'DB_REPLICA_USER': 'stockone', 'HOSTNAME': '138c3f1451dd', 'PYTHON_VERSION': '3.12.10', 'DB_REPORTS_USER': 'stockone', 'DB_REPLICA_HOST': 'db1', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'PWD': '/application', 'USE_S3': 'TRUE', 'DB_USER': 'stockone', 'HOME': '/root', 'LANG': 'C.UTF-8', 'DB_REPLICA_NAME': 'stockone', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'TERM': 'xterm', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_HOST': 'db1', 'AWS_ID': '********************', 'SHLVL': '1', 'DB_REPORTS_NAME': 'stockone', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DB_NAME': 'stockone', 'TASK_USAGE': '0', 'DB_REPORTS_PASS': 'stockone^123', 'DB_REPORTS_HOST': 'db1', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'DB_REPLICA_PASS': 'stockone^123', 'AWS_S3_REGION_NAME': 'ap-south-1', 'DEBUG': '1', 'DB_PASS': 'stockone^123', 'CONN_MAX_AGE': '300', 'ENTRYPOINT_FLAG': 'true', '_': '/usr/local/bin/python'})
[2025-04-28 11:36:28,299] INFO _appmap.env: appmap enabled: False
[2025-04-28 11:37:22,146] INFO _appmap.env: appmap enabled: False
[2025-04-28 11:37:24,146] INFO _appmap.configuration: file: /application/appmap.yml
[2025-04-28 11:37:24,146] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-04-28 11:37:24,146] INFO _appmap.configuration: env: environ({'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'PYTHONUNBUFFERED': '1', 'DB_REPLICA_USER': 'stockone', 'HOSTNAME': '138c3f1451dd', 'PYTHON_VERSION': '3.12.10', 'DB_REPORTS_USER': 'stockone', 'DB_REPLICA_HOST': 'db1', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'PWD': '/application', 'USE_S3': 'TRUE', 'DB_USER': 'stockone', 'HOME': '/root', 'LANG': 'C.UTF-8', 'DB_REPLICA_NAME': 'stockone', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'TERM': 'xterm', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_HOST': 'db1', 'AWS_ID': '********************', 'SHLVL': '1', 'DB_REPORTS_NAME': 'stockone', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DB_NAME': 'stockone', 'TASK_USAGE': '0', 'DB_REPORTS_PASS': 'stockone^123', 'DB_REPORTS_HOST': 'db1', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'DB_REPLICA_PASS': 'stockone^123', 'AWS_S3_REGION_NAME': 'ap-south-1', 'DEBUG': '1', 'DB_PASS': 'stockone^123', 'CONN_MAX_AGE': '300', 'ENTRYPOINT_FLAG': 'true', '_': '/usr/local/bin/python'})
[2025-04-28 11:37:24,151] INFO _appmap.env: appmap enabled: False
[2025-04-28 11:39:09,954] INFO _appmap.env: appmap enabled: False
[2025-04-28 11:39:33,277] INFO _appmap.env: appmap enabled: False
[2025-04-28 11:40:12,217] INFO _appmap.env: appmap enabled: False
[2025-04-28 11:40:37,500] INFO _appmap.env: appmap enabled: False
[2025-04-28 11:41:32,030] INFO _appmap.env: appmap enabled: False
[2025-04-28 11:41:59,867] INFO _appmap.env: appmap enabled: False
[2025-04-28 11:45:56,899] INFO _appmap.env: appmap enabled: False
[2025-04-28 11:45:57,807] INFO _appmap.env: appmap enabled: False
[2025-04-28 11:46:04,583] INFO _appmap.env: appmap enabled: False
[2025-04-28 11:52:09,411] INFO _appmap.env: appmap enabled: False
[2025-04-28 11:52:59,752] INFO _appmap.env: appmap enabled: False
[2025-04-28 11:53:10,316] INFO _appmap.env: appmap enabled: False
[2025-04-28 11:59:08,778] INFO _appmap.configuration: file: /application/appmap.yml
[2025-04-28 11:59:08,779] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-04-28 11:59:08,779] INFO _appmap.configuration: env: environ({'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'PYTHONUNBUFFERED': '1', 'DB_REPLICA_USER': 'stockone', 'HOSTNAME': '138c3f1451dd', 'PYTHON_VERSION': '3.12.10', 'DB_REPORTS_USER': 'stockone', 'DB_REPLICA_HOST': 'db1', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'PWD': '/application', 'USE_S3': 'TRUE', 'DB_USER': 'stockone', 'HOME': '/root', 'LANG': 'C.UTF-8', 'DB_REPLICA_NAME': 'stockone', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'TERM': 'xterm', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_HOST': 'db1', 'AWS_ID': '********************', 'SHLVL': '1', 'DB_REPORTS_NAME': 'stockone', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DB_NAME': 'stockone', 'TASK_USAGE': '0', 'DB_REPORTS_PASS': 'stockone^123', 'DB_REPORTS_HOST': 'db1', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'DB_REPLICA_PASS': 'stockone^123', 'AWS_S3_REGION_NAME': 'ap-south-1', 'DEBUG': '1', 'DB_PASS': 'stockone^123', 'CONN_MAX_AGE': '300', 'ENTRYPOINT_FLAG': 'true', '_': '/usr/local/bin/python'})
[2025-04-28 11:59:08,790] INFO _appmap.env: appmap enabled: False
[2025-04-28 12:06:23,161] INFO _appmap.env: appmap enabled: False
[2025-04-28 12:06:42,603] INFO _appmap.env: appmap enabled: False
[2025-04-28 12:07:04,530] INFO _appmap.env: appmap enabled: False
[2025-04-28 12:13:11,665] INFO _appmap.env: appmap enabled: False
[2025-04-28 12:13:36,997] INFO _appmap.env: appmap enabled: False
[2025-04-28 12:13:51,415] INFO _appmap.env: appmap enabled: False
[2025-04-28 12:16:41,758] INFO _appmap.env: appmap enabled: False
[2025-04-28 12:17:15,402] INFO _appmap.env: appmap enabled: False
[2025-04-28 12:17:22,720] INFO _appmap.env: appmap enabled: False
[2025-04-28 12:17:33,339] INFO _appmap.env: appmap enabled: False
[2025-04-28 12:28:39,274] INFO _appmap.env: appmap enabled: False
[2025-04-28 12:28:47,946] INFO _appmap.env: appmap enabled: False
[2025-04-28 12:29:07,384] INFO _appmap.env: appmap enabled: False
[2025-04-28 12:29:45,477] INFO _appmap.env: appmap enabled: False
[2025-04-28 12:30:44,754] INFO _appmap.env: appmap enabled: False
[2025-04-28 12:31:23,317] INFO _appmap.env: appmap enabled: False
[2025-04-28 12:31:36,087] INFO _appmap.env: appmap enabled: False
[2025-04-28 12:31:56,310] INFO _appmap.env: appmap enabled: False
[2025-04-28 12:32:02,681] INFO _appmap.env: appmap enabled: False
[2025-04-28 12:32:33,890] INFO _appmap.env: appmap enabled: False
[2025-04-28 12:32:59,425] INFO _appmap.env: appmap enabled: False
[2025-04-28 12:34:31,107] INFO _appmap.env: appmap enabled: False
[2025-04-28 12:36:49,951] INFO _appmap.env: appmap enabled: False
[2025-04-28 12:37:03,445] INFO _appmap.configuration: file: /application/appmap.yml
[2025-04-28 12:37:03,445] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-04-28 12:37:03,445] INFO _appmap.configuration: env: environ({'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'PYTHONUNBUFFERED': '1', 'DB_REPLICA_USER': 'stockone', 'HOSTNAME': '138c3f1451dd', 'PYTHON_VERSION': '3.12.10', 'DB_REPORTS_USER': 'stockone', 'DB_REPLICA_HOST': 'db1', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'PWD': '/application', 'USE_S3': 'TRUE', 'DB_USER': 'stockone', 'HOME': '/root', 'LANG': 'C.UTF-8', 'DB_REPLICA_NAME': 'stockone', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'TERM': 'xterm', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_HOST': 'db1', 'AWS_ID': '********************', 'SHLVL': '1', 'DB_REPORTS_NAME': 'stockone', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DB_NAME': 'stockone', 'TASK_USAGE': '0', 'DB_REPORTS_PASS': 'stockone^123', 'DB_REPORTS_HOST': 'db1', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'DB_REPLICA_PASS': 'stockone^123', 'AWS_S3_REGION_NAME': 'ap-south-1', 'DEBUG': '1', 'DB_PASS': 'stockone^123', 'CONN_MAX_AGE': '300', 'ENTRYPOINT_FLAG': 'true', '_': '/usr/local/bin/python'})
[2025-04-28 12:37:03,449] INFO _appmap.env: appmap enabled: False
[2025-04-28 12:37:22,852] INFO _appmap.configuration: file: /application/appmap.yml
[2025-04-28 12:37:22,852] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-04-28 12:37:22,852] INFO _appmap.configuration: env: environ({'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'PYTHONUNBUFFERED': '1', 'DB_REPLICA_USER': 'stockone', 'HOSTNAME': '138c3f1451dd', 'PYTHON_VERSION': '3.12.10', 'DB_REPORTS_USER': 'stockone', 'DB_REPLICA_HOST': 'db1', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'PWD': '/application', 'USE_S3': 'TRUE', 'DB_USER': 'stockone', 'HOME': '/root', 'LANG': 'C.UTF-8', 'DB_REPLICA_NAME': 'stockone', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'TERM': 'xterm', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_HOST': 'db1', 'AWS_ID': '********************', 'SHLVL': '1', 'DB_REPORTS_NAME': 'stockone', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DB_NAME': 'stockone', 'TASK_USAGE': '0', 'DB_REPORTS_PASS': 'stockone^123', 'DB_REPORTS_HOST': 'db1', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'DB_REPLICA_PASS': 'stockone^123', 'AWS_S3_REGION_NAME': 'ap-south-1', 'DEBUG': '1', 'DB_PASS': 'stockone^123', 'CONN_MAX_AGE': '300', 'ENTRYPOINT_FLAG': 'true', '_': '/usr/local/bin/python'})
[2025-04-28 12:37:22,857] INFO _appmap.env: appmap enabled: False
[2025-04-28 12:38:07,072] INFO _appmap.env: appmap enabled: False
[2025-04-28 12:38:09,453] INFO _appmap.configuration: file: /application/appmap.yml
[2025-04-28 12:38:09,453] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-04-28 12:38:09,453] INFO _appmap.configuration: env: environ({'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'PYTHONUNBUFFERED': '1', 'DB_REPLICA_USER': 'stockone', 'HOSTNAME': '138c3f1451dd', 'PYTHON_VERSION': '3.12.10', 'DB_REPORTS_USER': 'stockone', 'DB_REPLICA_HOST': 'db1', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'PWD': '/application', 'USE_S3': 'TRUE', 'DB_USER': 'stockone', 'HOME': '/root', 'LANG': 'C.UTF-8', 'DB_REPLICA_NAME': 'stockone', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'TERM': 'xterm', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_HOST': 'db1', 'AWS_ID': '********************', 'SHLVL': '1', 'DB_REPORTS_NAME': 'stockone', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DB_NAME': 'stockone', 'TASK_USAGE': '0', 'DB_REPORTS_PASS': 'stockone^123', 'DB_REPORTS_HOST': 'db1', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'DB_REPLICA_PASS': 'stockone^123', 'AWS_S3_REGION_NAME': 'ap-south-1', 'DEBUG': '1', 'DB_PASS': 'stockone^123', 'CONN_MAX_AGE': '300', 'ENTRYPOINT_FLAG': 'true', '_': '/usr/local/bin/python'})
[2025-04-28 12:38:09,458] INFO _appmap.env: appmap enabled: False
[2025-04-28 12:38:16,096] INFO _appmap.configuration: file: /application/appmap.yml
[2025-04-28 12:38:16,096] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-04-28 12:38:16,097] INFO _appmap.configuration: env: environ({'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'PYTHONUNBUFFERED': '1', 'DB_REPLICA_USER': 'stockone', 'HOSTNAME': '138c3f1451dd', 'PYTHON_VERSION': '3.12.10', 'DB_REPORTS_USER': 'stockone', 'DB_REPLICA_HOST': 'db1', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'PWD': '/application', 'USE_S3': 'TRUE', 'DB_USER': 'stockone', 'HOME': '/root', 'LANG': 'C.UTF-8', 'DB_REPLICA_NAME': 'stockone', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'TERM': 'xterm', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_HOST': 'db1', 'AWS_ID': '********************', 'SHLVL': '1', 'DB_REPORTS_NAME': 'stockone', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DB_NAME': 'stockone', 'TASK_USAGE': '0', 'DB_REPORTS_PASS': 'stockone^123', 'DB_REPORTS_HOST': 'db1', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'DB_REPLICA_PASS': 'stockone^123', 'AWS_S3_REGION_NAME': 'ap-south-1', 'DEBUG': '1', 'DB_PASS': 'stockone^123', 'CONN_MAX_AGE': '300', 'ENTRYPOINT_FLAG': 'true', '_': '/usr/local/bin/python'})
[2025-04-28 12:38:16,121] INFO _appmap.env: appmap enabled: False
[2025-04-28 12:40:28,231] INFO _appmap.configuration: file: /application/appmap.yml
[2025-04-28 12:40:28,231] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-04-28 12:40:28,231] INFO _appmap.configuration: env: environ({'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'PYTHONUNBUFFERED': '1', 'DB_REPLICA_USER': 'stockone', 'HOSTNAME': '138c3f1451dd', 'PYTHON_VERSION': '3.12.10', 'DB_REPORTS_USER': 'stockone', 'DB_REPLICA_HOST': 'db1', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'PWD': '/application', 'USE_S3': 'TRUE', 'DB_USER': 'stockone', 'HOME': '/root', 'LANG': 'C.UTF-8', 'DB_REPLICA_NAME': 'stockone', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'TERM': 'xterm', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_HOST': 'db1', 'AWS_ID': '********************', 'SHLVL': '1', 'DB_REPORTS_NAME': 'stockone', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DB_NAME': 'stockone', 'TASK_USAGE': '0', 'DB_REPORTS_PASS': 'stockone^123', 'DB_REPORTS_HOST': 'db1', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'DB_REPLICA_PASS': 'stockone^123', 'AWS_S3_REGION_NAME': 'ap-south-1', 'DEBUG': '1', 'DB_PASS': 'stockone^123', 'CONN_MAX_AGE': '300', 'ENTRYPOINT_FLAG': 'true', '_': '/usr/local/bin/python'})
[2025-04-28 12:40:28,240] INFO _appmap.env: appmap enabled: False
[2025-04-28 12:40:38,699] INFO _appmap.env: appmap enabled: False
[2025-04-28 12:41:31,427] INFO _appmap.env: appmap enabled: False
[2025-04-28 12:42:14,577] INFO _appmap.env: appmap enabled: False
[2025-04-28 12:43:35,616] INFO _appmap.env: appmap enabled: False
[2025-04-28 12:44:30,697] INFO _appmap.env: appmap enabled: False
[2025-04-28 12:44:52,589] INFO _appmap.env: appmap enabled: False
[2025-04-28 12:45:20,818] INFO _appmap.env: appmap enabled: False
[2025-04-28 12:45:22,076] INFO _appmap.env: appmap enabled: False
[2025-04-28 12:45:23,117] INFO _appmap.env: appmap enabled: False
[2025-04-28 12:49:17,382] INFO _appmap.configuration: file: /application/appmap.yml
[2025-04-28 12:49:17,383] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-04-28 12:49:17,383] INFO _appmap.configuration: env: environ({'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'PYTHONUNBUFFERED': '1', 'DB_REPLICA_USER': 'stockone', 'HOSTNAME': '138c3f1451dd', 'PYTHON_VERSION': '3.12.10', 'DB_REPORTS_USER': 'stockone', 'DB_REPLICA_HOST': 'db1', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'PWD': '/application', 'USE_S3': 'TRUE', 'DB_USER': 'stockone', 'HOME': '/root', 'LANG': 'C.UTF-8', 'DB_REPLICA_NAME': 'stockone', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'TERM': 'xterm', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_HOST': 'db1', 'AWS_ID': '********************', 'SHLVL': '1', 'DB_REPORTS_NAME': 'stockone', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DB_NAME': 'stockone', 'TASK_USAGE': '0', 'DB_REPORTS_PASS': 'stockone^123', 'DB_REPORTS_HOST': 'db1', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'DB_REPLICA_PASS': 'stockone^123', 'AWS_S3_REGION_NAME': 'ap-south-1', 'DEBUG': '1', 'DB_PASS': 'stockone^123', 'CONN_MAX_AGE': '300', 'ENTRYPOINT_FLAG': 'true', '_': '/usr/local/bin/python'})
[2025-04-28 12:49:17,400] INFO _appmap.env: appmap enabled: False
[2025-04-28 12:50:03,388] INFO _appmap.env: appmap enabled: False
[2025-04-28 12:50:24,811] INFO _appmap.env: appmap enabled: False
[2025-04-28 12:51:14,778] INFO _appmap.env: appmap enabled: False
[2025-04-28 12:52:32,522] INFO _appmap.configuration: file: /application/appmap.yml
[2025-04-28 12:52:32,522] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-04-28 12:52:32,522] INFO _appmap.configuration: env: environ({'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'PYTHONUNBUFFERED': '1', 'DB_REPLICA_USER': 'stockone', 'HOSTNAME': '138c3f1451dd', 'PYTHON_VERSION': '3.12.10', 'DB_REPORTS_USER': 'stockone', 'DB_REPLICA_HOST': 'db1', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'PWD': '/application', 'USE_S3': 'TRUE', 'DB_USER': 'stockone', 'HOME': '/root', 'LANG': 'C.UTF-8', 'DB_REPLICA_NAME': 'stockone', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'TERM': 'xterm', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_HOST': 'db1', 'AWS_ID': '********************', 'SHLVL': '1', 'DB_REPORTS_NAME': 'stockone', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DB_NAME': 'stockone', 'TASK_USAGE': '0', 'DB_REPORTS_PASS': 'stockone^123', 'DB_REPORTS_HOST': 'db1', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'DB_REPLICA_PASS': 'stockone^123', 'AWS_S3_REGION_NAME': 'ap-south-1', 'DEBUG': '1', 'DB_PASS': 'stockone^123', 'CONN_MAX_AGE': '300', 'ENTRYPOINT_FLAG': 'true', '_': '/usr/local/bin/python'})
[2025-04-28 12:52:32,526] INFO _appmap.env: appmap enabled: False
[2025-04-28 12:52:44,354] INFO _appmap.configuration: file: /application/appmap.yml
[2025-04-28 12:52:44,354] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-04-28 12:52:44,354] INFO _appmap.configuration: env: environ({'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'PYTHONUNBUFFERED': '1', 'DB_REPLICA_USER': 'stockone', 'HOSTNAME': '138c3f1451dd', 'PYTHON_VERSION': '3.12.10', 'DB_REPORTS_USER': 'stockone', 'DB_REPLICA_HOST': 'db1', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'PWD': '/application', 'USE_S3': 'TRUE', 'DB_USER': 'stockone', 'HOME': '/root', 'LANG': 'C.UTF-8', 'DB_REPLICA_NAME': 'stockone', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'TERM': 'xterm', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_HOST': 'db1', 'AWS_ID': '********************', 'SHLVL': '1', 'DB_REPORTS_NAME': 'stockone', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DB_NAME': 'stockone', 'TASK_USAGE': '0', 'DB_REPORTS_PASS': 'stockone^123', 'DB_REPORTS_HOST': 'db1', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'DB_REPLICA_PASS': 'stockone^123', 'AWS_S3_REGION_NAME': 'ap-south-1', 'DEBUG': '1', 'DB_PASS': 'stockone^123', 'CONN_MAX_AGE': '300', 'ENTRYPOINT_FLAG': 'true', '_': '/usr/local/bin/python'})
[2025-04-28 12:52:44,359] INFO _appmap.env: appmap enabled: False
[2025-04-28 12:53:10,321] INFO _appmap.configuration: file: /application/appmap.yml
[2025-04-28 12:53:10,321] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-04-28 12:53:10,322] INFO _appmap.configuration: env: environ({'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'PYTHONUNBUFFERED': '1', 'DB_REPLICA_USER': 'stockone', 'HOSTNAME': '138c3f1451dd', 'PYTHON_VERSION': '3.12.10', 'DB_REPORTS_USER': 'stockone', 'DB_REPLICA_HOST': 'db1', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'PWD': '/application', 'USE_S3': 'TRUE', 'DB_USER': 'stockone', 'HOME': '/root', 'LANG': 'C.UTF-8', 'DB_REPLICA_NAME': 'stockone', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'TERM': 'xterm', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_HOST': 'db1', 'AWS_ID': '********************', 'SHLVL': '1', 'DB_REPORTS_NAME': 'stockone', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DB_NAME': 'stockone', 'TASK_USAGE': '0', 'DB_REPORTS_PASS': 'stockone^123', 'DB_REPORTS_HOST': 'db1', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'DB_REPLICA_PASS': 'stockone^123', 'AWS_S3_REGION_NAME': 'ap-south-1', 'DEBUG': '1', 'DB_PASS': 'stockone^123', 'CONN_MAX_AGE': '300', 'ENTRYPOINT_FLAG': 'true', '_': '/usr/local/bin/python'})
[2025-04-28 12:53:10,327] INFO _appmap.env: appmap enabled: False
[2025-04-28 12:56:44,449] INFO _appmap.configuration: file: /application/appmap.yml
[2025-04-28 12:56:44,450] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-04-28 12:56:44,450] INFO _appmap.configuration: env: environ({'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'PYTHONUNBUFFERED': '1', 'DB_REPLICA_USER': 'stockone', 'HOSTNAME': '138c3f1451dd', 'PYTHON_VERSION': '3.12.10', 'DB_REPORTS_USER': 'stockone', 'DB_REPLICA_HOST': 'db1', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'PWD': '/application', 'USE_S3': 'TRUE', 'DB_USER': 'stockone', 'HOME': '/root', 'LANG': 'C.UTF-8', 'DB_REPLICA_NAME': 'stockone', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'TERM': 'xterm', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_HOST': 'db1', 'AWS_ID': '********************', 'SHLVL': '1', 'DB_REPORTS_NAME': 'stockone', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DB_NAME': 'stockone', 'TASK_USAGE': '0', 'DB_REPORTS_PASS': 'stockone^123', 'DB_REPORTS_HOST': 'db1', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'DB_REPLICA_PASS': 'stockone^123', 'AWS_S3_REGION_NAME': 'ap-south-1', 'DEBUG': '1', 'DB_PASS': 'stockone^123', 'CONN_MAX_AGE': '300', 'ENTRYPOINT_FLAG': 'true', '_': '/usr/local/bin/python'})
[2025-04-28 12:56:44,454] INFO _appmap.env: appmap enabled: False
[2025-04-28 12:57:22,562] INFO _appmap.configuration: file: /application/appmap.yml
[2025-04-28 12:57:22,563] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-04-28 12:57:22,563] INFO _appmap.configuration: env: environ({'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'PYTHONUNBUFFERED': '1', 'DB_REPLICA_USER': 'stockone', 'HOSTNAME': '138c3f1451dd', 'PYTHON_VERSION': '3.12.10', 'DB_REPORTS_USER': 'stockone', 'DB_REPLICA_HOST': 'db1', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'PWD': '/application', 'USE_S3': 'TRUE', 'DB_USER': 'stockone', 'HOME': '/root', 'LANG': 'C.UTF-8', 'DB_REPLICA_NAME': 'stockone', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'TERM': 'xterm', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_HOST': 'db1', 'AWS_ID': '********************', 'SHLVL': '1', 'DB_REPORTS_NAME': 'stockone', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DB_NAME': 'stockone', 'TASK_USAGE': '0', 'DB_REPORTS_PASS': 'stockone^123', 'DB_REPORTS_HOST': 'db1', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'DB_REPLICA_PASS': 'stockone^123', 'AWS_S3_REGION_NAME': 'ap-south-1', 'DEBUG': '1', 'DB_PASS': 'stockone^123', 'CONN_MAX_AGE': '300', 'ENTRYPOINT_FLAG': 'true', '_': '/usr/local/bin/python'})
[2025-04-28 12:57:22,567] INFO _appmap.env: appmap enabled: False
[2025-04-28 12:57:55,040] INFO _appmap.env: appmap enabled: False
[2025-04-28 12:58:36,379] INFO _appmap.env: appmap enabled: False
[2025-04-28 12:59:30,123] INFO _appmap.env: appmap enabled: False
[2025-04-28 12:59:30,898] INFO _appmap.env: appmap enabled: False
[2025-04-28 12:59:49,112] INFO _appmap.configuration: file: /application/appmap.yml
[2025-04-28 12:59:49,113] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-04-28 12:59:49,113] INFO _appmap.configuration: env: environ({'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'PYTHONUNBUFFERED': '1', 'DB_REPLICA_USER': 'stockone', 'HOSTNAME': '138c3f1451dd', 'PYTHON_VERSION': '3.12.10', 'DB_REPORTS_USER': 'stockone', 'DB_REPLICA_HOST': 'db1', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'PWD': '/application', 'USE_S3': 'TRUE', 'DB_USER': 'stockone', 'HOME': '/root', 'LANG': 'C.UTF-8', 'DB_REPLICA_NAME': 'stockone', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'TERM': 'xterm', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_HOST': 'db1', 'AWS_ID': '********************', 'SHLVL': '1', 'DB_REPORTS_NAME': 'stockone', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DB_NAME': 'stockone', 'TASK_USAGE': '0', 'DB_REPORTS_PASS': 'stockone^123', 'DB_REPORTS_HOST': 'db1', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'DB_REPLICA_PASS': 'stockone^123', 'AWS_S3_REGION_NAME': 'ap-south-1', 'DEBUG': '1', 'DB_PASS': 'stockone^123', 'CONN_MAX_AGE': '300', 'ENTRYPOINT_FLAG': 'true', '_': '/usr/local/bin/python'})
[2025-04-28 12:59:49,117] INFO _appmap.env: appmap enabled: False
[2025-04-28 12:59:58,432] INFO _appmap.configuration: file: /application/appmap.yml
[2025-04-28 12:59:58,432] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-04-28 12:59:58,432] INFO _appmap.configuration: env: environ({'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'PYTHONUNBUFFERED': '1', 'DB_REPLICA_USER': 'stockone', 'HOSTNAME': '138c3f1451dd', 'PYTHON_VERSION': '3.12.10', 'DB_REPORTS_USER': 'stockone', 'DB_REPLICA_HOST': 'db1', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'PWD': '/application', 'USE_S3': 'TRUE', 'DB_USER': 'stockone', 'HOME': '/root', 'LANG': 'C.UTF-8', 'DB_REPLICA_NAME': 'stockone', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'TERM': 'xterm', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_HOST': 'db1', 'AWS_ID': '********************', 'SHLVL': '1', 'DB_REPORTS_NAME': 'stockone', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DB_NAME': 'stockone', 'TASK_USAGE': '0', 'DB_REPORTS_PASS': 'stockone^123', 'DB_REPORTS_HOST': 'db1', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'DB_REPLICA_PASS': 'stockone^123', 'AWS_S3_REGION_NAME': 'ap-south-1', 'DEBUG': '1', 'DB_PASS': 'stockone^123', 'CONN_MAX_AGE': '300', 'ENTRYPOINT_FLAG': 'true', '_': '/usr/local/bin/python'})
[2025-04-28 12:59:58,436] INFO _appmap.env: appmap enabled: False
[2025-04-28 13:21:57,510] INFO _appmap.configuration: file: /application/appmap.yml
[2025-04-28 13:21:57,512] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-04-28 13:21:57,512] INFO _appmap.configuration: env: environ({'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'PYTHONUNBUFFERED': '1', 'DB_REPLICA_USER': 'stockone', 'HOSTNAME': '138c3f1451dd', 'PYTHON_VERSION': '3.12.10', 'DB_REPORTS_USER': 'stockone', 'DB_REPLICA_HOST': 'db1', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'PWD': '/application', 'USE_S3': 'TRUE', 'DB_USER': 'stockone', 'HOME': '/root', 'LANG': 'C.UTF-8', 'DB_REPLICA_NAME': 'stockone', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'TERM': 'xterm', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_HOST': 'db1', 'AWS_ID': '********************', 'SHLVL': '1', 'DB_REPORTS_NAME': 'stockone', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DB_NAME': 'stockone', 'TASK_USAGE': '0', 'DB_REPORTS_PASS': 'stockone^123', 'DB_REPORTS_HOST': 'db1', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'DB_REPLICA_PASS': 'stockone^123', 'AWS_S3_REGION_NAME': 'ap-south-1', 'DEBUG': '1', 'DB_PASS': 'stockone^123', 'CONN_MAX_AGE': '300', 'ENTRYPOINT_FLAG': 'true', '_': '/usr/local/bin/python'})
[2025-04-28 13:21:57,532] INFO _appmap.env: appmap enabled: False
[2025-04-28 13:52:20,946] INFO _appmap.env: appmap enabled: False
[2025-04-29 06:27:29,161] INFO _appmap.configuration: file: /application/appmap.yml
[2025-04-29 06:27:29,162] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-04-29 06:27:29,162] INFO _appmap.configuration: env: environ({'HOSTNAME': 'fcafacc790e2', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DEBUG': '1', 'HOME': '/root', 'DB_NAME': 'stockone', 'PYTHONUNBUFFERED': '1', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'CONN_MAX_AGE': '300', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'DB_REPLICA_PASS': 'stockone^123', 'TASK_USAGE': '0', 'ENTRYPOINT_FLAG': 'true', 'AWS_S3_REGION_NAME': 'ap-south-1', 'TERM': 'xterm', 'DB_REPLICA_HOST': 'db1', 'DB_REPORTS_PASS': 'stockone^123', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'DB_REPLICA_USER': 'stockone', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'LANG': 'C.UTF-8', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_REPORTS_HOST': 'db1', 'DB_REPORTS_USER': 'stockone', 'PYTHON_VERSION': '3.12.10', 'DB_PASS': 'stockone^123', 'DB_REPLICA_NAME': 'stockone', 'AWS_ID': '********************', 'USE_S3': 'TRUE', 'PWD': '/application', 'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'DB_HOST': 'db1', 'DB_REPORTS_NAME': 'stockone', 'DB_USER': 'stockone'})
[2025-04-29 06:27:29,205] INFO _appmap.env: appmap enabled: False
[2025-04-29 06:27:39,967] INFO _appmap.configuration: file: /application/appmap.yml
[2025-04-29 06:27:39,968] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-04-29 06:27:39,968] INFO _appmap.configuration: env: environ({'HOSTNAME': 'fcafacc790e2', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DEBUG': '1', 'HOME': '/root', 'DB_NAME': 'stockone', 'PYTHONUNBUFFERED': '1', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'CONN_MAX_AGE': '300', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'DB_REPLICA_PASS': 'stockone^123', 'TASK_USAGE': '0', 'ENTRYPOINT_FLAG': 'true', 'AWS_S3_REGION_NAME': 'ap-south-1', 'TERM': 'xterm', 'DB_REPLICA_HOST': 'db1', 'DB_REPORTS_PASS': 'stockone^123', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'DB_REPLICA_USER': 'stockone', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'LANG': 'C.UTF-8', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_REPORTS_HOST': 'db1', 'DB_REPORTS_USER': 'stockone', 'PYTHON_VERSION': '3.12.10', 'DB_PASS': 'stockone^123', 'DB_REPLICA_NAME': 'stockone', 'AWS_ID': '********************', 'USE_S3': 'TRUE', 'PWD': '/application', 'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'DB_HOST': 'db1', 'DB_REPORTS_NAME': 'stockone', 'DB_USER': 'stockone'})
[2025-04-29 06:27:39,972] INFO _appmap.env: appmap enabled: False
[2025-04-29 06:27:43,826] INFO _appmap.configuration: file: /application/appmap.yml
[2025-04-29 06:27:43,826] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-04-29 06:27:43,826] INFO _appmap.configuration: env: environ({'HOSTNAME': 'fcafacc790e2', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DEBUG': '1', 'HOME': '/root', 'DB_NAME': 'stockone', 'PYTHONUNBUFFERED': '1', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'CONN_MAX_AGE': '300', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'DB_REPLICA_PASS': 'stockone^123', 'TASK_USAGE': '0', 'ENTRYPOINT_FLAG': 'true', 'AWS_S3_REGION_NAME': 'ap-south-1', 'TERM': 'xterm', 'DB_REPLICA_HOST': 'db1', 'DB_REPORTS_PASS': 'stockone^123', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'DB_REPLICA_USER': 'stockone', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'LANG': 'C.UTF-8', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_REPORTS_HOST': 'db1', 'DB_REPORTS_USER': 'stockone', 'PYTHON_VERSION': '3.12.10', 'DB_PASS': 'stockone^123', 'DB_REPLICA_NAME': 'stockone', 'AWS_ID': '********************', 'USE_S3': 'TRUE', 'PWD': '/application', 'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'DB_HOST': 'db1', 'DB_REPORTS_NAME': 'stockone', 'DB_USER': 'stockone'})
[2025-04-29 06:27:43,830] INFO _appmap.env: appmap enabled: False
[2025-04-29 06:27:47,497] INFO _appmap.configuration: file: /application/appmap.yml
[2025-04-29 06:27:47,498] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-04-29 06:27:47,498] INFO _appmap.configuration: env: environ({'HOSTNAME': 'fcafacc790e2', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DEBUG': '1', 'HOME': '/root', 'DB_NAME': 'stockone', 'PYTHONUNBUFFERED': '1', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'CONN_MAX_AGE': '300', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'DB_REPLICA_PASS': 'stockone^123', 'TASK_USAGE': '0', 'ENTRYPOINT_FLAG': 'true', 'AWS_S3_REGION_NAME': 'ap-south-1', 'TERM': 'xterm', 'DB_REPLICA_HOST': 'db1', 'DB_REPORTS_PASS': 'stockone^123', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'DB_REPLICA_USER': 'stockone', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'LANG': 'C.UTF-8', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_REPORTS_HOST': 'db1', 'DB_REPORTS_USER': 'stockone', 'PYTHON_VERSION': '3.12.10', 'DB_PASS': 'stockone^123', 'DB_REPLICA_NAME': 'stockone', 'AWS_ID': '********************', 'USE_S3': 'TRUE', 'PWD': '/application', 'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'DB_HOST': 'db1', 'DB_REPORTS_NAME': 'stockone', 'DB_USER': 'stockone'})
[2025-04-29 06:27:47,502] INFO _appmap.env: appmap enabled: False
[2025-04-29 06:27:52,516] INFO _appmap.configuration: file: /application/appmap.yml
[2025-04-29 06:27:52,517] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-04-29 06:27:52,517] INFO _appmap.configuration: env: environ({'HOSTNAME': 'fcafacc790e2', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DEBUG': '1', 'HOME': '/root', 'DB_NAME': 'stockone', 'PYTHONUNBUFFERED': '1', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'CONN_MAX_AGE': '300', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'DB_REPLICA_PASS': 'stockone^123', 'TASK_USAGE': '0', 'ENTRYPOINT_FLAG': 'true', 'AWS_S3_REGION_NAME': 'ap-south-1', 'TERM': 'xterm', 'DB_REPLICA_HOST': 'db1', 'DB_REPORTS_PASS': 'stockone^123', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'DB_REPLICA_USER': 'stockone', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'LANG': 'C.UTF-8', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_REPORTS_HOST': 'db1', 'DB_REPORTS_USER': 'stockone', 'PYTHON_VERSION': '3.12.10', 'DB_PASS': 'stockone^123', 'DB_REPLICA_NAME': 'stockone', 'AWS_ID': '********************', 'USE_S3': 'TRUE', 'PWD': '/application', 'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'DB_HOST': 'db1', 'DB_REPORTS_NAME': 'stockone', 'DB_USER': 'stockone'})
[2025-04-29 06:27:52,521] INFO _appmap.env: appmap enabled: False
[2025-04-29 06:27:52,612] INFO _appmap.env: appmap enabled: False
[2025-04-29 06:27:52,677] INFO _appmap.env: appmap enabled: False
[2025-04-29 06:35:43,172] INFO _appmap.configuration: file: /application/appmap.yml
[2025-04-29 06:35:43,172] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-04-29 06:35:43,173] INFO _appmap.configuration: env: environ({'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'PYTHONUNBUFFERED': '1', 'DB_REPLICA_USER': 'stockone', 'HOSTNAME': 'fcafacc790e2', 'PYTHON_VERSION': '3.12.10', 'DB_REPORTS_USER': 'stockone', 'DB_REPLICA_HOST': 'db1', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'PWD': '/application', 'USE_S3': 'TRUE', 'DB_USER': 'stockone', 'HOME': '/root', 'LANG': 'C.UTF-8', 'DB_REPLICA_NAME': 'stockone', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'TERM': 'xterm', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_HOST': 'db1', 'AWS_ID': '********************', 'SHLVL': '1', 'DB_REPORTS_NAME': 'stockone', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DB_NAME': 'stockone', 'TASK_USAGE': '0', 'DB_REPORTS_PASS': 'stockone^123', 'DB_REPORTS_HOST': 'db1', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'DB_REPLICA_PASS': 'stockone^123', 'AWS_S3_REGION_NAME': 'ap-south-1', 'DEBUG': '1', 'DB_PASS': 'stockone^123', 'ENTRYPOINT_FLAG': 'true', 'CONN_MAX_AGE': '300', '_': '/usr/local/bin/python'})
[2025-04-29 06:35:43,190] INFO _appmap.env: appmap enabled: False
[2025-04-29 09:03:20,559] INFO _appmap.env: appmap enabled: False
[2025-04-29 09:03:51,336] INFO _appmap.env: appmap enabled: False
[2025-04-29 09:03:56,220] INFO _appmap.env: appmap enabled: False
[2025-04-29 09:04:01,894] INFO _appmap.env: appmap enabled: False
[2025-04-29 09:28:19,435] INFO _appmap.env: appmap enabled: False
[2025-04-29 09:29:09,977] INFO _appmap.configuration: file: /application/appmap.yml
[2025-04-29 09:29:09,980] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-04-29 09:29:09,980] INFO _appmap.configuration: env: environ({'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'PYTHONUNBUFFERED': '1', 'DB_REPLICA_USER': 'stockone', 'HOSTNAME': 'fcafacc790e2', 'PYTHON_VERSION': '3.12.10', 'DB_REPORTS_USER': 'stockone', 'DB_REPLICA_HOST': 'db1', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'PWD': '/application', 'USE_S3': 'TRUE', 'DB_USER': 'stockone', 'HOME': '/root', 'LANG': 'C.UTF-8', 'DB_REPLICA_NAME': 'stockone', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'TERM': 'xterm', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_HOST': 'db1', 'AWS_ID': '********************', 'SHLVL': '1', 'DB_REPORTS_NAME': 'stockone', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DB_NAME': 'stockone', 'TASK_USAGE': '0', 'DB_REPORTS_PASS': 'stockone^123', 'DB_REPORTS_HOST': 'db1', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'DB_REPLICA_PASS': 'stockone^123', 'AWS_S3_REGION_NAME': 'ap-south-1', 'DEBUG': '1', 'DB_PASS': 'stockone^123', 'ENTRYPOINT_FLAG': 'true', 'CONN_MAX_AGE': '300', '_': '/usr/local/bin/python'})
[2025-04-29 09:29:10,006] INFO _appmap.env: appmap enabled: False
[2025-04-29 09:49:05,240] INFO _appmap.env: appmap enabled: False
[2025-04-29 09:51:19,507] INFO _appmap.env: appmap enabled: False
[2025-04-29 09:53:51,163] INFO _appmap.env: appmap enabled: False
[2025-04-29 09:57:16,530] INFO _appmap.env: appmap enabled: False
[2025-04-29 09:59:19,719] INFO _appmap.env: appmap enabled: False
[2025-04-29 10:03:43,712] INFO _appmap.env: appmap enabled: False
[2025-04-29 10:04:08,028] INFO _appmap.env: appmap enabled: False
[2025-04-29 10:05:12,057] INFO _appmap.env: appmap enabled: False
[2025-04-29 11:01:51,117] INFO _appmap.env: appmap enabled: False
[2025-04-29 11:05:08,478] INFO _appmap.env: appmap enabled: False
[2025-04-29 11:05:46,787] INFO _appmap.configuration: file: /application/appmap.yml
[2025-04-29 11:05:46,787] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-04-29 11:05:46,787] INFO _appmap.configuration: env: environ({'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'PYTHONUNBUFFERED': '1', 'DB_REPLICA_USER': 'stockone', 'HOSTNAME': 'fcafacc790e2', 'PYTHON_VERSION': '3.12.10', 'DB_REPORTS_USER': 'stockone', 'DB_REPLICA_HOST': 'db1', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'PWD': '/application', 'USE_S3': 'TRUE', 'DB_USER': 'stockone', 'HOME': '/root', 'LANG': 'C.UTF-8', 'DB_REPLICA_NAME': 'stockone', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'TERM': 'xterm', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_HOST': 'db1', 'AWS_ID': '********************', 'SHLVL': '1', 'DB_REPORTS_NAME': 'stockone', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DB_NAME': 'stockone', 'TASK_USAGE': '0', 'DB_REPORTS_PASS': 'stockone^123', 'DB_REPORTS_HOST': 'db1', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'DB_REPLICA_PASS': 'stockone^123', 'AWS_S3_REGION_NAME': 'ap-south-1', 'DEBUG': '1', 'DB_PASS': 'stockone^123', 'ENTRYPOINT_FLAG': 'true', 'CONN_MAX_AGE': '300', '_': '/usr/local/bin/python'})
[2025-04-29 11:05:46,801] INFO _appmap.env: appmap enabled: False
[2025-04-29 11:06:06,877] INFO _appmap.configuration: file: /application/appmap.yml
[2025-04-29 11:06:06,877] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-04-29 11:06:06,877] INFO _appmap.configuration: env: environ({'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'PYTHONUNBUFFERED': '1', 'DB_REPLICA_USER': 'stockone', 'HOSTNAME': 'fcafacc790e2', 'PYTHON_VERSION': '3.12.10', 'DB_REPORTS_USER': 'stockone', 'DB_REPLICA_HOST': 'db1', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'PWD': '/application', 'USE_S3': 'TRUE', 'DB_USER': 'stockone', 'HOME': '/root', 'LANG': 'C.UTF-8', 'DB_REPLICA_NAME': 'stockone', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'TERM': 'xterm', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_HOST': 'db1', 'AWS_ID': '********************', 'SHLVL': '1', 'DB_REPORTS_NAME': 'stockone', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DB_NAME': 'stockone', 'TASK_USAGE': '0', 'DB_REPORTS_PASS': 'stockone^123', 'DB_REPORTS_HOST': 'db1', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'DB_REPLICA_PASS': 'stockone^123', 'AWS_S3_REGION_NAME': 'ap-south-1', 'DEBUG': '1', 'DB_PASS': 'stockone^123', 'ENTRYPOINT_FLAG': 'true', 'CONN_MAX_AGE': '300', '_': '/usr/local/bin/python'})
[2025-04-29 11:06:06,882] INFO _appmap.env: appmap enabled: False
[2025-04-29 11:10:05,167] INFO _appmap.env: appmap enabled: False
[2025-04-29 11:11:01,373] INFO _appmap.configuration: file: /application/appmap.yml
[2025-04-29 11:11:01,374] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-04-29 11:11:01,374] INFO _appmap.configuration: env: environ({'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'PYTHONUNBUFFERED': '1', 'DB_REPLICA_USER': 'stockone', 'HOSTNAME': 'fcafacc790e2', 'PYTHON_VERSION': '3.12.10', 'DB_REPORTS_USER': 'stockone', 'DB_REPLICA_HOST': 'db1', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'PWD': '/application', 'USE_S3': 'TRUE', 'DB_USER': 'stockone', 'HOME': '/root', 'LANG': 'C.UTF-8', 'DB_REPLICA_NAME': 'stockone', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'TERM': 'xterm', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_HOST': 'db1', 'AWS_ID': '********************', 'SHLVL': '1', 'DB_REPORTS_NAME': 'stockone', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DB_NAME': 'stockone', 'TASK_USAGE': '0', 'DB_REPORTS_PASS': 'stockone^123', 'DB_REPORTS_HOST': 'db1', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'DB_REPLICA_PASS': 'stockone^123', 'AWS_S3_REGION_NAME': 'ap-south-1', 'DEBUG': '1', 'DB_PASS': 'stockone^123', 'ENTRYPOINT_FLAG': 'true', 'CONN_MAX_AGE': '300', '_': '/usr/local/bin/python'})
[2025-04-29 11:11:01,381] INFO _appmap.env: appmap enabled: False
[2025-04-29 11:17:06,511] INFO _appmap.env: appmap enabled: False
[2025-04-29 11:17:12,693] INFO _appmap.configuration: file: /application/appmap.yml
[2025-04-29 11:17:12,693] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-04-29 11:17:12,693] INFO _appmap.configuration: env: environ({'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'PYTHONUNBUFFERED': '1', 'DB_REPLICA_USER': 'stockone', 'HOSTNAME': 'fcafacc790e2', 'PYTHON_VERSION': '3.12.10', 'DB_REPORTS_USER': 'stockone', 'DB_REPLICA_HOST': 'db1', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'PWD': '/application', 'USE_S3': 'TRUE', 'DB_USER': 'stockone', 'HOME': '/root', 'LANG': 'C.UTF-8', 'DB_REPLICA_NAME': 'stockone', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'TERM': 'xterm', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_HOST': 'db1', 'AWS_ID': '********************', 'SHLVL': '1', 'DB_REPORTS_NAME': 'stockone', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DB_NAME': 'stockone', 'TASK_USAGE': '0', 'DB_REPORTS_PASS': 'stockone^123', 'DB_REPORTS_HOST': 'db1', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'DB_REPLICA_PASS': 'stockone^123', 'AWS_S3_REGION_NAME': 'ap-south-1', 'DEBUG': '1', 'DB_PASS': 'stockone^123', 'ENTRYPOINT_FLAG': 'true', 'CONN_MAX_AGE': '300', '_': '/usr/local/bin/python'})
[2025-04-29 11:17:12,699] INFO _appmap.env: appmap enabled: False
[2025-04-29 11:17:53,162] INFO _appmap.env: appmap enabled: False
[2025-04-29 11:18:19,007] INFO _appmap.env: appmap enabled: False
[2025-04-29 11:38:40,540] INFO _appmap.env: appmap enabled: False
[2025-04-29 11:38:52,934] INFO _appmap.env: appmap enabled: False
[2025-04-29 11:39:12,589] INFO _appmap.env: appmap enabled: False
[2025-04-29 11:39:24,628] INFO _appmap.env: appmap enabled: False
[2025-04-29 11:39:48,059] INFO _appmap.env: appmap enabled: False
[2025-04-29 11:40:31,361] INFO _appmap.env: appmap enabled: False
[2025-04-29 11:40:57,661] INFO _appmap.env: appmap enabled: False
[2025-04-29 11:41:58,527] INFO _appmap.env: appmap enabled: False
[2025-04-29 11:42:14,244] INFO _appmap.env: appmap enabled: False
[2025-04-29 11:47:40,829] INFO _appmap.env: appmap enabled: False
[2025-04-29 11:47:45,727] INFO _appmap.env: appmap enabled: False
[2025-04-29 11:48:15,804] INFO _appmap.env: appmap enabled: False
[2025-04-29 11:48:35,171] INFO _appmap.env: appmap enabled: False
[2025-04-29 11:49:25,260] INFO _appmap.env: appmap enabled: False
[2025-04-29 11:50:10,188] INFO _appmap.env: appmap enabled: False
[2025-04-29 11:51:05,432] INFO _appmap.env: appmap enabled: False
[2025-04-29 11:55:43,586] INFO _appmap.env: appmap enabled: False
[2025-04-29 11:57:20,568] INFO _appmap.env: appmap enabled: False
[2025-04-29 11:57:42,468] INFO _appmap.env: appmap enabled: False
[2025-04-29 11:57:51,209] INFO _appmap.configuration: file: /application/appmap.yml
[2025-04-29 11:57:51,210] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-04-29 11:57:51,210] INFO _appmap.configuration: env: environ({'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'PYTHONUNBUFFERED': '1', 'DB_REPLICA_USER': 'stockone', 'HOSTNAME': 'fcafacc790e2', 'PYTHON_VERSION': '3.12.10', 'DB_REPORTS_USER': 'stockone', 'DB_REPLICA_HOST': 'db1', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'PWD': '/application', 'USE_S3': 'TRUE', 'DB_USER': 'stockone', 'HOME': '/root', 'LANG': 'C.UTF-8', 'DB_REPLICA_NAME': 'stockone', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'TERM': 'xterm', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_HOST': 'db1', 'AWS_ID': '********************', 'SHLVL': '1', 'DB_REPORTS_NAME': 'stockone', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DB_NAME': 'stockone', 'TASK_USAGE': '0', 'DB_REPORTS_PASS': 'stockone^123', 'DB_REPORTS_HOST': 'db1', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'DB_REPLICA_PASS': 'stockone^123', 'AWS_S3_REGION_NAME': 'ap-south-1', 'DEBUG': '1', 'DB_PASS': 'stockone^123', 'ENTRYPOINT_FLAG': 'true', 'CONN_MAX_AGE': '300', '_': '/usr/local/bin/python'})
[2025-04-29 11:57:51,215] INFO _appmap.env: appmap enabled: False
[2025-04-29 11:58:51,831] INFO _appmap.env: appmap enabled: False
[2025-04-29 11:59:27,930] INFO _appmap.env: appmap enabled: False
[2025-04-29 11:59:34,329] INFO _appmap.configuration: file: /application/appmap.yml
[2025-04-29 11:59:34,329] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-04-29 11:59:34,329] INFO _appmap.configuration: env: environ({'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'PYTHONUNBUFFERED': '1', 'DB_REPLICA_USER': 'stockone', 'HOSTNAME': 'fcafacc790e2', 'PYTHON_VERSION': '3.12.10', 'DB_REPORTS_USER': 'stockone', 'DB_REPLICA_HOST': 'db1', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'PWD': '/application', 'USE_S3': 'TRUE', 'DB_USER': 'stockone', 'HOME': '/root', 'LANG': 'C.UTF-8', 'DB_REPLICA_NAME': 'stockone', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'TERM': 'xterm', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_HOST': 'db1', 'AWS_ID': '********************', 'SHLVL': '1', 'DB_REPORTS_NAME': 'stockone', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DB_NAME': 'stockone', 'TASK_USAGE': '0', 'DB_REPORTS_PASS': 'stockone^123', 'DB_REPORTS_HOST': 'db1', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'DB_REPLICA_PASS': 'stockone^123', 'AWS_S3_REGION_NAME': 'ap-south-1', 'DEBUG': '1', 'DB_PASS': 'stockone^123', 'ENTRYPOINT_FLAG': 'true', 'CONN_MAX_AGE': '300', '_': '/usr/local/bin/python'})
[2025-04-29 11:59:34,333] INFO _appmap.env: appmap enabled: False
[2025-04-29 11:59:57,072] INFO _appmap.env: appmap enabled: False
[2025-04-29 11:59:58,839] INFO _appmap.configuration: file: /application/appmap.yml
[2025-04-29 11:59:58,840] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-04-29 11:59:58,840] INFO _appmap.configuration: env: environ({'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'PYTHONUNBUFFERED': '1', 'DB_REPLICA_USER': 'stockone', 'HOSTNAME': 'fcafacc790e2', 'PYTHON_VERSION': '3.12.10', 'DB_REPORTS_USER': 'stockone', 'DB_REPLICA_HOST': 'db1', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'PWD': '/application', 'USE_S3': 'TRUE', 'DB_USER': 'stockone', 'HOME': '/root', 'LANG': 'C.UTF-8', 'DB_REPLICA_NAME': 'stockone', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'TERM': 'xterm', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_HOST': 'db1', 'AWS_ID': '********************', 'SHLVL': '1', 'DB_REPORTS_NAME': 'stockone', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DB_NAME': 'stockone', 'TASK_USAGE': '0', 'DB_REPORTS_PASS': 'stockone^123', 'DB_REPORTS_HOST': 'db1', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'DB_REPLICA_PASS': 'stockone^123', 'AWS_S3_REGION_NAME': 'ap-south-1', 'DEBUG': '1', 'DB_PASS': 'stockone^123', 'ENTRYPOINT_FLAG': 'true', 'CONN_MAX_AGE': '300', '_': '/usr/local/bin/python'})
[2025-04-29 11:59:58,844] INFO _appmap.env: appmap enabled: False
[2025-04-29 12:00:47,131] INFO _appmap.env: appmap enabled: False
[2025-04-29 12:00:47,859] INFO _appmap.configuration: file: /application/appmap.yml
[2025-04-29 12:00:47,860] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-04-29 12:00:47,861] INFO _appmap.configuration: env: environ({'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'PYTHONUNBUFFERED': '1', 'DB_REPLICA_USER': 'stockone', 'HOSTNAME': 'fcafacc790e2', 'PYTHON_VERSION': '3.12.10', 'DB_REPORTS_USER': 'stockone', 'DB_REPLICA_HOST': 'db1', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'PWD': '/application', 'USE_S3': 'TRUE', 'DB_USER': 'stockone', 'HOME': '/root', 'LANG': 'C.UTF-8', 'DB_REPLICA_NAME': 'stockone', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'TERM': 'xterm', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_HOST': 'db1', 'AWS_ID': '********************', 'SHLVL': '1', 'DB_REPORTS_NAME': 'stockone', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DB_NAME': 'stockone', 'TASK_USAGE': '0', 'DB_REPORTS_PASS': 'stockone^123', 'DB_REPORTS_HOST': 'db1', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'DB_REPLICA_PASS': 'stockone^123', 'AWS_S3_REGION_NAME': 'ap-south-1', 'DEBUG': '1', 'DB_PASS': 'stockone^123', 'ENTRYPOINT_FLAG': 'true', 'CONN_MAX_AGE': '300', '_': '/usr/local/bin/python'})
[2025-04-29 12:00:47,871] INFO _appmap.env: appmap enabled: False
[2025-04-29 12:01:14,126] INFO _appmap.env: appmap enabled: False
[2025-04-29 12:01:17,709] INFO _appmap.configuration: file: /application/appmap.yml
[2025-04-29 12:01:17,710] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-04-29 12:01:17,710] INFO _appmap.configuration: env: environ({'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'PYTHONUNBUFFERED': '1', 'DB_REPLICA_USER': 'stockone', 'HOSTNAME': 'fcafacc790e2', 'PYTHON_VERSION': '3.12.10', 'DB_REPORTS_USER': 'stockone', 'DB_REPLICA_HOST': 'db1', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'PWD': '/application', 'USE_S3': 'TRUE', 'DB_USER': 'stockone', 'HOME': '/root', 'LANG': 'C.UTF-8', 'DB_REPLICA_NAME': 'stockone', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'TERM': 'xterm', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_HOST': 'db1', 'AWS_ID': '********************', 'SHLVL': '1', 'DB_REPORTS_NAME': 'stockone', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DB_NAME': 'stockone', 'TASK_USAGE': '0', 'DB_REPORTS_PASS': 'stockone^123', 'DB_REPORTS_HOST': 'db1', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'DB_REPLICA_PASS': 'stockone^123', 'AWS_S3_REGION_NAME': 'ap-south-1', 'DEBUG': '1', 'DB_PASS': 'stockone^123', 'ENTRYPOINT_FLAG': 'true', 'CONN_MAX_AGE': '300', '_': '/usr/local/bin/python'})
[2025-04-29 12:01:17,715] INFO _appmap.env: appmap enabled: False
[2025-04-29 12:27:00,515] INFO _appmap.configuration: file: /application/appmap.yml
[2025-04-29 12:27:00,516] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-04-29 12:27:00,516] INFO _appmap.configuration: env: environ({'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'PYTHONUNBUFFERED': '1', 'DB_REPLICA_USER': 'stockone', 'HOSTNAME': 'fcafacc790e2', 'PYTHON_VERSION': '3.12.10', 'DB_REPORTS_USER': 'stockone', 'DB_REPLICA_HOST': 'db1', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'PWD': '/application', 'USE_S3': 'TRUE', 'DB_USER': 'stockone', 'HOME': '/root', 'LANG': 'C.UTF-8', 'DB_REPLICA_NAME': 'stockone', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'TERM': 'xterm', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_HOST': 'db1', 'AWS_ID': '********************', 'SHLVL': '1', 'DB_REPORTS_NAME': 'stockone', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DB_NAME': 'stockone', 'TASK_USAGE': '0', 'DB_REPORTS_PASS': 'stockone^123', 'DB_REPORTS_HOST': 'db1', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'DB_REPLICA_PASS': 'stockone^123', 'AWS_S3_REGION_NAME': 'ap-south-1', 'DEBUG': '1', 'DB_PASS': 'stockone^123', 'ENTRYPOINT_FLAG': 'true', 'CONN_MAX_AGE': '300', '_': '/usr/local/bin/python'})
[2025-04-29 12:27:00,530] INFO _appmap.env: appmap enabled: False
[2025-04-29 12:27:20,906] INFO _appmap.env: appmap enabled: False
[2025-04-29 12:27:27,372] INFO _appmap.env: appmap enabled: False
[2025-04-29 12:31:11,046] INFO _appmap.env: appmap enabled: False
[2025-04-29 12:32:42,047] INFO _appmap.configuration: file: /application/appmap.yml
[2025-04-29 12:32:42,048] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-04-29 12:32:42,048] INFO _appmap.configuration: env: environ({'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'PYTHONUNBUFFERED': '1', 'DB_REPLICA_USER': 'stockone', 'HOSTNAME': 'fcafacc790e2', 'PYTHON_VERSION': '3.12.10', 'DB_REPORTS_USER': 'stockone', 'DB_REPLICA_HOST': 'db1', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'PWD': '/application', 'USE_S3': 'TRUE', 'DB_USER': 'stockone', 'HOME': '/root', 'LANG': 'C.UTF-8', 'DB_REPLICA_NAME': 'stockone', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'TERM': 'xterm', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_HOST': 'db1', 'AWS_ID': '********************', 'SHLVL': '1', 'DB_REPORTS_NAME': 'stockone', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DB_NAME': 'stockone', 'TASK_USAGE': '0', 'DB_REPORTS_PASS': 'stockone^123', 'DB_REPORTS_HOST': 'db1', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'DB_REPLICA_PASS': 'stockone^123', 'AWS_S3_REGION_NAME': 'ap-south-1', 'DEBUG': '1', 'DB_PASS': 'stockone^123', 'ENTRYPOINT_FLAG': 'true', 'CONN_MAX_AGE': '300', '_': '/usr/local/bin/python'})
[2025-04-29 12:32:42,054] INFO _appmap.env: appmap enabled: False
[2025-04-29 12:32:59,811] INFO _appmap.configuration: file: /application/appmap.yml
[2025-04-29 12:32:59,811] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-04-29 12:32:59,811] INFO _appmap.configuration: env: environ({'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'PYTHONUNBUFFERED': '1', 'DB_REPLICA_USER': 'stockone', 'HOSTNAME': 'fcafacc790e2', 'PYTHON_VERSION': '3.12.10', 'DB_REPORTS_USER': 'stockone', 'DB_REPLICA_HOST': 'db1', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'PWD': '/application', 'USE_S3': 'TRUE', 'DB_USER': 'stockone', 'HOME': '/root', 'LANG': 'C.UTF-8', 'DB_REPLICA_NAME': 'stockone', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'TERM': 'xterm', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_HOST': 'db1', 'AWS_ID': '********************', 'SHLVL': '1', 'DB_REPORTS_NAME': 'stockone', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DB_NAME': 'stockone', 'TASK_USAGE': '0', 'DB_REPORTS_PASS': 'stockone^123', 'DB_REPORTS_HOST': 'db1', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'DB_REPLICA_PASS': 'stockone^123', 'AWS_S3_REGION_NAME': 'ap-south-1', 'DEBUG': '1', 'DB_PASS': 'stockone^123', 'ENTRYPOINT_FLAG': 'true', 'CONN_MAX_AGE': '300', '_': '/usr/local/bin/python'})
[2025-04-29 12:32:59,816] INFO _appmap.env: appmap enabled: False
[2025-04-29 12:36:46,226] INFO _appmap.configuration: file: /application/appmap.yml
[2025-04-29 12:36:46,227] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-04-29 12:36:46,227] INFO _appmap.configuration: env: environ({'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'PYTHONUNBUFFERED': '1', 'DB_REPLICA_USER': 'stockone', 'HOSTNAME': 'fcafacc790e2', 'PYTHON_VERSION': '3.12.10', 'DB_REPORTS_USER': 'stockone', 'DB_REPLICA_HOST': 'db1', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'PWD': '/application', 'USE_S3': 'TRUE', 'DB_USER': 'stockone', 'HOME': '/root', 'LANG': 'C.UTF-8', 'DB_REPLICA_NAME': 'stockone', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'TERM': 'xterm', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_HOST': 'db1', 'AWS_ID': '********************', 'SHLVL': '1', 'DB_REPORTS_NAME': 'stockone', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DB_NAME': 'stockone', 'TASK_USAGE': '0', 'DB_REPORTS_PASS': 'stockone^123', 'DB_REPORTS_HOST': 'db1', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'DB_REPLICA_PASS': 'stockone^123', 'AWS_S3_REGION_NAME': 'ap-south-1', 'DEBUG': '1', 'DB_PASS': 'stockone^123', 'ENTRYPOINT_FLAG': 'true', 'CONN_MAX_AGE': '300', '_': '/usr/local/bin/python'})
[2025-04-29 12:36:46,234] INFO _appmap.env: appmap enabled: False
[2025-04-29 12:36:57,541] INFO _appmap.configuration: file: /application/appmap.yml
[2025-04-29 12:36:57,541] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-04-29 12:36:57,541] INFO _appmap.configuration: env: environ({'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'PYTHONUNBUFFERED': '1', 'DB_REPLICA_USER': 'stockone', 'HOSTNAME': 'fcafacc790e2', 'PYTHON_VERSION': '3.12.10', 'DB_REPORTS_USER': 'stockone', 'DB_REPLICA_HOST': 'db1', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'PWD': '/application', 'USE_S3': 'TRUE', 'DB_USER': 'stockone', 'HOME': '/root', 'LANG': 'C.UTF-8', 'DB_REPLICA_NAME': 'stockone', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'TERM': 'xterm', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_HOST': 'db1', 'AWS_ID': '********************', 'SHLVL': '1', 'DB_REPORTS_NAME': 'stockone', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DB_NAME': 'stockone', 'TASK_USAGE': '0', 'DB_REPORTS_PASS': 'stockone^123', 'DB_REPORTS_HOST': 'db1', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'DB_REPLICA_PASS': 'stockone^123', 'AWS_S3_REGION_NAME': 'ap-south-1', 'DEBUG': '1', 'DB_PASS': 'stockone^123', 'ENTRYPOINT_FLAG': 'true', 'CONN_MAX_AGE': '300', '_': '/usr/local/bin/python'})
[2025-04-29 12:36:57,546] INFO _appmap.env: appmap enabled: False
[2025-04-29 12:37:24,292] INFO _appmap.configuration: file: /application/appmap.yml
[2025-04-29 12:37:24,292] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-04-29 12:37:24,292] INFO _appmap.configuration: env: environ({'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'PYTHONUNBUFFERED': '1', 'DB_REPLICA_USER': 'stockone', 'HOSTNAME': 'fcafacc790e2', 'PYTHON_VERSION': '3.12.10', 'DB_REPORTS_USER': 'stockone', 'DB_REPLICA_HOST': 'db1', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'PWD': '/application', 'USE_S3': 'TRUE', 'DB_USER': 'stockone', 'HOME': '/root', 'LANG': 'C.UTF-8', 'DB_REPLICA_NAME': 'stockone', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'TERM': 'xterm', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_HOST': 'db1', 'AWS_ID': '********************', 'SHLVL': '1', 'DB_REPORTS_NAME': 'stockone', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DB_NAME': 'stockone', 'TASK_USAGE': '0', 'DB_REPORTS_PASS': 'stockone^123', 'DB_REPORTS_HOST': 'db1', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'DB_REPLICA_PASS': 'stockone^123', 'AWS_S3_REGION_NAME': 'ap-south-1', 'DEBUG': '1', 'DB_PASS': 'stockone^123', 'ENTRYPOINT_FLAG': 'true', 'CONN_MAX_AGE': '300', '_': '/usr/local/bin/python'})
[2025-04-29 12:37:24,295] INFO _appmap.env: appmap enabled: False
[2025-04-29 12:43:02,518] INFO _appmap.env: appmap enabled: False
[2025-04-29 12:43:38,890] INFO _appmap.env: appmap enabled: False
[2025-04-29 12:43:41,460] INFO _appmap.env: appmap enabled: False
[2025-04-29 12:43:45,259] INFO _appmap.configuration: file: /application/appmap.yml
[2025-04-29 12:43:45,259] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-04-29 12:43:45,259] INFO _appmap.configuration: env: environ({'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'PYTHONUNBUFFERED': '1', 'DB_REPLICA_USER': 'stockone', 'HOSTNAME': 'fcafacc790e2', 'PYTHON_VERSION': '3.12.10', 'DB_REPORTS_USER': 'stockone', 'DB_REPLICA_HOST': 'db1', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'PWD': '/application', 'USE_S3': 'TRUE', 'DB_USER': 'stockone', 'HOME': '/root', 'LANG': 'C.UTF-8', 'DB_REPLICA_NAME': 'stockone', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'TERM': 'xterm', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_HOST': 'db1', 'AWS_ID': '********************', 'SHLVL': '1', 'DB_REPORTS_NAME': 'stockone', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DB_NAME': 'stockone', 'TASK_USAGE': '0', 'DB_REPORTS_PASS': 'stockone^123', 'DB_REPORTS_HOST': 'db1', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'DB_REPLICA_PASS': 'stockone^123', 'AWS_S3_REGION_NAME': 'ap-south-1', 'DEBUG': '1', 'DB_PASS': 'stockone^123', 'ENTRYPOINT_FLAG': 'true', 'CONN_MAX_AGE': '300', '_': '/usr/local/bin/python'})
[2025-04-29 12:43:45,263] INFO _appmap.env: appmap enabled: False
[2025-04-29 12:44:01,728] INFO _appmap.env: appmap enabled: False
[2025-04-29 12:44:03,576] INFO _appmap.configuration: file: /application/appmap.yml
[2025-04-29 12:44:03,576] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-04-29 12:44:03,576] INFO _appmap.configuration: env: environ({'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'PYTHONUNBUFFERED': '1', 'DB_REPLICA_USER': 'stockone', 'HOSTNAME': 'fcafacc790e2', 'PYTHON_VERSION': '3.12.10', 'DB_REPORTS_USER': 'stockone', 'DB_REPLICA_HOST': 'db1', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'PWD': '/application', 'USE_S3': 'TRUE', 'DB_USER': 'stockone', 'HOME': '/root', 'LANG': 'C.UTF-8', 'DB_REPLICA_NAME': 'stockone', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'TERM': 'xterm', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_HOST': 'db1', 'AWS_ID': '********************', 'SHLVL': '1', 'DB_REPORTS_NAME': 'stockone', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DB_NAME': 'stockone', 'TASK_USAGE': '0', 'DB_REPORTS_PASS': 'stockone^123', 'DB_REPORTS_HOST': 'db1', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'DB_REPLICA_PASS': 'stockone^123', 'AWS_S3_REGION_NAME': 'ap-south-1', 'DEBUG': '1', 'DB_PASS': 'stockone^123', 'ENTRYPOINT_FLAG': 'true', 'CONN_MAX_AGE': '300', '_': '/usr/local/bin/python'})
[2025-04-29 12:44:03,581] INFO _appmap.env: appmap enabled: False
[2025-04-29 12:45:44,877] INFO _appmap.env: appmap enabled: False
[2025-04-29 12:45:46,691] INFO _appmap.configuration: file: /application/appmap.yml
[2025-04-29 12:45:46,691] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-04-29 12:45:46,691] INFO _appmap.configuration: env: environ({'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'PYTHONUNBUFFERED': '1', 'DB_REPLICA_USER': 'stockone', 'HOSTNAME': 'fcafacc790e2', 'PYTHON_VERSION': '3.12.10', 'DB_REPORTS_USER': 'stockone', 'DB_REPLICA_HOST': 'db1', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'PWD': '/application', 'USE_S3': 'TRUE', 'DB_USER': 'stockone', 'HOME': '/root', 'LANG': 'C.UTF-8', 'DB_REPLICA_NAME': 'stockone', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'TERM': 'xterm', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_HOST': 'db1', 'AWS_ID': '********************', 'SHLVL': '1', 'DB_REPORTS_NAME': 'stockone', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DB_NAME': 'stockone', 'TASK_USAGE': '0', 'DB_REPORTS_PASS': 'stockone^123', 'DB_REPORTS_HOST': 'db1', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'DB_REPLICA_PASS': 'stockone^123', 'AWS_S3_REGION_NAME': 'ap-south-1', 'DEBUG': '1', 'DB_PASS': 'stockone^123', 'ENTRYPOINT_FLAG': 'true', 'CONN_MAX_AGE': '300', '_': '/usr/local/bin/python'})
[2025-04-29 12:45:46,696] INFO _appmap.env: appmap enabled: False
[2025-04-29 12:47:24,203] INFO _appmap.configuration: file: /application/appmap.yml
[2025-04-29 12:47:24,204] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-04-29 12:47:24,204] INFO _appmap.configuration: env: environ({'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'PYTHONUNBUFFERED': '1', 'DB_REPLICA_USER': 'stockone', 'HOSTNAME': 'fcafacc790e2', 'PYTHON_VERSION': '3.12.10', 'DB_REPORTS_USER': 'stockone', 'DB_REPLICA_HOST': 'db1', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'PWD': '/application', 'USE_S3': 'TRUE', 'DB_USER': 'stockone', 'HOME': '/root', 'LANG': 'C.UTF-8', 'DB_REPLICA_NAME': 'stockone', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'TERM': 'xterm', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_HOST': 'db1', 'AWS_ID': '********************', 'SHLVL': '1', 'DB_REPORTS_NAME': 'stockone', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DB_NAME': 'stockone', 'TASK_USAGE': '0', 'DB_REPORTS_PASS': 'stockone^123', 'DB_REPORTS_HOST': 'db1', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'DB_REPLICA_PASS': 'stockone^123', 'AWS_S3_REGION_NAME': 'ap-south-1', 'DEBUG': '1', 'DB_PASS': 'stockone^123', 'ENTRYPOINT_FLAG': 'true', 'CONN_MAX_AGE': '300', '_': '/usr/local/bin/python'})
[2025-04-29 12:47:24,209] INFO _appmap.env: appmap enabled: False
[2025-04-29 12:49:01,026] INFO _appmap.env: appmap enabled: False
[2025-04-29 12:49:07,533] INFO _appmap.env: appmap enabled: False
[2025-04-29 12:49:46,874] INFO _appmap.configuration: file: /application/appmap.yml
[2025-04-29 12:49:46,874] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-04-29 12:49:46,874] INFO _appmap.configuration: env: environ({'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'PYTHONUNBUFFERED': '1', 'DB_REPLICA_USER': 'stockone', 'HOSTNAME': 'fcafacc790e2', 'PYTHON_VERSION': '3.12.10', 'DB_REPORTS_USER': 'stockone', 'DB_REPLICA_HOST': 'db1', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'PWD': '/application', 'USE_S3': 'TRUE', 'DB_USER': 'stockone', 'HOME': '/root', 'LANG': 'C.UTF-8', 'DB_REPLICA_NAME': 'stockone', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'TERM': 'xterm', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_HOST': 'db1', 'AWS_ID': '********************', 'SHLVL': '1', 'DB_REPORTS_NAME': 'stockone', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DB_NAME': 'stockone', 'TASK_USAGE': '0', 'DB_REPORTS_PASS': 'stockone^123', 'DB_REPORTS_HOST': 'db1', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'DB_REPLICA_PASS': 'stockone^123', 'AWS_S3_REGION_NAME': 'ap-south-1', 'DEBUG': '1', 'DB_PASS': 'stockone^123', 'ENTRYPOINT_FLAG': 'true', 'CONN_MAX_AGE': '300', '_': '/usr/local/bin/python'})
[2025-04-29 12:49:46,882] INFO _appmap.env: appmap enabled: False
[2025-04-29 12:53:03,152] INFO _appmap.configuration: file: /application/appmap.yml
[2025-04-29 12:53:03,152] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-04-29 12:53:03,152] INFO _appmap.configuration: env: environ({'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'PYTHONUNBUFFERED': '1', 'DB_REPLICA_USER': 'stockone', 'HOSTNAME': 'fcafacc790e2', 'PYTHON_VERSION': '3.12.10', 'DB_REPORTS_USER': 'stockone', 'DB_REPLICA_HOST': 'db1', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'PWD': '/application', 'USE_S3': 'TRUE', 'DB_USER': 'stockone', 'HOME': '/root', 'LANG': 'C.UTF-8', 'DB_REPLICA_NAME': 'stockone', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'TERM': 'xterm', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_HOST': 'db1', 'AWS_ID': '********************', 'SHLVL': '1', 'DB_REPORTS_NAME': 'stockone', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DB_NAME': 'stockone', 'TASK_USAGE': '0', 'DB_REPORTS_PASS': 'stockone^123', 'DB_REPORTS_HOST': 'db1', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'DB_REPLICA_PASS': 'stockone^123', 'AWS_S3_REGION_NAME': 'ap-south-1', 'DEBUG': '1', 'DB_PASS': 'stockone^123', 'ENTRYPOINT_FLAG': 'true', 'CONN_MAX_AGE': '300', '_': '/usr/local/bin/python'})
[2025-04-29 12:53:03,159] INFO _appmap.env: appmap enabled: False
[2025-04-29 12:55:53,019] INFO _appmap.env: appmap enabled: False
[2025-04-29 13:10:57,423] INFO _appmap.env: appmap enabled: False
[2025-04-29 13:11:01,060] INFO _appmap.env: appmap enabled: False
[2025-04-30 07:04:07,553] INFO _appmap.env: appmap enabled: False
[2025-04-30 07:04:39,552] INFO _appmap.env: appmap enabled: False
[2025-04-30 07:07:41,865] INFO _appmap.configuration: file: /application/appmap.yml
[2025-04-30 07:07:41,865] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-04-30 07:07:41,865] INFO _appmap.configuration: env: environ({'HOSTNAME': 'ab07f183acaa', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DEBUG': '1', 'HOME': '/root', 'DB_NAME': 'stockone', 'PYTHONUNBUFFERED': '1', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'CONN_MAX_AGE': '300', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'DB_REPLICA_PASS': 'stockone^123', 'TASK_USAGE': '0', 'ENTRYPOINT_FLAG': 'true', 'AWS_S3_REGION_NAME': 'ap-south-1', 'TERM': 'xterm', 'DB_REPLICA_HOST': 'db1', 'DB_REPLICA_USER': 'stockone', 'DB_REPORTS_PASS': 'stockone^123', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'LANG': 'C.UTF-8', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_REPORTS_HOST': 'db1', 'DB_REPORTS_USER': 'stockone', 'PYTHON_VERSION': '3.12.10', 'AWS_ID': '********************', 'DB_REPLICA_NAME': 'stockone', 'DB_PASS': 'stockone^123', 'USE_S3': 'TRUE', 'PWD': '/application', 'DB_HOST': 'db1', 'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'DB_REPORTS_NAME': 'stockone', 'DB_USER': 'stockone'})
[2025-04-30 07:07:41,871] INFO _appmap.env: appmap enabled: False
[2025-04-30 07:07:46,043] INFO _appmap.configuration: file: /application/appmap.yml
[2025-04-30 07:07:46,043] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-04-30 07:07:46,043] INFO _appmap.configuration: env: environ({'HOSTNAME': 'ab07f183acaa', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DEBUG': '1', 'HOME': '/root', 'DB_NAME': 'stockone', 'PYTHONUNBUFFERED': '1', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'CONN_MAX_AGE': '300', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'DB_REPLICA_PASS': 'stockone^123', 'TASK_USAGE': '0', 'ENTRYPOINT_FLAG': 'true', 'AWS_S3_REGION_NAME': 'ap-south-1', 'TERM': 'xterm', 'DB_REPLICA_HOST': 'db1', 'DB_REPLICA_USER': 'stockone', 'DB_REPORTS_PASS': 'stockone^123', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'LANG': 'C.UTF-8', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_REPORTS_HOST': 'db1', 'DB_REPORTS_USER': 'stockone', 'PYTHON_VERSION': '3.12.10', 'AWS_ID': '********************', 'DB_REPLICA_NAME': 'stockone', 'DB_PASS': 'stockone^123', 'USE_S3': 'TRUE', 'PWD': '/application', 'DB_HOST': 'db1', 'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'DB_REPORTS_NAME': 'stockone', 'DB_USER': 'stockone'})
[2025-04-30 07:07:46,047] INFO _appmap.env: appmap enabled: False
[2025-04-30 07:07:50,103] INFO _appmap.configuration: file: /application/appmap.yml
[2025-04-30 07:07:50,103] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-04-30 07:07:50,104] INFO _appmap.configuration: env: environ({'HOSTNAME': 'ab07f183acaa', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DEBUG': '1', 'HOME': '/root', 'DB_NAME': 'stockone', 'PYTHONUNBUFFERED': '1', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'CONN_MAX_AGE': '300', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'DB_REPLICA_PASS': 'stockone^123', 'TASK_USAGE': '0', 'ENTRYPOINT_FLAG': 'true', 'AWS_S3_REGION_NAME': 'ap-south-1', 'TERM': 'xterm', 'DB_REPLICA_HOST': 'db1', 'DB_REPLICA_USER': 'stockone', 'DB_REPORTS_PASS': 'stockone^123', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'LANG': 'C.UTF-8', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_REPORTS_HOST': 'db1', 'DB_REPORTS_USER': 'stockone', 'PYTHON_VERSION': '3.12.10', 'AWS_ID': '********************', 'DB_REPLICA_NAME': 'stockone', 'DB_PASS': 'stockone^123', 'USE_S3': 'TRUE', 'PWD': '/application', 'DB_HOST': 'db1', 'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'DB_REPORTS_NAME': 'stockone', 'DB_USER': 'stockone'})
[2025-04-30 07:07:50,107] INFO _appmap.env: appmap enabled: False
[2025-04-30 07:07:52,846] INFO _appmap.configuration: file: /application/appmap.yml
[2025-04-30 07:07:52,846] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-04-30 07:07:52,846] INFO _appmap.configuration: env: environ({'HOSTNAME': 'ab07f183acaa', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DEBUG': '1', 'HOME': '/root', 'DB_NAME': 'stockone', 'PYTHONUNBUFFERED': '1', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'CONN_MAX_AGE': '300', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'DB_REPLICA_PASS': 'stockone^123', 'TASK_USAGE': '0', 'ENTRYPOINT_FLAG': 'true', 'AWS_S3_REGION_NAME': 'ap-south-1', 'TERM': 'xterm', 'DB_REPLICA_HOST': 'db1', 'DB_REPLICA_USER': 'stockone', 'DB_REPORTS_PASS': 'stockone^123', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'LANG': 'C.UTF-8', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_REPORTS_HOST': 'db1', 'DB_REPORTS_USER': 'stockone', 'PYTHON_VERSION': '3.12.10', 'AWS_ID': '********************', 'DB_REPLICA_NAME': 'stockone', 'DB_PASS': 'stockone^123', 'USE_S3': 'TRUE', 'PWD': '/application', 'DB_HOST': 'db1', 'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'DB_REPORTS_NAME': 'stockone', 'DB_USER': 'stockone'})
[2025-04-30 07:07:52,849] INFO _appmap.env: appmap enabled: False
[2025-04-30 07:07:57,170] INFO _appmap.configuration: file: /application/appmap.yml
[2025-04-30 07:07:57,171] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-04-30 07:07:57,171] INFO _appmap.configuration: env: environ({'HOSTNAME': 'ab07f183acaa', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DEBUG': '1', 'HOME': '/root', 'DB_NAME': 'stockone', 'PYTHONUNBUFFERED': '1', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'CONN_MAX_AGE': '300', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'DB_REPLICA_PASS': 'stockone^123', 'TASK_USAGE': '0', 'ENTRYPOINT_FLAG': 'true', 'AWS_S3_REGION_NAME': 'ap-south-1', 'TERM': 'xterm', 'DB_REPLICA_HOST': 'db1', 'DB_REPLICA_USER': 'stockone', 'DB_REPORTS_PASS': 'stockone^123', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'LANG': 'C.UTF-8', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_REPORTS_HOST': 'db1', 'DB_REPORTS_USER': 'stockone', 'PYTHON_VERSION': '3.12.10', 'AWS_ID': '********************', 'DB_REPLICA_NAME': 'stockone', 'DB_PASS': 'stockone^123', 'USE_S3': 'TRUE', 'PWD': '/application', 'DB_HOST': 'db1', 'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'DB_REPORTS_NAME': 'stockone', 'DB_USER': 'stockone'})
[2025-04-30 07:07:57,174] INFO _appmap.env: appmap enabled: False
[2025-04-30 07:07:57,257] INFO _appmap.env: appmap enabled: False
[2025-04-30 07:07:57,267] INFO _appmap.env: appmap enabled: False
[2025-04-30 07:08:13,089] INFO _appmap.configuration: file: /application/appmap.yml
[2025-04-30 07:08:13,089] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-04-30 07:08:13,090] INFO _appmap.configuration: env: environ({'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'PYTHONUNBUFFERED': '1', 'DB_REPLICA_USER': 'stockone', 'HOSTNAME': 'ab07f183acaa', 'PYTHON_VERSION': '3.12.10', 'DB_REPORTS_USER': 'stockone', 'DB_REPLICA_HOST': 'db1', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'PWD': '/application', 'USE_S3': 'TRUE', 'DB_USER': 'stockone', 'HOME': '/root', 'LANG': 'C.UTF-8', 'DB_REPLICA_NAME': 'stockone', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'TERM': 'xterm', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_HOST': 'db1', 'AWS_ID': '********************', 'SHLVL': '1', 'DB_REPORTS_NAME': 'stockone', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DB_NAME': 'stockone', 'TASK_USAGE': '0', 'DB_REPORTS_PASS': 'stockone^123', 'DB_REPORTS_HOST': 'db1', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'DB_REPLICA_PASS': 'stockone^123', 'AWS_S3_REGION_NAME': 'ap-south-1', 'DEBUG': '1', 'DB_PASS': 'stockone^123', 'CONN_MAX_AGE': '300', 'ENTRYPOINT_FLAG': 'true', '_': '/usr/local/bin/python'})
[2025-04-30 07:08:13,096] INFO _appmap.env: appmap enabled: False
[2025-04-30 07:08:38,798] INFO _appmap.configuration: file: /application/appmap.yml
[2025-04-30 07:08:38,798] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-04-30 07:08:38,799] INFO _appmap.configuration: env: environ({'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'PYTHONUNBUFFERED': '1', 'DB_REPLICA_USER': 'stockone', 'HOSTNAME': 'ab07f183acaa', 'PYTHON_VERSION': '3.12.10', 'DB_REPORTS_USER': 'stockone', 'DB_REPLICA_HOST': 'db1', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'PWD': '/application', 'USE_S3': 'TRUE', 'DB_USER': 'stockone', 'HOME': '/root', 'LANG': 'C.UTF-8', 'DB_REPLICA_NAME': 'stockone', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'TERM': 'xterm', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_HOST': 'db1', 'AWS_ID': '********************', 'SHLVL': '1', 'DB_REPORTS_NAME': 'stockone', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DB_NAME': 'stockone', 'TASK_USAGE': '0', 'DB_REPORTS_PASS': 'stockone^123', 'DB_REPORTS_HOST': 'db1', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'DB_REPLICA_PASS': 'stockone^123', 'AWS_S3_REGION_NAME': 'ap-south-1', 'DEBUG': '1', 'DB_PASS': 'stockone^123', 'CONN_MAX_AGE': '300', 'ENTRYPOINT_FLAG': 'true', '_': '/usr/local/bin/python'})
[2025-04-30 07:08:38,803] INFO _appmap.env: appmap enabled: False
[2025-04-30 07:09:32,185] INFO _appmap.configuration: file: /application/appmap.yml
[2025-04-30 07:09:32,185] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-04-30 07:09:32,185] INFO _appmap.configuration: env: environ({'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'PYTHONUNBUFFERED': '1', 'DB_REPLICA_USER': 'stockone', 'HOSTNAME': 'ab07f183acaa', 'PYTHON_VERSION': '3.12.10', 'DB_REPORTS_USER': 'stockone', 'DB_REPLICA_HOST': 'db1', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'PWD': '/application', 'USE_S3': 'TRUE', 'DB_USER': 'stockone', 'HOME': '/root', 'LANG': 'C.UTF-8', 'DB_REPLICA_NAME': 'stockone', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'TERM': 'xterm', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_HOST': 'db1', 'AWS_ID': '********************', 'SHLVL': '1', 'DB_REPORTS_NAME': 'stockone', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DB_NAME': 'stockone', 'TASK_USAGE': '0', 'DB_REPORTS_PASS': 'stockone^123', 'DB_REPORTS_HOST': 'db1', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'DB_REPLICA_PASS': 'stockone^123', 'AWS_S3_REGION_NAME': 'ap-south-1', 'DEBUG': '1', 'DB_PASS': 'stockone^123', 'CONN_MAX_AGE': '300', 'ENTRYPOINT_FLAG': 'true', '_': '/usr/local/bin/python'})
[2025-04-30 07:09:32,190] INFO _appmap.env: appmap enabled: False
[2025-04-30 07:09:42,023] INFO _appmap.configuration: file: /application/appmap.yml
[2025-04-30 07:09:42,023] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-04-30 07:09:42,023] INFO _appmap.configuration: env: environ({'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'PYTHONUNBUFFERED': '1', 'DB_REPLICA_USER': 'stockone', 'HOSTNAME': 'ab07f183acaa', 'PYTHON_VERSION': '3.12.10', 'DB_REPORTS_USER': 'stockone', 'DB_REPLICA_HOST': 'db1', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'PWD': '/application', 'USE_S3': 'TRUE', 'DB_USER': 'stockone', 'HOME': '/root', 'LANG': 'C.UTF-8', 'DB_REPLICA_NAME': 'stockone', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'TERM': 'xterm', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_HOST': 'db1', 'AWS_ID': '********************', 'SHLVL': '1', 'DB_REPORTS_NAME': 'stockone', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DB_NAME': 'stockone', 'TASK_USAGE': '0', 'DB_REPORTS_PASS': 'stockone^123', 'DB_REPORTS_HOST': 'db1', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'DB_REPLICA_PASS': 'stockone^123', 'AWS_S3_REGION_NAME': 'ap-south-1', 'DEBUG': '1', 'DB_PASS': 'stockone^123', 'CONN_MAX_AGE': '300', 'ENTRYPOINT_FLAG': 'true', '_': '/usr/local/bin/python'})
[2025-04-30 07:09:42,028] INFO _appmap.env: appmap enabled: False
[2025-04-30 07:26:00,015] INFO _appmap.env: appmap enabled: False
[2025-04-30 07:26:24,767] INFO _appmap.env: appmap enabled: False
[2025-04-30 07:26:53,640] INFO _appmap.env: appmap enabled: False
[2025-04-30 07:32:45,357] INFO _appmap.env: appmap enabled: False
[2025-04-30 07:36:58,257] INFO _appmap.env: appmap enabled: False
[2025-04-30 07:37:01,934] INFO _appmap.env: appmap enabled: False
[2025-04-30 07:39:03,125] INFO _appmap.configuration: file: /application/appmap.yml
[2025-04-30 07:39:03,126] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-04-30 07:39:03,126] INFO _appmap.configuration: env: environ({'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'PYTHONUNBUFFERED': '1', 'DB_REPLICA_USER': 'stockone', 'HOSTNAME': 'ab07f183acaa', 'PYTHON_VERSION': '3.12.10', 'DB_REPORTS_USER': 'stockone', 'DB_REPLICA_HOST': 'db1', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'PWD': '/application', 'USE_S3': 'TRUE', 'DB_USER': 'stockone', 'HOME': '/root', 'LANG': 'C.UTF-8', 'DB_REPLICA_NAME': 'stockone', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'TERM': 'xterm', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_HOST': 'db1', 'AWS_ID': '********************', 'SHLVL': '1', 'DB_REPORTS_NAME': 'stockone', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DB_NAME': 'stockone', 'TASK_USAGE': '0', 'DB_REPORTS_PASS': 'stockone^123', 'DB_REPORTS_HOST': 'db1', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'DB_REPLICA_PASS': 'stockone^123', 'AWS_S3_REGION_NAME': 'ap-south-1', 'DEBUG': '1', 'DB_PASS': 'stockone^123', 'CONN_MAX_AGE': '300', 'ENTRYPOINT_FLAG': 'true', '_': '/usr/local/bin/python3'})
[2025-04-30 07:39:03,131] INFO _appmap.env: appmap enabled: False
[2025-04-30 07:40:20,381] INFO _appmap.configuration: file: /application/appmap.yml
[2025-04-30 07:40:20,382] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-04-30 07:40:20,382] INFO _appmap.configuration: env: environ({'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'HOSTNAME': 'ab07f183acaa', 'TERM': 'xterm', 'ENTRYPOINT_FLAG': 'true', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'AWS_S3_REGION_NAME': 'ap-south-1', 'CONN_MAX_AGE': '300', 'DB_REPLICA_PASS': 'stockone^123', 'AWS_ID': '********************', 'DB_REPLICA_HOST': 'db1', 'DB_HOST': 'db1', 'DB_REPLICA_USER': 'stockone', 'TASK_USAGE': '0', 'DB_REPORTS_PASS': 'stockone^123', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'USE_S3': 'TRUE', 'DB_NAME': 'stockone', 'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'DB_REPLICA_NAME': 'stockone', 'DB_REPORTS_HOST': 'db1', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'DEBUG': '1', 'DB_REPORTS_NAME': 'stockone', 'DB_PASS': 'stockone^123', 'DB_USER': 'stockone', 'DB_REPORTS_USER': 'stockone', 'LANG': 'C.UTF-8', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'PYTHON_VERSION': '3.12.10', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'PYTHONUNBUFFERED': '1', 'HOME': '/root'})
[2025-04-30 07:40:20,401] INFO _appmap.env: appmap enabled: False
[2025-04-30 07:40:45,838] INFO _appmap.configuration: file: /application/appmap.yml
[2025-04-30 07:40:45,838] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-04-30 07:40:45,839] INFO _appmap.configuration: env: environ({'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'HOSTNAME': 'ab07f183acaa', 'TERM': 'xterm', 'ENTRYPOINT_FLAG': 'true', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'AWS_S3_REGION_NAME': 'ap-south-1', 'CONN_MAX_AGE': '300', 'DB_REPLICA_PASS': 'stockone^123', 'AWS_ID': '********************', 'DB_REPLICA_HOST': 'db1', 'DB_HOST': 'db1', 'DB_REPLICA_USER': 'stockone', 'TASK_USAGE': '0', 'DB_REPORTS_PASS': 'stockone^123', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'USE_S3': 'TRUE', 'DB_NAME': 'stockone', 'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'DB_REPLICA_NAME': 'stockone', 'DB_REPORTS_HOST': 'db1', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'DEBUG': '1', 'DB_REPORTS_NAME': 'stockone', 'DB_PASS': 'stockone^123', 'DB_USER': 'stockone', 'DB_REPORTS_USER': 'stockone', 'LANG': 'C.UTF-8', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'PYTHON_VERSION': '3.12.10', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'PYTHONUNBUFFERED': '1', 'HOME': '/root'})
[2025-04-30 07:40:45,842] INFO _appmap.env: appmap enabled: False
[2025-04-30 08:05:05,901] INFO _appmap.env: appmap enabled: False
[2025-04-30 08:08:42,156] INFO _appmap.env: appmap enabled: False
[2025-04-30 08:09:10,774] INFO _appmap.env: appmap enabled: False
[2025-04-30 08:09:19,085] INFO _appmap.env: appmap enabled: False
[2025-05-13 05:00:11,416] INFO _appmap.configuration: file: /application/appmap.yml
[2025-05-13 05:00:11,416] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-05-13 05:00:11,416] INFO _appmap.configuration: env: environ({'HOSTNAME': '3c112a962ddd', 'DEBUG': '1', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'HOME': '/root', 'DB_NAME': 'stockone', 'PYTHONUNBUFFERED': '1', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'CONN_MAX_AGE': '300', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'DB_REPLICA_PASS': 'stockone^123', 'TASK_USAGE': '0', 'ENTRYPOINT_FLAG': 'true', 'AWS_S3_REGION_NAME': 'ap-south-1', 'TERM': 'xterm', 'DB_REPLICA_HOST': 'db1', 'DB_REPORTS_PASS': 'stockone^123', 'DB_REPLICA_USER': 'stockone', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'LANG': 'C.UTF-8', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_REPORTS_HOST': 'db1', 'DB_REPORTS_USER': 'stockone', 'PYTHON_VERSION': '3.12.10', 'DB_PASS': 'stockone^123', 'DB_REPLICA_NAME': 'stockone', 'AWS_ID': '********************', 'USE_S3': 'TRUE', 'PWD': '/application', 'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'DB_HOST': 'db1', 'DB_REPORTS_NAME': 'stockone', 'DB_USER': 'stockone'})
[2025-05-13 05:00:11,439] INFO _appmap.env: appmap enabled: False
[2025-05-13 05:00:18,575] INFO _appmap.configuration: file: /application/appmap.yml
[2025-05-13 05:00:18,575] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-05-13 05:00:18,575] INFO _appmap.configuration: env: environ({'HOSTNAME': '3c112a962ddd', 'DEBUG': '1', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'HOME': '/root', 'DB_NAME': 'stockone', 'PYTHONUNBUFFERED': '1', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'CONN_MAX_AGE': '300', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'DB_REPLICA_PASS': 'stockone^123', 'TASK_USAGE': '0', 'ENTRYPOINT_FLAG': 'true', 'AWS_S3_REGION_NAME': 'ap-south-1', 'TERM': 'xterm', 'DB_REPLICA_HOST': 'db1', 'DB_REPORTS_PASS': 'stockone^123', 'DB_REPLICA_USER': 'stockone', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'LANG': 'C.UTF-8', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_REPORTS_HOST': 'db1', 'DB_REPORTS_USER': 'stockone', 'PYTHON_VERSION': '3.12.10', 'DB_PASS': 'stockone^123', 'DB_REPLICA_NAME': 'stockone', 'AWS_ID': '********************', 'USE_S3': 'TRUE', 'PWD': '/application', 'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'DB_HOST': 'db1', 'DB_REPORTS_NAME': 'stockone', 'DB_USER': 'stockone'})
[2025-05-13 05:00:18,578] INFO _appmap.env: appmap enabled: False
[2025-05-13 05:00:21,229] INFO _appmap.configuration: file: /application/appmap.yml
[2025-05-13 05:00:21,229] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-05-13 05:00:21,229] INFO _appmap.configuration: env: environ({'HOSTNAME': '3c112a962ddd', 'DEBUG': '1', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'HOME': '/root', 'DB_NAME': 'stockone', 'PYTHONUNBUFFERED': '1', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'CONN_MAX_AGE': '300', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'DB_REPLICA_PASS': 'stockone^123', 'TASK_USAGE': '0', 'ENTRYPOINT_FLAG': 'true', 'AWS_S3_REGION_NAME': 'ap-south-1', 'TERM': 'xterm', 'DB_REPLICA_HOST': 'db1', 'DB_REPORTS_PASS': 'stockone^123', 'DB_REPLICA_USER': 'stockone', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'LANG': 'C.UTF-8', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_REPORTS_HOST': 'db1', 'DB_REPORTS_USER': 'stockone', 'PYTHON_VERSION': '3.12.10', 'DB_PASS': 'stockone^123', 'DB_REPLICA_NAME': 'stockone', 'AWS_ID': '********************', 'USE_S3': 'TRUE', 'PWD': '/application', 'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'DB_HOST': 'db1', 'DB_REPORTS_NAME': 'stockone', 'DB_USER': 'stockone'})
[2025-05-13 05:00:21,232] INFO _appmap.env: appmap enabled: False
[2025-05-13 05:00:24,168] INFO _appmap.configuration: file: /application/appmap.yml
[2025-05-13 05:00:24,174] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-05-13 05:00:24,174] INFO _appmap.configuration: env: environ({'HOSTNAME': '3c112a962ddd', 'DEBUG': '1', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'HOME': '/root', 'DB_NAME': 'stockone', 'PYTHONUNBUFFERED': '1', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'CONN_MAX_AGE': '300', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'DB_REPLICA_PASS': 'stockone^123', 'TASK_USAGE': '0', 'ENTRYPOINT_FLAG': 'true', 'AWS_S3_REGION_NAME': 'ap-south-1', 'TERM': 'xterm', 'DB_REPLICA_HOST': 'db1', 'DB_REPORTS_PASS': 'stockone^123', 'DB_REPLICA_USER': 'stockone', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'LANG': 'C.UTF-8', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_REPORTS_HOST': 'db1', 'DB_REPORTS_USER': 'stockone', 'PYTHON_VERSION': '3.12.10', 'DB_PASS': 'stockone^123', 'DB_REPLICA_NAME': 'stockone', 'AWS_ID': '********************', 'USE_S3': 'TRUE', 'PWD': '/application', 'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'DB_HOST': 'db1', 'DB_REPORTS_NAME': 'stockone', 'DB_USER': 'stockone'})
[2025-05-13 05:00:24,179] INFO _appmap.env: appmap enabled: False
[2025-05-13 05:00:28,451] INFO _appmap.configuration: file: /application/appmap.yml
[2025-05-13 05:00:28,451] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-05-13 05:00:28,451] INFO _appmap.configuration: env: environ({'HOSTNAME': '3c112a962ddd', 'DEBUG': '1', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'HOME': '/root', 'DB_NAME': 'stockone', 'PYTHONUNBUFFERED': '1', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'CONN_MAX_AGE': '300', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'DB_REPLICA_PASS': 'stockone^123', 'TASK_USAGE': '0', 'ENTRYPOINT_FLAG': 'true', 'AWS_S3_REGION_NAME': 'ap-south-1', 'TERM': 'xterm', 'DB_REPLICA_HOST': 'db1', 'DB_REPORTS_PASS': 'stockone^123', 'DB_REPLICA_USER': 'stockone', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'LANG': 'C.UTF-8', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_REPORTS_HOST': 'db1', 'DB_REPORTS_USER': 'stockone', 'PYTHON_VERSION': '3.12.10', 'DB_PASS': 'stockone^123', 'DB_REPLICA_NAME': 'stockone', 'AWS_ID': '********************', 'USE_S3': 'TRUE', 'PWD': '/application', 'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'DB_HOST': 'db1', 'DB_REPORTS_NAME': 'stockone', 'DB_USER': 'stockone'})
[2025-05-13 05:00:28,454] INFO _appmap.env: appmap enabled: False
[2025-05-13 05:00:28,531] INFO _appmap.env: appmap enabled: False
[2025-05-13 05:00:28,535] INFO _appmap.env: appmap enabled: False
[2025-05-13 05:02:43,575] INFO _appmap.configuration: file: /application/appmap.yml
[2025-05-13 05:02:43,575] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-05-13 05:02:43,575] INFO _appmap.configuration: env: environ({'HOSTNAME': '99c14ced4268', 'DEBUG': '1', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'HOME': '/root', 'DB_NAME': 'stockone', 'PYTHONUNBUFFERED': '1', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'CONN_MAX_AGE': '300', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'DB_REPLICA_PASS': 'stockone^123', 'TASK_USAGE': '0', 'ENTRYPOINT_FLAG': 'true', 'AWS_S3_REGION_NAME': 'ap-south-1', 'TERM': 'xterm', 'DB_REPLICA_HOST': 'db1', 'DB_REPORTS_PASS': 'stockone^123', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'DB_REPLICA_USER': 'stockone', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'LANG': 'C.UTF-8', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_REPORTS_HOST': 'db1', 'DB_REPORTS_USER': 'stockone', 'PYTHON_VERSION': '3.12.10', 'DB_REPLICA_NAME': 'stockone', 'DB_PASS': 'stockone^123', 'AWS_ID': '********************', 'USE_S3': 'TRUE', 'PWD': '/application', 'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'DB_HOST': 'db1', 'DB_REPORTS_NAME': 'stockone', 'DB_USER': 'stockone'})
[2025-05-13 05:02:43,582] INFO _appmap.env: appmap enabled: False
[2025-05-13 05:02:46,995] INFO _appmap.configuration: file: /application/appmap.yml
[2025-05-13 05:02:46,995] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-05-13 05:02:46,995] INFO _appmap.configuration: env: environ({'HOSTNAME': '99c14ced4268', 'DEBUG': '1', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'HOME': '/root', 'DB_NAME': 'stockone', 'PYTHONUNBUFFERED': '1', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'CONN_MAX_AGE': '300', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'DB_REPLICA_PASS': 'stockone^123', 'TASK_USAGE': '0', 'ENTRYPOINT_FLAG': 'true', 'AWS_S3_REGION_NAME': 'ap-south-1', 'TERM': 'xterm', 'DB_REPLICA_HOST': 'db1', 'DB_REPORTS_PASS': 'stockone^123', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'DB_REPLICA_USER': 'stockone', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'LANG': 'C.UTF-8', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_REPORTS_HOST': 'db1', 'DB_REPORTS_USER': 'stockone', 'PYTHON_VERSION': '3.12.10', 'DB_REPLICA_NAME': 'stockone', 'DB_PASS': 'stockone^123', 'AWS_ID': '********************', 'USE_S3': 'TRUE', 'PWD': '/application', 'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'DB_HOST': 'db1', 'DB_REPORTS_NAME': 'stockone', 'DB_USER': 'stockone'})
[2025-05-13 05:02:46,999] INFO _appmap.env: appmap enabled: False
[2025-05-13 05:02:49,593] INFO _appmap.configuration: file: /application/appmap.yml
[2025-05-13 05:02:49,593] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-05-13 05:02:49,593] INFO _appmap.configuration: env: environ({'HOSTNAME': '99c14ced4268', 'DEBUG': '1', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'HOME': '/root', 'DB_NAME': 'stockone', 'PYTHONUNBUFFERED': '1', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'CONN_MAX_AGE': '300', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'DB_REPLICA_PASS': 'stockone^123', 'TASK_USAGE': '0', 'ENTRYPOINT_FLAG': 'true', 'AWS_S3_REGION_NAME': 'ap-south-1', 'TERM': 'xterm', 'DB_REPLICA_HOST': 'db1', 'DB_REPORTS_PASS': 'stockone^123', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'DB_REPLICA_USER': 'stockone', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'LANG': 'C.UTF-8', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_REPORTS_HOST': 'db1', 'DB_REPORTS_USER': 'stockone', 'PYTHON_VERSION': '3.12.10', 'DB_REPLICA_NAME': 'stockone', 'DB_PASS': 'stockone^123', 'AWS_ID': '********************', 'USE_S3': 'TRUE', 'PWD': '/application', 'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'DB_HOST': 'db1', 'DB_REPORTS_NAME': 'stockone', 'DB_USER': 'stockone'})
[2025-05-13 05:02:49,596] INFO _appmap.env: appmap enabled: False
[2025-05-13 05:02:53,093] INFO _appmap.configuration: file: /application/appmap.yml
[2025-05-13 05:02:53,093] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-05-13 05:02:53,093] INFO _appmap.configuration: env: environ({'HOSTNAME': '99c14ced4268', 'DEBUG': '1', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'HOME': '/root', 'DB_NAME': 'stockone', 'PYTHONUNBUFFERED': '1', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'CONN_MAX_AGE': '300', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'DB_REPLICA_PASS': 'stockone^123', 'TASK_USAGE': '0', 'ENTRYPOINT_FLAG': 'true', 'AWS_S3_REGION_NAME': 'ap-south-1', 'TERM': 'xterm', 'DB_REPLICA_HOST': 'db1', 'DB_REPORTS_PASS': 'stockone^123', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'DB_REPLICA_USER': 'stockone', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'LANG': 'C.UTF-8', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_REPORTS_HOST': 'db1', 'DB_REPORTS_USER': 'stockone', 'PYTHON_VERSION': '3.12.10', 'DB_REPLICA_NAME': 'stockone', 'DB_PASS': 'stockone^123', 'AWS_ID': '********************', 'USE_S3': 'TRUE', 'PWD': '/application', 'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'DB_HOST': 'db1', 'DB_REPORTS_NAME': 'stockone', 'DB_USER': 'stockone'})
[2025-05-13 05:02:53,098] INFO _appmap.env: appmap enabled: False
[2025-05-13 05:02:59,548] INFO _appmap.configuration: file: /application/appmap.yml
[2025-05-13 05:02:59,549] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-05-13 05:02:59,549] INFO _appmap.configuration: env: environ({'HOSTNAME': '99c14ced4268', 'DEBUG': '1', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'HOME': '/root', 'DB_NAME': 'stockone', 'PYTHONUNBUFFERED': '1', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'CONN_MAX_AGE': '300', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'DB_REPLICA_PASS': 'stockone^123', 'TASK_USAGE': '0', 'ENTRYPOINT_FLAG': 'true', 'AWS_S3_REGION_NAME': 'ap-south-1', 'TERM': 'xterm', 'DB_REPLICA_HOST': 'db1', 'DB_REPORTS_PASS': 'stockone^123', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'DB_REPLICA_USER': 'stockone', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'LANG': 'C.UTF-8', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_REPORTS_HOST': 'db1', 'DB_REPORTS_USER': 'stockone', 'PYTHON_VERSION': '3.12.10', 'DB_REPLICA_NAME': 'stockone', 'DB_PASS': 'stockone^123', 'AWS_ID': '********************', 'USE_S3': 'TRUE', 'PWD': '/application', 'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'DB_HOST': 'db1', 'DB_REPORTS_NAME': 'stockone', 'DB_USER': 'stockone'})
[2025-05-13 05:02:59,554] INFO _appmap.env: appmap enabled: False
[2025-05-13 05:02:59,675] INFO _appmap.env: appmap enabled: False
[2025-05-13 05:02:59,682] INFO _appmap.env: appmap enabled: False
[2025-05-13 05:08:18,864] INFO _appmap.configuration: file: /application/appmap.yml
[2025-05-13 05:08:18,865] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-05-13 05:08:18,865] INFO _appmap.configuration: env: environ({'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'PYTHONUNBUFFERED': '1', 'DB_REPLICA_USER': 'stockone', 'HOSTNAME': '99c14ced4268', 'PYTHON_VERSION': '3.12.10', 'DB_REPORTS_USER': 'stockone', 'DB_REPLICA_HOST': 'db1', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'PWD': '/application', 'USE_S3': 'TRUE', 'DB_USER': 'stockone', 'HOME': '/root', 'LANG': 'C.UTF-8', 'DB_REPLICA_NAME': 'stockone', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'TERM': 'xterm', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_HOST': 'db1', 'AWS_ID': '********************', 'SHLVL': '1', 'DB_REPORTS_NAME': 'stockone', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DB_NAME': 'stockone', 'TASK_USAGE': '0', 'DB_REPORTS_PASS': 'stockone^123', 'DB_REPORTS_HOST': 'db1', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'DB_REPLICA_PASS': 'stockone^123', 'AWS_S3_REGION_NAME': 'ap-south-1', 'DEBUG': '1', 'DB_PASS': 'stockone^123', 'ENTRYPOINT_FLAG': 'true', 'CONN_MAX_AGE': '300', '_': '/usr/local/bin/python'})
[2025-05-13 05:08:18,877] INFO _appmap.env: appmap enabled: False
[2025-05-13 05:08:33,744] INFO _appmap.configuration: file: /application/appmap.yml
[2025-05-13 05:08:33,744] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-05-13 05:08:33,744] INFO _appmap.configuration: env: environ({'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'PYTHONUNBUFFERED': '1', 'DB_REPLICA_USER': 'stockone', 'HOSTNAME': '99c14ced4268', 'PYTHON_VERSION': '3.12.10', 'DB_REPORTS_USER': 'stockone', 'DB_REPLICA_HOST': 'db1', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'PWD': '/application', 'USE_S3': 'TRUE', 'DB_USER': 'stockone', 'HOME': '/root', 'LANG': 'C.UTF-8', 'DB_REPLICA_NAME': 'stockone', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'TERM': 'xterm', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_HOST': 'db1', 'AWS_ID': '********************', 'SHLVL': '1', 'DB_REPORTS_NAME': 'stockone', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DB_NAME': 'stockone', 'TASK_USAGE': '0', 'DB_REPORTS_PASS': 'stockone^123', 'DB_REPORTS_HOST': 'db1', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'DB_REPLICA_PASS': 'stockone^123', 'AWS_S3_REGION_NAME': 'ap-south-1', 'DEBUG': '1', 'DB_PASS': 'stockone^123', 'ENTRYPOINT_FLAG': 'true', 'CONN_MAX_AGE': '300', '_': '/usr/local/bin/python'})
[2025-05-13 05:08:33,748] INFO _appmap.env: appmap enabled: False
[2025-05-13 05:08:44,903] INFO _appmap.configuration: file: /application/appmap.yml
[2025-05-13 05:08:44,903] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-05-13 05:08:44,903] INFO _appmap.configuration: env: environ({'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'PYTHONUNBUFFERED': '1', 'DB_REPLICA_USER': 'stockone', 'HOSTNAME': '99c14ced4268', 'PYTHON_VERSION': '3.12.10', 'DB_REPORTS_USER': 'stockone', 'DB_REPLICA_HOST': 'db1', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'PWD': '/application', 'USE_S3': 'TRUE', 'DB_USER': 'stockone', 'HOME': '/root', 'LANG': 'C.UTF-8', 'DB_REPLICA_NAME': 'stockone', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'TERM': 'xterm', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_HOST': 'db1', 'AWS_ID': '********************', 'SHLVL': '1', 'DB_REPORTS_NAME': 'stockone', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DB_NAME': 'stockone', 'TASK_USAGE': '0', 'DB_REPORTS_PASS': 'stockone^123', 'DB_REPORTS_HOST': 'db1', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'DB_REPLICA_PASS': 'stockone^123', 'AWS_S3_REGION_NAME': 'ap-south-1', 'DEBUG': '1', 'DB_PASS': 'stockone^123', 'ENTRYPOINT_FLAG': 'true', 'CONN_MAX_AGE': '300', '_': '/usr/local/bin/python'})
[2025-05-13 05:08:44,908] INFO _appmap.env: appmap enabled: False
[2025-05-14 11:50:33,128] INFO _appmap.configuration: file: /application/appmap.yml
[2025-05-14 11:50:33,129] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-05-14 11:50:33,129] INFO _appmap.configuration: env: environ({'HOSTNAME': 'd8ba58a6bd83', 'DEBUG': '1', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'HOME': '/root', 'DB_NAME': 'stockone', 'PYTHONUNBUFFERED': '1', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'CONN_MAX_AGE': '300', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'DB_REPLICA_PASS': 'stockone^123', 'TASK_USAGE': '0', 'ENTRYPOINT_FLAG': 'true', 'AWS_S3_REGION_NAME': 'ap-south-1', 'TERM': 'xterm', 'DB_REPLICA_HOST': 'db1', 'DB_REPLICA_USER': 'stockone', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'DB_REPORTS_PASS': 'stockone^123', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'LANG': 'C.UTF-8', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_REPORTS_HOST': 'db1', 'DB_REPORTS_USER': 'stockone', 'PYTHON_VERSION': '3.12.10', 'DB_REPLICA_NAME': 'stockone', 'AWS_ID': '********************', 'DB_PASS': 'stockone^123', 'USE_S3': 'TRUE', 'PWD': '/application', 'DB_HOST': 'db1', 'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'DB_REPORTS_NAME': 'stockone', 'DB_USER': 'stockone'})
[2025-05-14 11:50:33,145] INFO _appmap.env: appmap enabled: False
[2025-05-14 11:50:40,703] INFO _appmap.configuration: file: /application/appmap.yml
[2025-05-14 11:50:40,704] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-05-14 11:50:40,704] INFO _appmap.configuration: env: environ({'HOSTNAME': 'd8ba58a6bd83', 'DEBUG': '1', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'HOME': '/root', 'DB_NAME': 'stockone', 'PYTHONUNBUFFERED': '1', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'CONN_MAX_AGE': '300', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'DB_REPLICA_PASS': 'stockone^123', 'TASK_USAGE': '0', 'ENTRYPOINT_FLAG': 'true', 'AWS_S3_REGION_NAME': 'ap-south-1', 'TERM': 'xterm', 'DB_REPLICA_HOST': 'db1', 'DB_REPLICA_USER': 'stockone', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'DB_REPORTS_PASS': 'stockone^123', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'LANG': 'C.UTF-8', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_REPORTS_HOST': 'db1', 'DB_REPORTS_USER': 'stockone', 'PYTHON_VERSION': '3.12.10', 'DB_REPLICA_NAME': 'stockone', 'AWS_ID': '********************', 'DB_PASS': 'stockone^123', 'USE_S3': 'TRUE', 'PWD': '/application', 'DB_HOST': 'db1', 'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'DB_REPORTS_NAME': 'stockone', 'DB_USER': 'stockone'})
[2025-05-14 11:50:40,707] INFO _appmap.env: appmap enabled: False
[2025-05-14 11:50:43,406] INFO _appmap.configuration: file: /application/appmap.yml
[2025-05-14 11:50:43,406] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-05-14 11:50:43,406] INFO _appmap.configuration: env: environ({'HOSTNAME': 'd8ba58a6bd83', 'DEBUG': '1', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'HOME': '/root', 'DB_NAME': 'stockone', 'PYTHONUNBUFFERED': '1', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'CONN_MAX_AGE': '300', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'DB_REPLICA_PASS': 'stockone^123', 'TASK_USAGE': '0', 'ENTRYPOINT_FLAG': 'true', 'AWS_S3_REGION_NAME': 'ap-south-1', 'TERM': 'xterm', 'DB_REPLICA_HOST': 'db1', 'DB_REPLICA_USER': 'stockone', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'DB_REPORTS_PASS': 'stockone^123', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'LANG': 'C.UTF-8', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_REPORTS_HOST': 'db1', 'DB_REPORTS_USER': 'stockone', 'PYTHON_VERSION': '3.12.10', 'DB_REPLICA_NAME': 'stockone', 'AWS_ID': '********************', 'DB_PASS': 'stockone^123', 'USE_S3': 'TRUE', 'PWD': '/application', 'DB_HOST': 'db1', 'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'DB_REPORTS_NAME': 'stockone', 'DB_USER': 'stockone'})
[2025-05-14 11:50:43,409] INFO _appmap.env: appmap enabled: False
[2025-05-14 11:50:46,011] INFO _appmap.configuration: file: /application/appmap.yml
[2025-05-14 11:50:46,011] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-05-14 11:50:46,011] INFO _appmap.configuration: env: environ({'HOSTNAME': 'd8ba58a6bd83', 'DEBUG': '1', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'HOME': '/root', 'DB_NAME': 'stockone', 'PYTHONUNBUFFERED': '1', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'CONN_MAX_AGE': '300', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'DB_REPLICA_PASS': 'stockone^123', 'TASK_USAGE': '0', 'ENTRYPOINT_FLAG': 'true', 'AWS_S3_REGION_NAME': 'ap-south-1', 'TERM': 'xterm', 'DB_REPLICA_HOST': 'db1', 'DB_REPLICA_USER': 'stockone', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'DB_REPORTS_PASS': 'stockone^123', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'LANG': 'C.UTF-8', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_REPORTS_HOST': 'db1', 'DB_REPORTS_USER': 'stockone', 'PYTHON_VERSION': '3.12.10', 'DB_REPLICA_NAME': 'stockone', 'AWS_ID': '********************', 'DB_PASS': 'stockone^123', 'USE_S3': 'TRUE', 'PWD': '/application', 'DB_HOST': 'db1', 'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'DB_REPORTS_NAME': 'stockone', 'DB_USER': 'stockone'})
[2025-05-14 11:50:46,014] INFO _appmap.env: appmap enabled: False
[2025-05-14 11:50:50,182] INFO _appmap.configuration: file: /application/appmap.yml
[2025-05-14 11:50:50,182] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-05-14 11:50:50,182] INFO _appmap.configuration: env: environ({'HOSTNAME': 'd8ba58a6bd83', 'DEBUG': '1', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'HOME': '/root', 'DB_NAME': 'stockone', 'PYTHONUNBUFFERED': '1', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'CONN_MAX_AGE': '300', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'DB_REPLICA_PASS': 'stockone^123', 'TASK_USAGE': '0', 'ENTRYPOINT_FLAG': 'true', 'AWS_S3_REGION_NAME': 'ap-south-1', 'TERM': 'xterm', 'DB_REPLICA_HOST': 'db1', 'DB_REPLICA_USER': 'stockone', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'DB_REPORTS_PASS': 'stockone^123', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'LANG': 'C.UTF-8', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_REPORTS_HOST': 'db1', 'DB_REPORTS_USER': 'stockone', 'PYTHON_VERSION': '3.12.10', 'DB_REPLICA_NAME': 'stockone', 'AWS_ID': '********************', 'DB_PASS': 'stockone^123', 'USE_S3': 'TRUE', 'PWD': '/application', 'DB_HOST': 'db1', 'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'DB_REPORTS_NAME': 'stockone', 'DB_USER': 'stockone'})
[2025-05-14 11:50:50,187] INFO _appmap.env: appmap enabled: False
[2025-05-14 11:50:50,265] INFO _appmap.env: appmap enabled: False
[2025-05-14 11:50:50,268] INFO _appmap.env: appmap enabled: False
[2025-05-14 11:51:06,931] INFO _appmap.configuration: file: /application/appmap.yml
[2025-05-14 11:51:06,931] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-05-14 11:51:06,932] INFO _appmap.configuration: env: environ({'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'PYTHONUNBUFFERED': '1', 'DB_REPLICA_USER': 'stockone', 'HOSTNAME': 'd8ba58a6bd83', 'PYTHON_VERSION': '3.12.10', 'DB_REPORTS_USER': 'stockone', 'DB_REPLICA_HOST': 'db1', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'PWD': '/application', 'USE_S3': 'TRUE', 'DB_USER': 'stockone', 'HOME': '/root', 'LANG': 'C.UTF-8', 'DB_REPLICA_NAME': 'stockone', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'TERM': 'xterm', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_HOST': 'db1', 'AWS_ID': '********************', 'SHLVL': '1', 'DB_REPORTS_NAME': 'stockone', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DB_NAME': 'stockone', 'TASK_USAGE': '0', 'DB_REPORTS_PASS': 'stockone^123', 'DB_REPORTS_HOST': 'db1', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'DB_REPLICA_PASS': 'stockone^123', 'AWS_S3_REGION_NAME': 'ap-south-1', 'DEBUG': '1', 'DB_PASS': 'stockone^123', 'CONN_MAX_AGE': '300', 'ENTRYPOINT_FLAG': 'true', '_': '/usr/local/bin/python'})
[2025-05-14 11:51:06,937] INFO _appmap.env: appmap enabled: False
[2025-05-14 11:51:16,660] INFO _appmap.configuration: file: /application/appmap.yml
[2025-05-14 11:51:16,660] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-05-14 11:51:16,661] INFO _appmap.configuration: env: environ({'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'PYTHONUNBUFFERED': '1', 'DB_REPLICA_USER': 'stockone', 'HOSTNAME': 'd8ba58a6bd83', 'PYTHON_VERSION': '3.12.10', 'DB_REPORTS_USER': 'stockone', 'DB_REPLICA_HOST': 'db1', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'PWD': '/application', 'USE_S3': 'TRUE', 'DB_USER': 'stockone', 'HOME': '/root', 'LANG': 'C.UTF-8', 'DB_REPLICA_NAME': 'stockone', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'TERM': 'xterm', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_HOST': 'db1', 'AWS_ID': '********************', 'SHLVL': '1', 'DB_REPORTS_NAME': 'stockone', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DB_NAME': 'stockone', 'TASK_USAGE': '0', 'DB_REPORTS_PASS': 'stockone^123', 'DB_REPORTS_HOST': 'db1', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'DB_REPLICA_PASS': 'stockone^123', 'AWS_S3_REGION_NAME': 'ap-south-1', 'DEBUG': '1', 'DB_PASS': 'stockone^123', 'CONN_MAX_AGE': '300', 'ENTRYPOINT_FLAG': 'true', '_': '/usr/local/bin/python'})
[2025-05-14 11:51:16,664] INFO _appmap.env: appmap enabled: False
[2025-05-14 11:58:15,119] INFO _appmap.configuration: file: /application/appmap.yml
[2025-05-14 11:58:15,120] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-05-14 11:58:15,120] INFO _appmap.configuration: env: environ({'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'PYTHONUNBUFFERED': '1', 'DB_REPLICA_USER': 'stockone', 'HOSTNAME': 'd8ba58a6bd83', 'PYTHON_VERSION': '3.12.10', 'DB_REPORTS_USER': 'stockone', 'DB_REPLICA_HOST': 'db1', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'PWD': '/application', 'USE_S3': 'TRUE', 'DB_USER': 'stockone', 'HOME': '/root', 'LANG': 'C.UTF-8', 'DB_REPLICA_NAME': 'stockone', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'TERM': 'xterm', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_HOST': 'db1', 'AWS_ID': '********************', 'SHLVL': '1', 'DB_REPORTS_NAME': 'stockone', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DB_NAME': 'stockone', 'TASK_USAGE': '0', 'DB_REPORTS_PASS': 'stockone^123', 'DB_REPORTS_HOST': 'db1', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'DB_REPLICA_PASS': 'stockone^123', 'AWS_S3_REGION_NAME': 'ap-south-1', 'DEBUG': '1', 'DB_PASS': 'stockone^123', 'CONN_MAX_AGE': '300', 'ENTRYPOINT_FLAG': 'true', '_': '/usr/local/bin/python', 'OLDPWD': '/application/inventory/migrations'})
[2025-05-14 11:58:15,126] INFO _appmap.env: appmap enabled: False
[2025-05-14 11:58:30,372] INFO _appmap.configuration: file: /application/appmap.yml
[2025-05-14 11:58:30,372] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-05-14 11:58:30,372] INFO _appmap.configuration: env: environ({'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'PYTHONUNBUFFERED': '1', 'DB_REPLICA_USER': 'stockone', 'HOSTNAME': 'd8ba58a6bd83', 'PYTHON_VERSION': '3.12.10', 'DB_REPORTS_USER': 'stockone', 'DB_REPLICA_HOST': 'db1', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'PWD': '/application', 'USE_S3': 'TRUE', 'DB_USER': 'stockone', 'HOME': '/root', 'LANG': 'C.UTF-8', 'DB_REPLICA_NAME': 'stockone', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'TERM': 'xterm', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_HOST': 'db1', 'AWS_ID': '********************', 'SHLVL': '1', 'DB_REPORTS_NAME': 'stockone', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DB_NAME': 'stockone', 'TASK_USAGE': '0', 'DB_REPORTS_PASS': 'stockone^123', 'DB_REPORTS_HOST': 'db1', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'DB_REPLICA_PASS': 'stockone^123', 'AWS_S3_REGION_NAME': 'ap-south-1', 'DEBUG': '1', 'DB_PASS': 'stockone^123', 'CONN_MAX_AGE': '300', 'ENTRYPOINT_FLAG': 'true', '_': '/usr/local/bin/python', 'OLDPWD': '/application/inventory/migrations'})
[2025-05-14 11:58:30,376] INFO _appmap.env: appmap enabled: False
[2025-05-14 12:24:37,289] INFO _appmap.configuration: file: /application/appmap.yml
[2025-05-14 12:24:37,290] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-05-14 12:24:37,290] INFO _appmap.configuration: env: environ({'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'PYTHONUNBUFFERED': '1', 'DB_REPLICA_USER': 'stockone', 'HOSTNAME': 'd8ba58a6bd83', 'PYTHON_VERSION': '3.12.10', 'DB_REPORTS_USER': 'stockone', 'DB_REPLICA_HOST': 'db1', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'PWD': '/application', 'USE_S3': 'TRUE', 'DB_USER': 'stockone', 'HOME': '/root', 'LANG': 'C.UTF-8', 'DB_REPLICA_NAME': 'stockone', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'TERM': 'xterm', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_HOST': 'db1', 'AWS_ID': '********************', 'SHLVL': '1', 'DB_REPORTS_NAME': 'stockone', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DB_NAME': 'stockone', 'TASK_USAGE': '0', 'DB_REPORTS_PASS': 'stockone^123', 'DB_REPORTS_HOST': 'db1', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'DB_REPLICA_PASS': 'stockone^123', 'AWS_S3_REGION_NAME': 'ap-south-1', 'DEBUG': '1', 'DB_PASS': 'stockone^123', 'CONN_MAX_AGE': '300', 'ENTRYPOINT_FLAG': 'true', '_': '/usr/local/bin/python', 'OLDPWD': '/application/inventory'})
[2025-05-14 12:24:37,301] INFO _appmap.env: appmap enabled: False
[2025-05-14 12:24:44,518] INFO _appmap.configuration: file: /application/appmap.yml
[2025-05-14 12:24:44,518] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-05-14 12:24:44,519] INFO _appmap.configuration: env: environ({'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'PYTHONUNBUFFERED': '1', 'DB_REPLICA_USER': 'stockone', 'HOSTNAME': 'd8ba58a6bd83', 'PYTHON_VERSION': '3.12.10', 'DB_REPORTS_USER': 'stockone', 'DB_REPLICA_HOST': 'db1', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'PWD': '/application', 'USE_S3': 'TRUE', 'DB_USER': 'stockone', 'HOME': '/root', 'LANG': 'C.UTF-8', 'DB_REPLICA_NAME': 'stockone', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'TERM': 'xterm', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_HOST': 'db1', 'AWS_ID': '********************', 'SHLVL': '1', 'DB_REPORTS_NAME': 'stockone', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DB_NAME': 'stockone', 'TASK_USAGE': '0', 'DB_REPORTS_PASS': 'stockone^123', 'DB_REPORTS_HOST': 'db1', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'DB_REPLICA_PASS': 'stockone^123', 'AWS_S3_REGION_NAME': 'ap-south-1', 'DEBUG': '1', 'DB_PASS': 'stockone^123', 'CONN_MAX_AGE': '300', 'ENTRYPOINT_FLAG': 'true', '_': '/usr/local/bin/python', 'OLDPWD': '/application/inventory'})
[2025-05-14 12:24:44,522] INFO _appmap.env: appmap enabled: False
[2025-05-14 12:25:07,689] INFO _appmap.configuration: file: /application/appmap.yml
[2025-05-14 12:25:07,690] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-05-14 12:25:07,690] INFO _appmap.configuration: env: environ({'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'PYTHONUNBUFFERED': '1', 'DB_REPLICA_USER': 'stockone', 'HOSTNAME': 'd8ba58a6bd83', 'PYTHON_VERSION': '3.12.10', 'DB_REPORTS_USER': 'stockone', 'DB_REPLICA_HOST': 'db1', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'PWD': '/application', 'USE_S3': 'TRUE', 'DB_USER': 'stockone', 'HOME': '/root', 'LANG': 'C.UTF-8', 'DB_REPLICA_NAME': 'stockone', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'TERM': 'xterm', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_HOST': 'db1', 'AWS_ID': '********************', 'SHLVL': '1', 'DB_REPORTS_NAME': 'stockone', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DB_NAME': 'stockone', 'TASK_USAGE': '0', 'DB_REPORTS_PASS': 'stockone^123', 'DB_REPORTS_HOST': 'db1', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'DB_REPLICA_PASS': 'stockone^123', 'AWS_S3_REGION_NAME': 'ap-south-1', 'DEBUG': '1', 'DB_PASS': 'stockone^123', 'CONN_MAX_AGE': '300', 'ENTRYPOINT_FLAG': 'true', '_': '/usr/local/bin/python', 'OLDPWD': '/application/inventory'})
[2025-05-14 12:25:07,695] INFO _appmap.env: appmap enabled: False
[2025-05-14 12:26:12,411] INFO _appmap.configuration: file: /application/appmap.yml
[2025-05-14 12:26:12,412] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-05-14 12:26:12,412] INFO _appmap.configuration: env: environ({'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'PYTHONUNBUFFERED': '1', 'DB_REPLICA_USER': 'stockone', 'HOSTNAME': 'd8ba58a6bd83', 'PYTHON_VERSION': '3.12.10', 'DB_REPORTS_USER': 'stockone', 'DB_REPLICA_HOST': 'db1', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'PWD': '/application', 'USE_S3': 'TRUE', 'DB_USER': 'stockone', 'HOME': '/root', 'LANG': 'C.UTF-8', 'DB_REPLICA_NAME': 'stockone', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'TERM': 'xterm', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_HOST': 'db1', 'AWS_ID': '********************', 'SHLVL': '1', 'DB_REPORTS_NAME': 'stockone', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DB_NAME': 'stockone', 'TASK_USAGE': '0', 'DB_REPORTS_PASS': 'stockone^123', 'DB_REPORTS_HOST': 'db1', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'DB_REPLICA_PASS': 'stockone^123', 'AWS_S3_REGION_NAME': 'ap-south-1', 'DEBUG': '1', 'DB_PASS': 'stockone^123', 'CONN_MAX_AGE': '300', 'ENTRYPOINT_FLAG': 'true', '_': '/usr/local/bin/python', 'OLDPWD': '/application/inventory/migrations'})
[2025-05-14 12:26:12,416] INFO _appmap.env: appmap enabled: False
[2025-05-14 12:26:22,056] INFO _appmap.configuration: file: /application/appmap.yml
[2025-05-14 12:26:22,056] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-05-14 12:26:22,056] INFO _appmap.configuration: env: environ({'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'PYTHONUNBUFFERED': '1', 'DB_REPLICA_USER': 'stockone', 'HOSTNAME': 'd8ba58a6bd83', 'PYTHON_VERSION': '3.12.10', 'DB_REPORTS_USER': 'stockone', 'DB_REPLICA_HOST': 'db1', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'PWD': '/application', 'USE_S3': 'TRUE', 'DB_USER': 'stockone', 'HOME': '/root', 'LANG': 'C.UTF-8', 'DB_REPLICA_NAME': 'stockone', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'TERM': 'xterm', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_HOST': 'db1', 'AWS_ID': '********************', 'SHLVL': '1', 'DB_REPORTS_NAME': 'stockone', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DB_NAME': 'stockone', 'TASK_USAGE': '0', 'DB_REPORTS_PASS': 'stockone^123', 'DB_REPORTS_HOST': 'db1', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'DB_REPLICA_PASS': 'stockone^123', 'AWS_S3_REGION_NAME': 'ap-south-1', 'DEBUG': '1', 'DB_PASS': 'stockone^123', 'CONN_MAX_AGE': '300', 'ENTRYPOINT_FLAG': 'true', '_': '/usr/local/bin/python', 'OLDPWD': '/application/inventory/migrations'})
[2025-05-14 12:26:22,060] INFO _appmap.env: appmap enabled: False
[2025-05-14 12:26:29,348] INFO _appmap.configuration: file: /application/appmap.yml
[2025-05-14 12:26:29,349] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-05-14 12:26:29,349] INFO _appmap.configuration: env: environ({'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'PYTHONUNBUFFERED': '1', 'DB_REPLICA_USER': 'stockone', 'HOSTNAME': 'd8ba58a6bd83', 'PYTHON_VERSION': '3.12.10', 'DB_REPORTS_USER': 'stockone', 'DB_REPLICA_HOST': 'db1', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'PWD': '/application', 'USE_S3': 'TRUE', 'DB_USER': 'stockone', 'HOME': '/root', 'LANG': 'C.UTF-8', 'DB_REPLICA_NAME': 'stockone', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'TERM': 'xterm', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_HOST': 'db1', 'AWS_ID': '********************', 'SHLVL': '1', 'DB_REPORTS_NAME': 'stockone', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DB_NAME': 'stockone', 'TASK_USAGE': '0', 'DB_REPORTS_PASS': 'stockone^123', 'DB_REPORTS_HOST': 'db1', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'DB_REPLICA_PASS': 'stockone^123', 'AWS_S3_REGION_NAME': 'ap-south-1', 'DEBUG': '1', 'DB_PASS': 'stockone^123', 'CONN_MAX_AGE': '300', 'ENTRYPOINT_FLAG': 'true', '_': '/usr/local/bin/python', 'OLDPWD': '/application/inventory/migrations'})
[2025-05-14 12:26:29,353] INFO _appmap.env: appmap enabled: False
[2025-05-14 13:34:39,092] INFO _appmap.configuration: file: /application/appmap.yml
[2025-05-14 13:34:39,092] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-05-14 13:34:39,092] INFO _appmap.configuration: env: environ({'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'PYTHONUNBUFFERED': '1', 'DB_REPLICA_USER': 'stockone', 'HOSTNAME': 'd8ba58a6bd83', 'PYTHON_VERSION': '3.12.10', 'DB_REPORTS_USER': 'stockone', 'DB_REPLICA_HOST': 'db1', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'PWD': '/application', 'USE_S3': 'TRUE', 'DB_USER': 'stockone', 'HOME': '/root', 'LANG': 'C.UTF-8', 'DB_REPLICA_NAME': 'stockone', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'TERM': 'xterm', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_HOST': 'db1', 'AWS_ID': '********************', 'SHLVL': '1', 'DB_REPORTS_NAME': 'stockone', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DB_NAME': 'stockone', 'TASK_USAGE': '0', 'DB_REPORTS_PASS': 'stockone^123', 'DB_REPORTS_HOST': 'db1', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'DB_REPLICA_PASS': 'stockone^123', 'AWS_S3_REGION_NAME': 'ap-south-1', 'DEBUG': '1', 'DB_PASS': 'stockone^123', 'CONN_MAX_AGE': '300', 'ENTRYPOINT_FLAG': 'true', '_': '/usr/local/bin/python', 'OLDPWD': '/application/inventory/migrations'})
[2025-05-14 13:34:39,110] INFO _appmap.env: appmap enabled: False
[2025-05-15 07:49:30,143] INFO _appmap.env: appmap enabled: False
[2025-05-15 08:05:09,745] INFO _appmap.configuration: file: /application/appmap.yml
[2025-05-15 08:05:09,745] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-05-15 08:05:09,745] INFO _appmap.configuration: env: environ({'APPROVAL_SERVICE_URL': 'http://************:8001/api/v1/', 'HOSTNAME': '33af9efc78a9', 'DEBUG': '1', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'HOME': '/root', 'DB_NAME': 'stockone', 'PYTHONUNBUFFERED': '1', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'CONN_MAX_AGE': '300', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'DB_REPLICA_PASS': 'stockone^123', 'TASK_USAGE': '0', 'APPROVAL_ORIGIN_NAME': 'dev-approval-app', 'ENTRYPOINT_FLAG': 'true', 'AWS_S3_REGION_NAME': 'ap-south-1', 'TERM': 'xterm', 'DB_REPLICA_HOST': 'db1', 'DB_REPLICA_USER': 'stockone', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'DB_REPORTS_PASS': 'stockone^123', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'LANG': 'C.UTF-8', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_REPORTS_HOST': 'db1', 'DB_REPORTS_USER': 'stockone', 'PYTHON_VERSION': '3.12.10', 'AWS_ID': '********************', 'DB_REPLICA_NAME': 'stockone', 'DB_PASS': 'stockone^123', 'USE_S3': 'TRUE', 'PWD': '/application', 'DB_HOST': 'db1', 'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'DB_USER': 'stockone', 'DB_REPORTS_NAME': 'stockone'})
[2025-05-15 08:05:09,750] INFO _appmap.env: appmap enabled: False
[2025-05-15 08:05:17,189] INFO _appmap.configuration: file: /application/appmap.yml
[2025-05-15 08:05:17,189] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-05-15 08:05:17,190] INFO _appmap.configuration: env: environ({'APPROVAL_SERVICE_URL': 'http://************:8001/api/v1/', 'HOSTNAME': '33af9efc78a9', 'DEBUG': '1', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'HOME': '/root', 'DB_NAME': 'stockone', 'PYTHONUNBUFFERED': '1', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'CONN_MAX_AGE': '300', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'DB_REPLICA_PASS': 'stockone^123', 'TASK_USAGE': '0', 'APPROVAL_ORIGIN_NAME': 'dev-approval-app', 'ENTRYPOINT_FLAG': 'true', 'AWS_S3_REGION_NAME': 'ap-south-1', 'TERM': 'xterm', 'DB_REPLICA_HOST': 'db1', 'DB_REPLICA_USER': 'stockone', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'DB_REPORTS_PASS': 'stockone^123', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'LANG': 'C.UTF-8', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_REPORTS_HOST': 'db1', 'DB_REPORTS_USER': 'stockone', 'PYTHON_VERSION': '3.12.10', 'AWS_ID': '********************', 'DB_REPLICA_NAME': 'stockone', 'DB_PASS': 'stockone^123', 'USE_S3': 'TRUE', 'PWD': '/application', 'DB_HOST': 'db1', 'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'DB_USER': 'stockone', 'DB_REPORTS_NAME': 'stockone'})
[2025-05-15 08:05:17,193] INFO _appmap.env: appmap enabled: False
[2025-05-15 08:05:19,813] INFO _appmap.configuration: file: /application/appmap.yml
[2025-05-15 08:05:19,813] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-05-15 08:05:19,813] INFO _appmap.configuration: env: environ({'APPROVAL_SERVICE_URL': 'http://************:8001/api/v1/', 'HOSTNAME': '33af9efc78a9', 'DEBUG': '1', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'HOME': '/root', 'DB_NAME': 'stockone', 'PYTHONUNBUFFERED': '1', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'CONN_MAX_AGE': '300', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'DB_REPLICA_PASS': 'stockone^123', 'TASK_USAGE': '0', 'APPROVAL_ORIGIN_NAME': 'dev-approval-app', 'ENTRYPOINT_FLAG': 'true', 'AWS_S3_REGION_NAME': 'ap-south-1', 'TERM': 'xterm', 'DB_REPLICA_HOST': 'db1', 'DB_REPLICA_USER': 'stockone', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'DB_REPORTS_PASS': 'stockone^123', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'LANG': 'C.UTF-8', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_REPORTS_HOST': 'db1', 'DB_REPORTS_USER': 'stockone', 'PYTHON_VERSION': '3.12.10', 'AWS_ID': '********************', 'DB_REPLICA_NAME': 'stockone', 'DB_PASS': 'stockone^123', 'USE_S3': 'TRUE', 'PWD': '/application', 'DB_HOST': 'db1', 'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'DB_USER': 'stockone', 'DB_REPORTS_NAME': 'stockone'})
[2025-05-15 08:05:19,816] INFO _appmap.env: appmap enabled: False
[2025-05-15 08:05:22,473] INFO _appmap.configuration: file: /application/appmap.yml
[2025-05-15 08:05:22,474] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-05-15 08:05:22,474] INFO _appmap.configuration: env: environ({'APPROVAL_SERVICE_URL': 'http://************:8001/api/v1/', 'HOSTNAME': '33af9efc78a9', 'DEBUG': '1', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'HOME': '/root', 'DB_NAME': 'stockone', 'PYTHONUNBUFFERED': '1', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'CONN_MAX_AGE': '300', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'DB_REPLICA_PASS': 'stockone^123', 'TASK_USAGE': '0', 'APPROVAL_ORIGIN_NAME': 'dev-approval-app', 'ENTRYPOINT_FLAG': 'true', 'AWS_S3_REGION_NAME': 'ap-south-1', 'TERM': 'xterm', 'DB_REPLICA_HOST': 'db1', 'DB_REPLICA_USER': 'stockone', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'DB_REPORTS_PASS': 'stockone^123', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'LANG': 'C.UTF-8', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_REPORTS_HOST': 'db1', 'DB_REPORTS_USER': 'stockone', 'PYTHON_VERSION': '3.12.10', 'AWS_ID': '********************', 'DB_REPLICA_NAME': 'stockone', 'DB_PASS': 'stockone^123', 'USE_S3': 'TRUE', 'PWD': '/application', 'DB_HOST': 'db1', 'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'DB_USER': 'stockone', 'DB_REPORTS_NAME': 'stockone'})
[2025-05-15 08:05:22,477] INFO _appmap.env: appmap enabled: False
[2025-05-15 08:05:26,398] INFO _appmap.configuration: file: /application/appmap.yml
[2025-05-15 08:05:26,399] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-05-15 08:05:26,399] INFO _appmap.configuration: env: environ({'APPROVAL_SERVICE_URL': 'http://************:8001/api/v1/', 'HOSTNAME': '33af9efc78a9', 'DEBUG': '1', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'HOME': '/root', 'DB_NAME': 'stockone', 'PYTHONUNBUFFERED': '1', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'CONN_MAX_AGE': '300', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'DB_REPLICA_PASS': 'stockone^123', 'TASK_USAGE': '0', 'APPROVAL_ORIGIN_NAME': 'dev-approval-app', 'ENTRYPOINT_FLAG': 'true', 'AWS_S3_REGION_NAME': 'ap-south-1', 'TERM': 'xterm', 'DB_REPLICA_HOST': 'db1', 'DB_REPLICA_USER': 'stockone', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'DB_REPORTS_PASS': 'stockone^123', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'LANG': 'C.UTF-8', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_REPORTS_HOST': 'db1', 'DB_REPORTS_USER': 'stockone', 'PYTHON_VERSION': '3.12.10', 'AWS_ID': '********************', 'DB_REPLICA_NAME': 'stockone', 'DB_PASS': 'stockone^123', 'USE_S3': 'TRUE', 'PWD': '/application', 'DB_HOST': 'db1', 'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'DB_USER': 'stockone', 'DB_REPORTS_NAME': 'stockone'})
[2025-05-15 08:05:26,401] INFO _appmap.env: appmap enabled: False
[2025-05-15 08:05:26,477] INFO _appmap.env: appmap enabled: False
[2025-05-15 08:05:26,490] INFO _appmap.env: appmap enabled: False
[2025-05-15 08:07:16,399] INFO _appmap.configuration: file: /application/appmap.yml
[2025-05-15 08:07:16,406] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-05-15 08:07:16,406] INFO _appmap.configuration: env: environ({'APPROVAL_SERVICE_URL': 'http://************:8001/api/v1/', 'HOSTNAME': '4b7a70fc09c8', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DEBUG': '1', 'HOME': '/root', 'DB_NAME': 'stockone', 'PYTHONUNBUFFERED': '1', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'CONN_MAX_AGE': '300', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'DB_REPLICA_PASS': 'stockone^123', 'TASK_USAGE': '0', 'APPROVAL_ORIGIN_NAME': 'dev-approval-app', 'ENTRYPOINT_FLAG': 'true', 'AWS_S3_REGION_NAME': 'ap-south-1', 'TERM': 'xterm', 'DB_REPLICA_HOST': 'db1', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'DB_REPORTS_PASS': 'stockone^123', 'DB_REPLICA_USER': 'stockone', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'LANG': 'C.UTF-8', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_REPORTS_HOST': 'db1', 'DB_REPORTS_USER': 'stockone', 'PYTHON_VERSION': '3.12.10', 'DB_PASS': 'stockone^123', 'DB_REPLICA_NAME': 'stockone', 'AWS_ID': '********************', 'USE_S3': 'TRUE', 'PWD': '/application', 'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'DB_HOST': 'db1', 'DB_REPORTS_NAME': 'stockone', 'DB_USER': 'stockone'})
[2025-05-15 08:07:16,421] INFO _appmap.env: appmap enabled: False
[2025-05-15 08:07:22,933] INFO _appmap.configuration: file: /application/appmap.yml
[2025-05-15 08:07:22,933] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-05-15 08:07:22,933] INFO _appmap.configuration: env: environ({'APPROVAL_SERVICE_URL': 'http://************:8001/api/v1/', 'HOSTNAME': '4b7a70fc09c8', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DEBUG': '1', 'HOME': '/root', 'DB_NAME': 'stockone', 'PYTHONUNBUFFERED': '1', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'CONN_MAX_AGE': '300', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'DB_REPLICA_PASS': 'stockone^123', 'TASK_USAGE': '0', 'APPROVAL_ORIGIN_NAME': 'dev-approval-app', 'ENTRYPOINT_FLAG': 'true', 'AWS_S3_REGION_NAME': 'ap-south-1', 'TERM': 'xterm', 'DB_REPLICA_HOST': 'db1', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'DB_REPORTS_PASS': 'stockone^123', 'DB_REPLICA_USER': 'stockone', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'LANG': 'C.UTF-8', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_REPORTS_HOST': 'db1', 'DB_REPORTS_USER': 'stockone', 'PYTHON_VERSION': '3.12.10', 'DB_PASS': 'stockone^123', 'DB_REPLICA_NAME': 'stockone', 'AWS_ID': '********************', 'USE_S3': 'TRUE', 'PWD': '/application', 'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'DB_HOST': 'db1', 'DB_REPORTS_NAME': 'stockone', 'DB_USER': 'stockone'})
[2025-05-15 08:07:22,936] INFO _appmap.env: appmap enabled: False
[2025-05-15 08:07:25,605] INFO _appmap.configuration: file: /application/appmap.yml
[2025-05-15 08:07:25,606] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-05-15 08:07:25,606] INFO _appmap.configuration: env: environ({'APPROVAL_SERVICE_URL': 'http://************:8001/api/v1/', 'HOSTNAME': '4b7a70fc09c8', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DEBUG': '1', 'HOME': '/root', 'DB_NAME': 'stockone', 'PYTHONUNBUFFERED': '1', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'CONN_MAX_AGE': '300', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'DB_REPLICA_PASS': 'stockone^123', 'TASK_USAGE': '0', 'APPROVAL_ORIGIN_NAME': 'dev-approval-app', 'ENTRYPOINT_FLAG': 'true', 'AWS_S3_REGION_NAME': 'ap-south-1', 'TERM': 'xterm', 'DB_REPLICA_HOST': 'db1', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'DB_REPORTS_PASS': 'stockone^123', 'DB_REPLICA_USER': 'stockone', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'LANG': 'C.UTF-8', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_REPORTS_HOST': 'db1', 'DB_REPORTS_USER': 'stockone', 'PYTHON_VERSION': '3.12.10', 'DB_PASS': 'stockone^123', 'DB_REPLICA_NAME': 'stockone', 'AWS_ID': '********************', 'USE_S3': 'TRUE', 'PWD': '/application', 'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'DB_HOST': 'db1', 'DB_REPORTS_NAME': 'stockone', 'DB_USER': 'stockone'})
[2025-05-15 08:07:25,609] INFO _appmap.env: appmap enabled: False
[2025-05-15 08:07:28,695] INFO _appmap.configuration: file: /application/appmap.yml
[2025-05-15 08:07:28,695] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-05-15 08:07:28,695] INFO _appmap.configuration: env: environ({'APPROVAL_SERVICE_URL': 'http://************:8001/api/v1/', 'HOSTNAME': '4b7a70fc09c8', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DEBUG': '1', 'HOME': '/root', 'DB_NAME': 'stockone', 'PYTHONUNBUFFERED': '1', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'CONN_MAX_AGE': '300', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'DB_REPLICA_PASS': 'stockone^123', 'TASK_USAGE': '0', 'APPROVAL_ORIGIN_NAME': 'dev-approval-app', 'ENTRYPOINT_FLAG': 'true', 'AWS_S3_REGION_NAME': 'ap-south-1', 'TERM': 'xterm', 'DB_REPLICA_HOST': 'db1', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'DB_REPORTS_PASS': 'stockone^123', 'DB_REPLICA_USER': 'stockone', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'LANG': 'C.UTF-8', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_REPORTS_HOST': 'db1', 'DB_REPORTS_USER': 'stockone', 'PYTHON_VERSION': '3.12.10', 'DB_PASS': 'stockone^123', 'DB_REPLICA_NAME': 'stockone', 'AWS_ID': '********************', 'USE_S3': 'TRUE', 'PWD': '/application', 'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'DB_HOST': 'db1', 'DB_REPORTS_NAME': 'stockone', 'DB_USER': 'stockone'})
[2025-05-15 08:07:28,698] INFO _appmap.env: appmap enabled: False
[2025-05-15 08:07:32,992] INFO _appmap.configuration: file: /application/appmap.yml
[2025-05-15 08:07:32,992] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-05-15 08:07:32,992] INFO _appmap.configuration: env: environ({'APPROVAL_SERVICE_URL': 'http://************:8001/api/v1/', 'HOSTNAME': '4b7a70fc09c8', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DEBUG': '1', 'HOME': '/root', 'DB_NAME': 'stockone', 'PYTHONUNBUFFERED': '1', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'CONN_MAX_AGE': '300', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'DB_REPLICA_PASS': 'stockone^123', 'TASK_USAGE': '0', 'APPROVAL_ORIGIN_NAME': 'dev-approval-app', 'ENTRYPOINT_FLAG': 'true', 'AWS_S3_REGION_NAME': 'ap-south-1', 'TERM': 'xterm', 'DB_REPLICA_HOST': 'db1', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'DB_REPORTS_PASS': 'stockone^123', 'DB_REPLICA_USER': 'stockone', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'LANG': 'C.UTF-8', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_REPORTS_HOST': 'db1', 'DB_REPORTS_USER': 'stockone', 'PYTHON_VERSION': '3.12.10', 'DB_PASS': 'stockone^123', 'DB_REPLICA_NAME': 'stockone', 'AWS_ID': '********************', 'USE_S3': 'TRUE', 'PWD': '/application', 'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'DB_HOST': 'db1', 'DB_REPORTS_NAME': 'stockone', 'DB_USER': 'stockone'})
[2025-05-15 08:07:32,995] INFO _appmap.env: appmap enabled: False
[2025-05-15 08:07:33,071] INFO _appmap.env: appmap enabled: False
[2025-05-15 08:07:33,072] INFO _appmap.env: appmap enabled: False
[2025-05-15 08:15:04,768] INFO _appmap.configuration: file: /application/appmap.yml
[2025-05-15 08:15:04,769] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-05-15 08:15:04,769] INFO _appmap.configuration: env: environ({'APPROVAL_SERVICE_URL': 'http://************:8001/api/v1/', 'HOSTNAME': '4b7a70fc09c8', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DEBUG': '1', 'HOME': '/root', 'DB_NAME': 'stockone', 'PYTHONUNBUFFERED': '1', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'CONN_MAX_AGE': '300', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'DB_REPLICA_PASS': 'stockone^123', 'TASK_USAGE': '0', 'APPROVAL_ORIGIN_NAME': 'dev-approval-app', 'ENTRYPOINT_FLAG': 'true', 'AWS_S3_REGION_NAME': 'ap-south-1', 'TERM': 'xterm', 'DB_REPLICA_HOST': 'db1', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'DB_REPORTS_PASS': 'stockone^123', 'DB_REPLICA_USER': 'stockone', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'LANG': 'C.UTF-8', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_REPORTS_HOST': 'db1', 'DB_REPORTS_USER': 'stockone', 'PYTHON_VERSION': '3.12.10', 'DB_PASS': 'stockone^123', 'DB_REPLICA_NAME': 'stockone', 'AWS_ID': '********************', 'USE_S3': 'TRUE', 'PWD': '/application', 'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'DB_HOST': 'db1', 'DB_REPORTS_NAME': 'stockone', 'DB_USER': 'stockone'})
[2025-05-15 08:15:04,783] INFO _appmap.env: appmap enabled: False
[2025-05-15 08:15:11,388] INFO _appmap.configuration: file: /application/appmap.yml
[2025-05-15 08:15:11,388] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-05-15 08:15:11,388] INFO _appmap.configuration: env: environ({'APPROVAL_SERVICE_URL': 'http://************:8001/api/v1/', 'HOSTNAME': '4b7a70fc09c8', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DEBUG': '1', 'HOME': '/root', 'DB_NAME': 'stockone', 'PYTHONUNBUFFERED': '1', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'CONN_MAX_AGE': '300', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'DB_REPLICA_PASS': 'stockone^123', 'TASK_USAGE': '0', 'APPROVAL_ORIGIN_NAME': 'dev-approval-app', 'ENTRYPOINT_FLAG': 'true', 'AWS_S3_REGION_NAME': 'ap-south-1', 'TERM': 'xterm', 'DB_REPLICA_HOST': 'db1', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'DB_REPORTS_PASS': 'stockone^123', 'DB_REPLICA_USER': 'stockone', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'LANG': 'C.UTF-8', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_REPORTS_HOST': 'db1', 'DB_REPORTS_USER': 'stockone', 'PYTHON_VERSION': '3.12.10', 'DB_PASS': 'stockone^123', 'DB_REPLICA_NAME': 'stockone', 'AWS_ID': '********************', 'USE_S3': 'TRUE', 'PWD': '/application', 'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'DB_HOST': 'db1', 'DB_REPORTS_NAME': 'stockone', 'DB_USER': 'stockone'})
[2025-05-15 08:15:11,392] INFO _appmap.env: appmap enabled: False
[2025-05-15 08:15:14,240] INFO _appmap.configuration: file: /application/appmap.yml
[2025-05-15 08:15:14,240] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-05-15 08:15:14,240] INFO _appmap.configuration: env: environ({'APPROVAL_SERVICE_URL': 'http://************:8001/api/v1/', 'HOSTNAME': '4b7a70fc09c8', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DEBUG': '1', 'HOME': '/root', 'DB_NAME': 'stockone', 'PYTHONUNBUFFERED': '1', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'CONN_MAX_AGE': '300', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'DB_REPLICA_PASS': 'stockone^123', 'TASK_USAGE': '0', 'APPROVAL_ORIGIN_NAME': 'dev-approval-app', 'ENTRYPOINT_FLAG': 'true', 'AWS_S3_REGION_NAME': 'ap-south-1', 'TERM': 'xterm', 'DB_REPLICA_HOST': 'db1', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'DB_REPORTS_PASS': 'stockone^123', 'DB_REPLICA_USER': 'stockone', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'LANG': 'C.UTF-8', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_REPORTS_HOST': 'db1', 'DB_REPORTS_USER': 'stockone', 'PYTHON_VERSION': '3.12.10', 'DB_PASS': 'stockone^123', 'DB_REPLICA_NAME': 'stockone', 'AWS_ID': '********************', 'USE_S3': 'TRUE', 'PWD': '/application', 'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'DB_HOST': 'db1', 'DB_REPORTS_NAME': 'stockone', 'DB_USER': 'stockone'})
[2025-05-15 08:15:14,244] INFO _appmap.env: appmap enabled: False
[2025-05-15 08:15:17,061] INFO _appmap.configuration: file: /application/appmap.yml
[2025-05-15 08:15:17,061] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-05-15 08:15:17,061] INFO _appmap.configuration: env: environ({'APPROVAL_SERVICE_URL': 'http://************:8001/api/v1/', 'HOSTNAME': '4b7a70fc09c8', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DEBUG': '1', 'HOME': '/root', 'DB_NAME': 'stockone', 'PYTHONUNBUFFERED': '1', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'CONN_MAX_AGE': '300', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'DB_REPLICA_PASS': 'stockone^123', 'TASK_USAGE': '0', 'APPROVAL_ORIGIN_NAME': 'dev-approval-app', 'ENTRYPOINT_FLAG': 'true', 'AWS_S3_REGION_NAME': 'ap-south-1', 'TERM': 'xterm', 'DB_REPLICA_HOST': 'db1', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'DB_REPORTS_PASS': 'stockone^123', 'DB_REPLICA_USER': 'stockone', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'LANG': 'C.UTF-8', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_REPORTS_HOST': 'db1', 'DB_REPORTS_USER': 'stockone', 'PYTHON_VERSION': '3.12.10', 'DB_PASS': 'stockone^123', 'DB_REPLICA_NAME': 'stockone', 'AWS_ID': '********************', 'USE_S3': 'TRUE', 'PWD': '/application', 'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'DB_HOST': 'db1', 'DB_REPORTS_NAME': 'stockone', 'DB_USER': 'stockone'})
[2025-05-15 08:15:17,064] INFO _appmap.env: appmap enabled: False
[2025-05-15 08:15:22,080] INFO _appmap.configuration: file: /application/appmap.yml
[2025-05-15 08:15:22,080] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-05-15 08:15:22,080] INFO _appmap.configuration: env: environ({'APPROVAL_SERVICE_URL': 'http://************:8001/api/v1/', 'HOSTNAME': '4b7a70fc09c8', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DEBUG': '1', 'HOME': '/root', 'DB_NAME': 'stockone', 'PYTHONUNBUFFERED': '1', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'CONN_MAX_AGE': '300', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'DB_REPLICA_PASS': 'stockone^123', 'TASK_USAGE': '0', 'APPROVAL_ORIGIN_NAME': 'dev-approval-app', 'ENTRYPOINT_FLAG': 'true', 'AWS_S3_REGION_NAME': 'ap-south-1', 'TERM': 'xterm', 'DB_REPLICA_HOST': 'db1', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'DB_REPORTS_PASS': 'stockone^123', 'DB_REPLICA_USER': 'stockone', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'LANG': 'C.UTF-8', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_REPORTS_HOST': 'db1', 'DB_REPORTS_USER': 'stockone', 'PYTHON_VERSION': '3.12.10', 'DB_PASS': 'stockone^123', 'DB_REPLICA_NAME': 'stockone', 'AWS_ID': '********************', 'USE_S3': 'TRUE', 'PWD': '/application', 'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'DB_HOST': 'db1', 'DB_REPORTS_NAME': 'stockone', 'DB_USER': 'stockone'})
[2025-05-15 08:15:22,085] INFO _appmap.env: appmap enabled: False
[2025-05-15 08:15:22,204] INFO _appmap.env: appmap enabled: False
[2025-05-15 08:15:22,210] INFO _appmap.env: appmap enabled: False
[2025-05-15 12:51:35,187] INFO _appmap.configuration: file: /application/appmap.yml
[2025-05-15 12:51:35,187] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-05-15 12:51:35,187] INFO _appmap.configuration: env: environ({'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'PYTHONUNBUFFERED': '1', 'DB_REPLICA_USER': 'stockone', 'HOSTNAME': '4b7a70fc09c8', 'PYTHON_VERSION': '3.12.10', 'DB_REPORTS_USER': 'stockone', 'DB_REPLICA_HOST': 'db1', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'PWD': '/application', 'USE_S3': 'TRUE', 'DB_USER': 'stockone', 'HOME': '/root', 'LANG': 'C.UTF-8', 'DB_REPLICA_NAME': 'stockone', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'TERM': 'xterm', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_HOST': 'db1', 'AWS_ID': '********************', 'SHLVL': '1', 'DB_REPORTS_NAME': 'stockone', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DB_NAME': 'stockone', 'APPROVAL_SERVICE_URL': 'http://************:8001/api/v1/', 'TASK_USAGE': '0', 'DB_REPORTS_PASS': 'stockone^123', 'DB_REPORTS_HOST': 'db1', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'DB_REPLICA_PASS': 'stockone^123', 'AWS_S3_REGION_NAME': 'ap-south-1', 'DEBUG': '1', 'DB_PASS': 'stockone^123', 'ENTRYPOINT_FLAG': 'true', 'CONN_MAX_AGE': '300', 'APPROVAL_ORIGIN_NAME': 'dev-approval-app', '_': '/usr/local/bin/python'})
[2025-05-15 12:51:35,194] INFO _appmap.env: appmap enabled: False
[2025-05-15 13:03:26,637] INFO _appmap.env: appmap enabled: False
[2025-05-15 13:15:21,021] INFO _appmap.env: appmap enabled: False
[2025-05-15 13:15:32,576] INFO _appmap.env: appmap enabled: False
[2025-05-15 13:15:55,102] INFO _appmap.env: appmap enabled: False
[2025-05-15 13:16:12,482] INFO _appmap.env: appmap enabled: False
[2025-05-15 13:16:56,395] INFO _appmap.env: appmap enabled: False
[2025-05-15 13:17:35,512] INFO _appmap.env: appmap enabled: False
[2025-05-15 13:20:56,584] INFO _appmap.env: appmap enabled: False
[2025-05-15 13:21:00,811] INFO _appmap.env: appmap enabled: False
[2025-05-15 13:21:07,872] INFO _appmap.env: appmap enabled: False
[2025-05-15 13:23:21,088] INFO _appmap.env: appmap enabled: False
[2025-05-15 13:23:25,303] INFO _appmap.env: appmap enabled: False
[2025-05-15 13:23:34,142] INFO _appmap.env: appmap enabled: False
[2025-05-15 13:28:10,533] INFO _appmap.env: appmap enabled: False
[2025-05-15 13:30:01,456] INFO _appmap.env: appmap enabled: False
[2025-05-15 13:31:47,526] INFO _appmap.env: appmap enabled: False
[2025-05-15 13:33:43,917] INFO _appmap.env: appmap enabled: False
[2025-05-15 13:33:54,935] INFO _appmap.env: appmap enabled: False
[2025-05-15 13:33:56,498] INFO _appmap.env: appmap enabled: False
[2025-05-15 13:34:22,568] INFO _appmap.env: appmap enabled: False
[2025-05-15 13:34:25,051] INFO _appmap.env: appmap enabled: False
[2025-05-15 13:34:27,889] INFO _appmap.env: appmap enabled: False
[2025-05-15 13:34:49,306] INFO _appmap.env: appmap enabled: False
[2025-05-15 13:34:52,306] INFO _appmap.env: appmap enabled: False
[2025-05-15 13:34:59,455] INFO _appmap.env: appmap enabled: False
[2025-05-15 13:35:07,338] INFO _appmap.env: appmap enabled: False
[2025-05-15 13:37:22,604] INFO _appmap.env: appmap enabled: False
[2025-05-15 13:38:25,271] INFO _appmap.env: appmap enabled: False
[2025-05-15 13:39:18,248] INFO _appmap.env: appmap enabled: False
[2025-05-15 13:40:42,392] INFO _appmap.env: appmap enabled: False
[2025-05-15 13:40:46,141] INFO _appmap.env: appmap enabled: False
[2025-05-15 13:41:16,677] INFO _appmap.env: appmap enabled: False
[2025-05-15 13:41:30,788] INFO _appmap.env: appmap enabled: False
[2025-05-15 13:42:07,592] INFO _appmap.env: appmap enabled: False
[2025-05-15 13:42:22,220] INFO _appmap.env: appmap enabled: False
[2025-05-15 13:42:23,892] INFO _appmap.env: appmap enabled: False
[2025-05-15 13:42:30,508] INFO _appmap.env: appmap enabled: False
[2025-05-15 13:43:02,439] INFO _appmap.env: appmap enabled: False
[2025-05-15 13:43:05,180] INFO _appmap.env: appmap enabled: False
[2025-05-15 13:43:56,741] INFO _appmap.env: appmap enabled: False
[2025-05-15 13:43:59,231] INFO _appmap.env: appmap enabled: False
[2025-05-15 13:44:14,734] INFO _appmap.env: appmap enabled: False
[2025-05-15 13:44:51,209] INFO _appmap.env: appmap enabled: False
[2025-05-15 13:45:15,822] INFO _appmap.env: appmap enabled: False
[2025-05-15 13:45:32,135] INFO _appmap.env: appmap enabled: False
[2025-05-15 13:46:03,119] INFO _appmap.env: appmap enabled: False
[2025-05-15 13:46:03,873] INFO _appmap.env: appmap enabled: False
[2025-05-15 13:46:10,292] INFO _appmap.env: appmap enabled: False
[2025-05-15 13:46:22,048] INFO _appmap.env: appmap enabled: False
[2025-05-15 13:46:29,424] INFO _appmap.env: appmap enabled: False
[2025-05-15 13:46:54,729] INFO _appmap.env: appmap enabled: False
[2025-05-15 13:47:08,816] INFO _appmap.env: appmap enabled: False
[2025-05-15 13:47:10,575] INFO _appmap.env: appmap enabled: False
[2025-05-15 13:47:14,114] INFO _appmap.env: appmap enabled: False
[2025-05-15 13:47:22,418] INFO _appmap.env: appmap enabled: False
[2025-05-15 13:48:23,432] INFO _appmap.env: appmap enabled: False
[2025-05-15 13:48:31,808] INFO _appmap.env: appmap enabled: False
[2025-05-15 13:49:45,909] INFO _appmap.env: appmap enabled: False
[2025-05-15 13:50:03,704] INFO _appmap.env: appmap enabled: False
[2025-05-15 13:55:24,519] INFO _appmap.env: appmap enabled: False
[2025-05-15 13:57:42,641] INFO _appmap.env: appmap enabled: False
[2025-05-15 13:58:47,087] INFO _appmap.env: appmap enabled: False
[2025-05-15 13:58:56,245] INFO _appmap.env: appmap enabled: False
[2025-05-15 13:59:21,899] INFO _appmap.env: appmap enabled: False
[2025-05-15 13:59:24,030] INFO _appmap.env: appmap enabled: False
[2025-05-15 13:59:25,725] INFO _appmap.env: appmap enabled: False
[2025-05-15 13:59:29,195] INFO _appmap.env: appmap enabled: False
[2025-05-15 13:59:38,740] INFO _appmap.env: appmap enabled: False
[2025-05-15 13:59:42,021] INFO _appmap.env: appmap enabled: False
[2025-05-15 13:59:51,063] INFO _appmap.env: appmap enabled: False
[2025-05-15 14:02:03,940] INFO _appmap.env: appmap enabled: False
[2025-05-15 14:03:43,434] INFO _appmap.env: appmap enabled: False
[2025-05-15 14:03:44,275] INFO _appmap.env: appmap enabled: False
[2025-05-15 14:05:52,284] INFO _appmap.env: appmap enabled: False
[2025-05-15 14:06:25,652] INFO _appmap.env: appmap enabled: False
[2025-05-15 14:06:39,434] INFO _appmap.env: appmap enabled: False
[2025-05-15 14:11:55,939] INFO _appmap.env: appmap enabled: False
[2025-05-15 14:11:58,381] INFO _appmap.env: appmap enabled: False
[2025-05-16 06:34:22,083] INFO _appmap.env: appmap enabled: False
[2025-05-16 06:41:19,355] INFO _appmap.env: appmap enabled: False
[2025-05-16 06:51:28,299] INFO _appmap.configuration: file: /application/appmap.yml
[2025-05-16 06:51:28,299] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-05-16 06:51:28,299] INFO _appmap.configuration: env: environ({'APPROVAL_SERVICE_URL': 'http://************:8001/api/v1/', 'HOSTNAME': 'a5d5e07cc0fd', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DEBUG': '1', 'HOME': '/root', 'DB_NAME': 'stockone', 'PYTHONUNBUFFERED': '1', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'CONN_MAX_AGE': '300', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'DB_REPLICA_PASS': 'stockone^123', 'TASK_USAGE': '0', 'APPROVAL_ORIGIN_NAME': 'dev-approval-app', 'ENTRYPOINT_FLAG': 'true', 'AWS_S3_REGION_NAME': 'ap-south-1', 'TERM': 'xterm', 'DB_REPLICA_HOST': 'db1', 'DB_REPLICA_USER': 'stockone', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'DB_REPORTS_PASS': 'stockone^123', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'LANG': 'C.UTF-8', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_REPORTS_HOST': 'db1', 'DB_REPORTS_USER': 'stockone', 'PYTHON_VERSION': '3.12.10', 'AWS_ID': '********************', 'DB_PASS': 'stockone^123', 'DB_REPLICA_NAME': 'stockone', 'USE_S3': 'TRUE', 'PWD': '/application', 'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'DB_HOST': 'db1', 'DB_REPORTS_NAME': 'stockone', 'DB_USER': 'stockone'})
[2025-05-16 06:51:28,317] INFO _appmap.env: appmap enabled: False
[2025-05-16 06:51:39,034] INFO _appmap.configuration: file: /application/appmap.yml
[2025-05-16 06:51:39,034] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-05-16 06:51:39,034] INFO _appmap.configuration: env: environ({'APPROVAL_SERVICE_URL': 'http://************:8001/api/v1/', 'HOSTNAME': 'a5d5e07cc0fd', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DEBUG': '1', 'HOME': '/root', 'DB_NAME': 'stockone', 'PYTHONUNBUFFERED': '1', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'CONN_MAX_AGE': '300', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'DB_REPLICA_PASS': 'stockone^123', 'TASK_USAGE': '0', 'APPROVAL_ORIGIN_NAME': 'dev-approval-app', 'ENTRYPOINT_FLAG': 'true', 'AWS_S3_REGION_NAME': 'ap-south-1', 'TERM': 'xterm', 'DB_REPLICA_HOST': 'db1', 'DB_REPLICA_USER': 'stockone', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'DB_REPORTS_PASS': 'stockone^123', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'LANG': 'C.UTF-8', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_REPORTS_HOST': 'db1', 'DB_REPORTS_USER': 'stockone', 'PYTHON_VERSION': '3.12.10', 'AWS_ID': '********************', 'DB_PASS': 'stockone^123', 'DB_REPLICA_NAME': 'stockone', 'USE_S3': 'TRUE', 'PWD': '/application', 'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'DB_HOST': 'db1', 'DB_REPORTS_NAME': 'stockone', 'DB_USER': 'stockone'})
[2025-05-16 06:51:39,038] INFO _appmap.env: appmap enabled: False
[2025-05-16 06:51:42,496] INFO _appmap.configuration: file: /application/appmap.yml
[2025-05-16 06:51:42,496] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-05-16 06:51:42,497] INFO _appmap.configuration: env: environ({'APPROVAL_SERVICE_URL': 'http://************:8001/api/v1/', 'HOSTNAME': 'a5d5e07cc0fd', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DEBUG': '1', 'HOME': '/root', 'DB_NAME': 'stockone', 'PYTHONUNBUFFERED': '1', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'CONN_MAX_AGE': '300', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'DB_REPLICA_PASS': 'stockone^123', 'TASK_USAGE': '0', 'APPROVAL_ORIGIN_NAME': 'dev-approval-app', 'ENTRYPOINT_FLAG': 'true', 'AWS_S3_REGION_NAME': 'ap-south-1', 'TERM': 'xterm', 'DB_REPLICA_HOST': 'db1', 'DB_REPLICA_USER': 'stockone', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'DB_REPORTS_PASS': 'stockone^123', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'LANG': 'C.UTF-8', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_REPORTS_HOST': 'db1', 'DB_REPORTS_USER': 'stockone', 'PYTHON_VERSION': '3.12.10', 'AWS_ID': '********************', 'DB_PASS': 'stockone^123', 'DB_REPLICA_NAME': 'stockone', 'USE_S3': 'TRUE', 'PWD': '/application', 'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'DB_HOST': 'db1', 'DB_REPORTS_NAME': 'stockone', 'DB_USER': 'stockone'})
[2025-05-16 06:51:42,500] INFO _appmap.env: appmap enabled: False
[2025-05-16 06:51:45,659] INFO _appmap.configuration: file: /application/appmap.yml
[2025-05-16 06:51:45,659] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-05-16 06:51:45,659] INFO _appmap.configuration: env: environ({'APPROVAL_SERVICE_URL': 'http://************:8001/api/v1/', 'HOSTNAME': 'a5d5e07cc0fd', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DEBUG': '1', 'HOME': '/root', 'DB_NAME': 'stockone', 'PYTHONUNBUFFERED': '1', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'CONN_MAX_AGE': '300', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'DB_REPLICA_PASS': 'stockone^123', 'TASK_USAGE': '0', 'APPROVAL_ORIGIN_NAME': 'dev-approval-app', 'ENTRYPOINT_FLAG': 'true', 'AWS_S3_REGION_NAME': 'ap-south-1', 'TERM': 'xterm', 'DB_REPLICA_HOST': 'db1', 'DB_REPLICA_USER': 'stockone', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'DB_REPORTS_PASS': 'stockone^123', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'LANG': 'C.UTF-8', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_REPORTS_HOST': 'db1', 'DB_REPORTS_USER': 'stockone', 'PYTHON_VERSION': '3.12.10', 'AWS_ID': '********************', 'DB_PASS': 'stockone^123', 'DB_REPLICA_NAME': 'stockone', 'USE_S3': 'TRUE', 'PWD': '/application', 'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'DB_HOST': 'db1', 'DB_REPORTS_NAME': 'stockone', 'DB_USER': 'stockone'})
[2025-05-16 06:51:45,662] INFO _appmap.env: appmap enabled: False
[2025-05-16 06:51:49,958] INFO _appmap.configuration: file: /application/appmap.yml
[2025-05-16 06:51:49,958] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-05-16 06:51:49,958] INFO _appmap.configuration: env: environ({'APPROVAL_SERVICE_URL': 'http://************:8001/api/v1/', 'HOSTNAME': 'a5d5e07cc0fd', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DEBUG': '1', 'HOME': '/root', 'DB_NAME': 'stockone', 'PYTHONUNBUFFERED': '1', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'CONN_MAX_AGE': '300', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'DB_REPLICA_PASS': 'stockone^123', 'TASK_USAGE': '0', 'APPROVAL_ORIGIN_NAME': 'dev-approval-app', 'ENTRYPOINT_FLAG': 'true', 'AWS_S3_REGION_NAME': 'ap-south-1', 'TERM': 'xterm', 'DB_REPLICA_HOST': 'db1', 'DB_REPLICA_USER': 'stockone', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'DB_REPORTS_PASS': 'stockone^123', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'LANG': 'C.UTF-8', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_REPORTS_HOST': 'db1', 'DB_REPORTS_USER': 'stockone', 'PYTHON_VERSION': '3.12.10', 'AWS_ID': '********************', 'DB_PASS': 'stockone^123', 'DB_REPLICA_NAME': 'stockone', 'USE_S3': 'TRUE', 'PWD': '/application', 'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'DB_HOST': 'db1', 'DB_REPORTS_NAME': 'stockone', 'DB_USER': 'stockone'})
[2025-05-16 06:51:49,962] INFO _appmap.env: appmap enabled: False
[2025-05-16 06:51:50,040] INFO _appmap.env: appmap enabled: False
[2025-05-16 06:51:50,051] INFO _appmap.env: appmap enabled: False
[2025-05-16 06:51:59,182] INFO _appmap.env: appmap enabled: False
[2025-05-16 06:52:05,892] INFO _appmap.env: appmap enabled: False
[2025-05-16 06:52:09,499] INFO _appmap.env: appmap enabled: False
[2025-05-16 06:52:36,024] INFO _appmap.env: appmap enabled: False
[2025-05-16 06:52:44,000] INFO _appmap.env: appmap enabled: False
[2025-05-16 06:52:54,652] INFO _appmap.env: appmap enabled: False
[2025-05-16 06:53:13,205] INFO _appmap.env: appmap enabled: False
[2025-05-16 06:53:18,807] INFO _appmap.env: appmap enabled: False
[2025-05-16 06:53:21,079] INFO _appmap.env: appmap enabled: False
[2025-05-16 06:53:28,654] INFO _appmap.env: appmap enabled: False
[2025-05-16 06:53:33,430] INFO _appmap.env: appmap enabled: False
[2025-05-16 06:53:57,654] INFO _appmap.env: appmap enabled: False
[2025-05-16 06:53:59,002] INFO _appmap.env: appmap enabled: False
[2025-05-16 06:54:06,258] INFO _appmap.env: appmap enabled: False
[2025-05-16 06:54:15,589] INFO _appmap.env: appmap enabled: False
[2025-05-16 06:54:21,404] INFO _appmap.env: appmap enabled: False
[2025-05-16 06:54:28,950] INFO _appmap.env: appmap enabled: False
[2025-05-16 06:55:07,040] INFO _appmap.env: appmap enabled: False
[2025-05-16 06:55:15,961] INFO _appmap.env: appmap enabled: False
[2025-05-16 06:55:50,498] INFO _appmap.env: appmap enabled: False
[2025-05-16 06:55:54,696] INFO _appmap.env: appmap enabled: False
[2025-05-16 06:56:45,830] INFO _appmap.env: appmap enabled: False
[2025-05-16 06:56:50,992] INFO _appmap.env: appmap enabled: False
[2025-05-16 06:56:56,219] INFO _appmap.env: appmap enabled: False
[2025-05-16 06:56:58,891] INFO _appmap.env: appmap enabled: False
[2025-05-16 06:57:01,857] INFO _appmap.env: appmap enabled: False
[2025-05-16 06:57:06,387] INFO _appmap.env: appmap enabled: False
[2025-05-16 06:57:08,617] INFO _appmap.env: appmap enabled: False
[2025-05-16 06:57:13,021] INFO _appmap.env: appmap enabled: False
[2025-05-16 06:58:19,667] INFO _appmap.env: appmap enabled: False
[2025-05-16 06:58:23,137] INFO _appmap.env: appmap enabled: False
[2025-05-16 06:58:25,315] INFO _appmap.env: appmap enabled: False
[2025-05-16 06:58:48,632] INFO _appmap.env: appmap enabled: False
[2025-05-16 06:58:58,406] INFO _appmap.env: appmap enabled: False
[2025-05-16 06:59:07,396] INFO _appmap.env: appmap enabled: False
[2025-05-16 06:59:08,287] INFO _appmap.env: appmap enabled: False
[2025-05-16 06:59:09,655] INFO _appmap.env: appmap enabled: False
[2025-05-16 06:59:10,971] INFO _appmap.env: appmap enabled: False
[2025-05-16 07:00:01,267] INFO _appmap.env: appmap enabled: False
[2025-05-16 07:00:03,078] INFO _appmap.env: appmap enabled: False
[2025-05-16 07:00:04,933] INFO _appmap.env: appmap enabled: False
[2025-05-16 07:00:35,305] INFO _appmap.env: appmap enabled: False
[2025-05-16 07:00:36,127] INFO _appmap.env: appmap enabled: False
[2025-05-16 07:00:42,878] INFO _appmap.env: appmap enabled: False
[2025-05-16 07:08:53,775] INFO _appmap.env: appmap enabled: False
[2025-05-16 07:09:16,633] INFO _appmap.env: appmap enabled: False
[2025-05-16 07:09:19,244] INFO _appmap.env: appmap enabled: False
[2025-05-16 07:31:06,236] INFO _appmap.env: appmap enabled: False
[2025-05-16 07:31:53,419] INFO _appmap.env: appmap enabled: False
[2025-05-16 07:31:54,259] INFO _appmap.env: appmap enabled: False
[2025-05-16 07:31:55,546] INFO _appmap.env: appmap enabled: False
[2025-05-16 07:32:35,126] INFO _appmap.env: appmap enabled: False
[2025-05-16 07:32:39,166] INFO _appmap.env: appmap enabled: False
[2025-05-16 07:32:40,795] INFO _appmap.env: appmap enabled: False
[2025-05-16 07:32:51,099] INFO _appmap.env: appmap enabled: False
[2025-05-16 07:33:13,682] INFO _appmap.env: appmap enabled: False
[2025-05-16 07:35:00,931] INFO _appmap.env: appmap enabled: False
[2025-05-16 07:35:24,896] INFO _appmap.env: appmap enabled: False
[2025-05-16 07:36:02,278] INFO _appmap.env: appmap enabled: False
[2025-05-16 07:36:12,103] INFO _appmap.env: appmap enabled: False
[2025-05-16 07:36:25,402] INFO _appmap.env: appmap enabled: False
[2025-05-16 07:37:30,657] INFO _appmap.env: appmap enabled: False
[2025-05-16 07:37:41,654] INFO _appmap.env: appmap enabled: False
[2025-05-16 07:38:17,611] INFO _appmap.env: appmap enabled: False
[2025-05-16 07:38:43,903] INFO _appmap.env: appmap enabled: False
[2025-05-16 07:38:54,654] INFO _appmap.env: appmap enabled: False
[2025-05-16 07:39:04,329] INFO _appmap.env: appmap enabled: False
[2025-05-16 07:39:14,363] INFO _appmap.env: appmap enabled: False
[2025-05-16 07:39:50,849] INFO _appmap.env: appmap enabled: False
[2025-05-16 07:39:54,396] INFO _appmap.env: appmap enabled: False
[2025-05-16 07:41:27,400] INFO _appmap.env: appmap enabled: False
[2025-05-16 07:41:28,900] INFO _appmap.env: appmap enabled: False
[2025-05-16 07:41:45,985] INFO _appmap.env: appmap enabled: False
[2025-05-16 07:42:01,386] INFO _appmap.env: appmap enabled: False
[2025-05-16 07:42:08,694] INFO _appmap.env: appmap enabled: False
[2025-05-16 07:43:23,457] INFO _appmap.env: appmap enabled: False
[2025-05-16 07:43:25,215] INFO _appmap.env: appmap enabled: False
[2025-05-16 07:43:29,724] INFO _appmap.env: appmap enabled: False
[2025-05-16 07:43:38,541] INFO _appmap.env: appmap enabled: False
[2025-05-16 07:43:39,550] INFO _appmap.env: appmap enabled: False
[2025-05-16 07:43:41,737] INFO _appmap.env: appmap enabled: False
[2025-05-16 07:43:46,974] INFO _appmap.env: appmap enabled: False
[2025-05-16 07:45:43,607] INFO _appmap.env: appmap enabled: False
[2025-05-16 07:47:51,433] INFO _appmap.env: appmap enabled: False
[2025-05-16 07:47:53,333] INFO _appmap.env: appmap enabled: False
[2025-05-16 07:49:02,835] INFO _appmap.env: appmap enabled: False
[2025-05-16 07:49:28,610] INFO _appmap.env: appmap enabled: False
[2025-05-16 07:51:19,346] INFO _appmap.env: appmap enabled: False
[2025-05-16 07:51:43,533] INFO _appmap.env: appmap enabled: False
[2025-05-16 07:51:54,349] INFO _appmap.env: appmap enabled: False
[2025-05-16 07:52:14,349] INFO _appmap.env: appmap enabled: False
[2025-05-16 07:52:21,358] INFO _appmap.env: appmap enabled: False
[2025-05-16 07:52:40,182] INFO _appmap.env: appmap enabled: False
[2025-05-16 07:52:47,555] INFO _appmap.env: appmap enabled: False
[2025-05-16 07:53:02,871] INFO _appmap.env: appmap enabled: False
[2025-05-16 07:53:06,041] INFO _appmap.env: appmap enabled: False
[2025-05-16 07:53:13,881] INFO _appmap.env: appmap enabled: False
[2025-05-16 07:54:10,463] INFO _appmap.env: appmap enabled: False
[2025-05-16 07:54:11,495] INFO _appmap.env: appmap enabled: False
[2025-05-16 07:54:21,256] INFO _appmap.env: appmap enabled: False
[2025-05-16 07:54:29,815] INFO _appmap.env: appmap enabled: False
[2025-05-16 07:54:37,602] INFO _appmap.env: appmap enabled: False
[2025-05-16 07:55:04,407] INFO _appmap.env: appmap enabled: False
[2025-05-16 08:02:53,350] INFO _appmap.env: appmap enabled: False
[2025-05-16 08:05:06,437] INFO _appmap.env: appmap enabled: False
[2025-05-16 08:06:52,302] INFO _appmap.env: appmap enabled: False
[2025-05-16 08:07:13,597] INFO _appmap.env: appmap enabled: False
[2025-05-16 08:07:36,494] INFO _appmap.env: appmap enabled: False
[2025-05-16 08:10:10,567] INFO _appmap.env: appmap enabled: False
[2025-05-16 08:10:39,852] INFO _appmap.env: appmap enabled: False
[2025-05-16 08:11:16,292] INFO _appmap.env: appmap enabled: False
[2025-05-16 08:12:05,670] INFO _appmap.env: appmap enabled: False
[2025-05-16 08:16:23,116] INFO _appmap.configuration: file: /application/appmap.yml
[2025-05-16 08:16:23,119] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-05-16 08:16:23,119] INFO _appmap.configuration: env: environ({'APPROVAL_SERVICE_URL': 'http://************:8001/api/v1/', 'HOSTNAME': 'f10640ce7b82', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DEBUG': '1', 'HOME': '/root', 'DB_NAME': 'stockone', 'PYTHONUNBUFFERED': '1', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'CONN_MAX_AGE': '300', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'DB_REPLICA_PASS': 'stockone^123', 'TASK_USAGE': '0', 'APPROVAL_ORIGIN_NAME': 'dev-approval-app', 'ENTRYPOINT_FLAG': 'true', 'AWS_S3_REGION_NAME': 'ap-south-1', 'TERM': 'xterm', 'DB_REPLICA_HOST': 'db1', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'DB_REPLICA_USER': 'stockone', 'DB_REPORTS_PASS': 'stockone^123', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'LANG': 'C.UTF-8', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_REPORTS_HOST': 'db1', 'DB_REPORTS_USER': 'stockone', 'PYTHON_VERSION': '3.12.10', 'AWS_ID': '********************', 'DB_PASS': 'stockone^123', 'DB_REPLICA_NAME': 'stockone', 'USE_S3': 'TRUE', 'PWD': '/application', 'DB_HOST': 'db1', 'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'DB_USER': 'stockone', 'DB_REPORTS_NAME': 'stockone'})
[2025-05-16 08:16:23,155] INFO _appmap.env: appmap enabled: False
[2025-05-16 08:16:31,997] INFO _appmap.configuration: file: /application/appmap.yml
[2025-05-16 08:16:31,997] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-05-16 08:16:31,997] INFO _appmap.configuration: env: environ({'APPROVAL_SERVICE_URL': 'http://************:8001/api/v1/', 'HOSTNAME': 'f10640ce7b82', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DEBUG': '1', 'HOME': '/root', 'DB_NAME': 'stockone', 'PYTHONUNBUFFERED': '1', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'CONN_MAX_AGE': '300', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'DB_REPLICA_PASS': 'stockone^123', 'TASK_USAGE': '0', 'APPROVAL_ORIGIN_NAME': 'dev-approval-app', 'ENTRYPOINT_FLAG': 'true', 'AWS_S3_REGION_NAME': 'ap-south-1', 'TERM': 'xterm', 'DB_REPLICA_HOST': 'db1', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'DB_REPLICA_USER': 'stockone', 'DB_REPORTS_PASS': 'stockone^123', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'LANG': 'C.UTF-8', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_REPORTS_HOST': 'db1', 'DB_REPORTS_USER': 'stockone', 'PYTHON_VERSION': '3.12.10', 'AWS_ID': '********************', 'DB_PASS': 'stockone^123', 'DB_REPLICA_NAME': 'stockone', 'USE_S3': 'TRUE', 'PWD': '/application', 'DB_HOST': 'db1', 'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'DB_USER': 'stockone', 'DB_REPORTS_NAME': 'stockone'})
[2025-05-16 08:16:32,001] INFO _appmap.env: appmap enabled: False
[2025-05-16 08:16:35,288] INFO _appmap.configuration: file: /application/appmap.yml
[2025-05-16 08:16:35,288] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-05-16 08:16:35,288] INFO _appmap.configuration: env: environ({'APPROVAL_SERVICE_URL': 'http://************:8001/api/v1/', 'HOSTNAME': 'f10640ce7b82', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DEBUG': '1', 'HOME': '/root', 'DB_NAME': 'stockone', 'PYTHONUNBUFFERED': '1', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'CONN_MAX_AGE': '300', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'DB_REPLICA_PASS': 'stockone^123', 'TASK_USAGE': '0', 'APPROVAL_ORIGIN_NAME': 'dev-approval-app', 'ENTRYPOINT_FLAG': 'true', 'AWS_S3_REGION_NAME': 'ap-south-1', 'TERM': 'xterm', 'DB_REPLICA_HOST': 'db1', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'DB_REPLICA_USER': 'stockone', 'DB_REPORTS_PASS': 'stockone^123', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'LANG': 'C.UTF-8', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_REPORTS_HOST': 'db1', 'DB_REPORTS_USER': 'stockone', 'PYTHON_VERSION': '3.12.10', 'AWS_ID': '********************', 'DB_PASS': 'stockone^123', 'DB_REPLICA_NAME': 'stockone', 'USE_S3': 'TRUE', 'PWD': '/application', 'DB_HOST': 'db1', 'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'DB_USER': 'stockone', 'DB_REPORTS_NAME': 'stockone'})
[2025-05-16 08:16:35,292] INFO _appmap.env: appmap enabled: False
[2025-05-16 08:16:39,346] INFO _appmap.configuration: file: /application/appmap.yml
[2025-05-16 08:16:39,347] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-05-16 08:16:39,347] INFO _appmap.configuration: env: environ({'APPROVAL_SERVICE_URL': 'http://************:8001/api/v1/', 'HOSTNAME': 'f10640ce7b82', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DEBUG': '1', 'HOME': '/root', 'DB_NAME': 'stockone', 'PYTHONUNBUFFERED': '1', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'CONN_MAX_AGE': '300', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'DB_REPLICA_PASS': 'stockone^123', 'TASK_USAGE': '0', 'APPROVAL_ORIGIN_NAME': 'dev-approval-app', 'ENTRYPOINT_FLAG': 'true', 'AWS_S3_REGION_NAME': 'ap-south-1', 'TERM': 'xterm', 'DB_REPLICA_HOST': 'db1', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'DB_REPLICA_USER': 'stockone', 'DB_REPORTS_PASS': 'stockone^123', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'LANG': 'C.UTF-8', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_REPORTS_HOST': 'db1', 'DB_REPORTS_USER': 'stockone', 'PYTHON_VERSION': '3.12.10', 'AWS_ID': '********************', 'DB_PASS': 'stockone^123', 'DB_REPLICA_NAME': 'stockone', 'USE_S3': 'TRUE', 'PWD': '/application', 'DB_HOST': 'db1', 'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'DB_USER': 'stockone', 'DB_REPORTS_NAME': 'stockone'})
[2025-05-16 08:16:39,350] INFO _appmap.env: appmap enabled: False
[2025-05-16 08:16:43,914] INFO _appmap.configuration: file: /application/appmap.yml
[2025-05-16 08:16:43,914] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-05-16 08:16:43,914] INFO _appmap.configuration: env: environ({'APPROVAL_SERVICE_URL': 'http://************:8001/api/v1/', 'HOSTNAME': 'f10640ce7b82', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DEBUG': '1', 'HOME': '/root', 'DB_NAME': 'stockone', 'PYTHONUNBUFFERED': '1', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'CONN_MAX_AGE': '300', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'DB_REPLICA_PASS': 'stockone^123', 'TASK_USAGE': '0', 'APPROVAL_ORIGIN_NAME': 'dev-approval-app', 'ENTRYPOINT_FLAG': 'true', 'AWS_S3_REGION_NAME': 'ap-south-1', 'TERM': 'xterm', 'DB_REPLICA_HOST': 'db1', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'DB_REPLICA_USER': 'stockone', 'DB_REPORTS_PASS': 'stockone^123', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'LANG': 'C.UTF-8', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_REPORTS_HOST': 'db1', 'DB_REPORTS_USER': 'stockone', 'PYTHON_VERSION': '3.12.10', 'AWS_ID': '********************', 'DB_PASS': 'stockone^123', 'DB_REPLICA_NAME': 'stockone', 'USE_S3': 'TRUE', 'PWD': '/application', 'DB_HOST': 'db1', 'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'DB_USER': 'stockone', 'DB_REPORTS_NAME': 'stockone'})
[2025-05-16 08:16:43,917] INFO _appmap.env: appmap enabled: False
[2025-05-16 08:16:43,998] INFO _appmap.env: appmap enabled: False
[2025-05-16 08:16:44,010] INFO _appmap.env: appmap enabled: False
[2025-05-16 08:25:23,267] INFO _appmap.env: appmap enabled: False
[2025-05-16 08:28:01,880] INFO _appmap.env: appmap enabled: False
[2025-05-16 08:28:04,874] INFO _appmap.env: appmap enabled: False
[2025-05-16 08:30:20,164] INFO _appmap.env: appmap enabled: False
[2025-05-16 08:30:39,359] INFO _appmap.env: appmap enabled: False
[2025-05-16 08:43:09,164] INFO _appmap.env: appmap enabled: False
[2025-05-16 08:44:10,294] INFO _appmap.env: appmap enabled: False
[2025-05-16 08:44:11,759] INFO _appmap.env: appmap enabled: False
[2025-05-16 08:44:14,498] INFO _appmap.env: appmap enabled: False
[2025-05-16 08:44:16,404] INFO _appmap.env: appmap enabled: False
[2025-05-16 08:44:21,920] INFO _appmap.env: appmap enabled: False
[2025-05-16 08:44:23,364] INFO _appmap.env: appmap enabled: False
[2025-05-16 08:44:32,118] INFO _appmap.env: appmap enabled: False
[2025-05-16 08:44:33,948] INFO _appmap.env: appmap enabled: False
[2025-05-16 08:44:39,756] INFO _appmap.env: appmap enabled: False
[2025-05-16 08:44:41,064] INFO _appmap.env: appmap enabled: False
[2025-05-19 13:04:48,210] INFO _appmap.env: appmap enabled: False
[2025-05-19 13:06:52,104] INFO _appmap.configuration: file: /application/appmap.yml
[2025-05-19 13:06:52,108] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-05-19 13:06:52,109] INFO _appmap.configuration: env: environ({'APPROVAL_SERVICE_URL': 'http://************:8001/api/v1/', 'HOSTNAME': 'cc4f03ba0bbe', 'DEBUG': '1', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'HOME': '/root', 'DB_NAME': 'stockone', 'PYTHONUNBUFFERED': '1', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'CONN_MAX_AGE': '300', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'DB_REPLICA_PASS': 'stockone^123', 'TASK_USAGE': '0', 'APPROVAL_ORIGIN_NAME': 'dev-approval-app', 'ENTRYPOINT_FLAG': 'true', 'AWS_S3_REGION_NAME': 'ap-south-1', 'TERM': 'xterm', 'DB_REPLICA_HOST': 'db1', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'DB_REPLICA_USER': 'stockone', 'DB_REPORTS_PASS': 'stockone^123', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'LANG': 'C.UTF-8', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_REPORTS_HOST': 'db1', 'DB_REPORTS_USER': 'stockone', 'PYTHON_VERSION': '3.12.10', 'DB_PASS': 'stockone^123', 'AWS_ID': '********************', 'DB_REPLICA_NAME': 'stockone', 'USE_S3': 'TRUE', 'PWD': '/application', 'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'DB_HOST': 'db1', 'DB_REPORTS_NAME': 'stockone', 'DB_USER': 'stockone'})
[2025-05-19 13:06:52,172] INFO _appmap.env: appmap enabled: False
[2025-05-19 13:07:37,022] INFO _appmap.configuration: file: /application/appmap.yml
[2025-05-19 13:07:37,023] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-05-19 13:07:37,023] INFO _appmap.configuration: env: environ({'APPROVAL_SERVICE_URL': 'http://************:8001/api/v1/', 'HOSTNAME': 'cc4f03ba0bbe', 'DEBUG': '1', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'HOME': '/root', 'DB_NAME': 'stockone', 'PYTHONUNBUFFERED': '1', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'CONN_MAX_AGE': '300', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'DB_REPLICA_PASS': 'stockone^123', 'TASK_USAGE': '0', 'APPROVAL_ORIGIN_NAME': 'dev-approval-app', 'ENTRYPOINT_FLAG': 'true', 'AWS_S3_REGION_NAME': 'ap-south-1', 'TERM': 'xterm', 'DB_REPLICA_HOST': 'db1', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'DB_REPLICA_USER': 'stockone', 'DB_REPORTS_PASS': 'stockone^123', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'LANG': 'C.UTF-8', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_REPORTS_HOST': 'db1', 'DB_REPORTS_USER': 'stockone', 'PYTHON_VERSION': '3.12.10', 'DB_PASS': 'stockone^123', 'AWS_ID': '********************', 'DB_REPLICA_NAME': 'stockone', 'USE_S3': 'TRUE', 'PWD': '/application', 'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'DB_HOST': 'db1', 'DB_REPORTS_NAME': 'stockone', 'DB_USER': 'stockone'})
[2025-05-19 13:07:37,027] INFO _appmap.env: appmap enabled: False
[2025-05-19 13:09:00,780] INFO _appmap.configuration: file: /application/appmap.yml
[2025-05-19 13:09:00,780] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-05-19 13:09:00,781] INFO _appmap.configuration: env: environ({'APPROVAL_SERVICE_URL': 'http://************:8001/api/v1/', 'HOSTNAME': 'cc4f03ba0bbe', 'DEBUG': '1', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'HOME': '/root', 'DB_NAME': 'stockone', 'PYTHONUNBUFFERED': '1', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'CONN_MAX_AGE': '300', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'DB_REPLICA_PASS': 'stockone^123', 'TASK_USAGE': '0', 'APPROVAL_ORIGIN_NAME': 'dev-approval-app', 'ENTRYPOINT_FLAG': 'true', 'AWS_S3_REGION_NAME': 'ap-south-1', 'TERM': 'xterm', 'DB_REPLICA_HOST': 'db1', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'DB_REPLICA_USER': 'stockone', 'DB_REPORTS_PASS': 'stockone^123', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'LANG': 'C.UTF-8', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_REPORTS_HOST': 'db1', 'DB_REPORTS_USER': 'stockone', 'PYTHON_VERSION': '3.12.10', 'DB_PASS': 'stockone^123', 'AWS_ID': '********************', 'DB_REPLICA_NAME': 'stockone', 'USE_S3': 'TRUE', 'PWD': '/application', 'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'DB_HOST': 'db1', 'DB_REPORTS_NAME': 'stockone', 'DB_USER': 'stockone'})
[2025-05-19 13:09:00,792] INFO _appmap.env: appmap enabled: False
[2025-05-19 13:09:13,338] INFO _appmap.configuration: file: /application/appmap.yml
[2025-05-19 13:09:13,338] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-05-19 13:09:13,338] INFO _appmap.configuration: env: environ({'APPROVAL_SERVICE_URL': 'http://************:8001/api/v1/', 'HOSTNAME': 'cc4f03ba0bbe', 'DEBUG': '1', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'HOME': '/root', 'DB_NAME': 'stockone', 'PYTHONUNBUFFERED': '1', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'CONN_MAX_AGE': '300', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'DB_REPLICA_PASS': 'stockone^123', 'TASK_USAGE': '0', 'APPROVAL_ORIGIN_NAME': 'dev-approval-app', 'ENTRYPOINT_FLAG': 'true', 'AWS_S3_REGION_NAME': 'ap-south-1', 'TERM': 'xterm', 'DB_REPLICA_HOST': 'db1', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'DB_REPLICA_USER': 'stockone', 'DB_REPORTS_PASS': 'stockone^123', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'LANG': 'C.UTF-8', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_REPORTS_HOST': 'db1', 'DB_REPORTS_USER': 'stockone', 'PYTHON_VERSION': '3.12.10', 'DB_PASS': 'stockone^123', 'AWS_ID': '********************', 'DB_REPLICA_NAME': 'stockone', 'USE_S3': 'TRUE', 'PWD': '/application', 'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'DB_HOST': 'db1', 'DB_REPORTS_NAME': 'stockone', 'DB_USER': 'stockone'})
[2025-05-19 13:09:13,342] INFO _appmap.env: appmap enabled: False
[2025-05-19 13:09:23,904] INFO _appmap.configuration: file: /application/appmap.yml
[2025-05-19 13:09:23,904] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-05-19 13:09:23,904] INFO _appmap.configuration: env: environ({'APPROVAL_SERVICE_URL': 'http://************:8001/api/v1/', 'HOSTNAME': 'cc4f03ba0bbe', 'DEBUG': '1', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'HOME': '/root', 'DB_NAME': 'stockone', 'PYTHONUNBUFFERED': '1', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'CONN_MAX_AGE': '300', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'DB_REPLICA_PASS': 'stockone^123', 'TASK_USAGE': '0', 'APPROVAL_ORIGIN_NAME': 'dev-approval-app', 'ENTRYPOINT_FLAG': 'true', 'AWS_S3_REGION_NAME': 'ap-south-1', 'TERM': 'xterm', 'DB_REPLICA_HOST': 'db1', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'DB_REPLICA_USER': 'stockone', 'DB_REPORTS_PASS': 'stockone^123', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'LANG': 'C.UTF-8', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_REPORTS_HOST': 'db1', 'DB_REPORTS_USER': 'stockone', 'PYTHON_VERSION': '3.12.10', 'DB_PASS': 'stockone^123', 'AWS_ID': '********************', 'DB_REPLICA_NAME': 'stockone', 'USE_S3': 'TRUE', 'PWD': '/application', 'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'DB_HOST': 'db1', 'DB_REPORTS_NAME': 'stockone', 'DB_USER': 'stockone'})
[2025-05-19 13:09:23,912] INFO _appmap.env: appmap enabled: False
[2025-05-19 13:09:24,049] INFO _appmap.env: appmap enabled: False
[2025-05-19 13:09:24,059] INFO _appmap.env: appmap enabled: False
[2025-05-19 13:19:20,484] INFO _appmap.env: appmap enabled: False
[2025-05-19 13:19:31,427] INFO _appmap.env: appmap enabled: False
[2025-05-19 13:22:10,267] INFO _appmap.env: appmap enabled: False
[2025-05-19 13:31:25,712] INFO _appmap.env: appmap enabled: False
[2025-05-19 13:35:05,465] INFO _appmap.env: appmap enabled: False
[2025-05-19 13:35:10,166] INFO _appmap.env: appmap enabled: False
[2025-05-19 13:35:12,941] INFO _appmap.env: appmap enabled: False
[2025-05-19 13:40:15,536] INFO _appmap.env: appmap enabled: False
[2025-05-19 14:05:50,712] INFO _appmap.configuration: file: /application/appmap.yml
[2025-05-19 14:05:50,712] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-05-19 14:05:50,712] INFO _appmap.configuration: env: environ({'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'PYTHONUNBUFFERED': '1', 'DB_REPLICA_USER': 'stockone', 'HOSTNAME': 'cc4f03ba0bbe', 'PYTHON_VERSION': '3.12.10', 'DB_REPORTS_USER': 'stockone', 'DB_REPLICA_HOST': 'db1', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'PWD': '/application', 'USE_S3': 'TRUE', 'DB_USER': 'stockone', 'HOME': '/root', 'LANG': 'C.UTF-8', 'DB_REPLICA_NAME': 'stockone', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'TERM': 'xterm', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_HOST': 'db1', 'AWS_ID': '********************', 'SHLVL': '1', 'DB_REPORTS_NAME': 'stockone', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DB_NAME': 'stockone', 'APPROVAL_SERVICE_URL': 'http://************:8001/api/v1/', 'TASK_USAGE': '0', 'DB_REPORTS_PASS': 'stockone^123', 'DB_REPORTS_HOST': 'db1', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'DB_REPLICA_PASS': 'stockone^123', 'AWS_S3_REGION_NAME': 'ap-south-1', 'DEBUG': '1', 'DB_PASS': 'stockone^123', 'CONN_MAX_AGE': '300', 'ENTRYPOINT_FLAG': 'true', 'APPROVAL_ORIGIN_NAME': 'dev-approval-app', '_': '/usr/local/bin/python'})
[2025-05-19 14:05:50,722] INFO _appmap.env: appmap enabled: False
[2025-05-19 14:10:46,322] INFO _appmap.env: appmap enabled: False
[2025-05-19 14:10:57,053] INFO _appmap.env: appmap enabled: False
[2025-05-19 14:10:58,405] INFO _appmap.env: appmap enabled: False
[2025-05-19 14:11:14,646] INFO _appmap.env: appmap enabled: False
[2025-05-19 14:11:16,345] INFO _appmap.env: appmap enabled: False
[2025-05-19 14:11:18,601] INFO _appmap.env: appmap enabled: False
[2025-05-20 09:01:02,855] INFO _appmap.env: appmap enabled: False
[2025-05-20 09:01:26,718] INFO _appmap.env: appmap enabled: False
[2025-05-20 09:03:15,713] INFO _appmap.env: appmap enabled: False
[2025-05-20 09:03:20,392] INFO _appmap.env: appmap enabled: False
[2025-05-20 09:36:22,501] INFO _appmap.env: appmap enabled: False
[2025-05-20 11:11:52,330] INFO _appmap.configuration: file: /application/appmap.yml
[2025-05-20 11:11:52,331] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-05-20 11:11:52,331] INFO _appmap.configuration: env: environ({'APPROVAL_SERVICE_URL': 'http://************:8001/api/v1/', 'HOSTNAME': '0ba8259519b7', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DEBUG': '1', 'HOME': '/root', 'DB_NAME': 'stockone', 'PYTHONUNBUFFERED': '1', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'CONN_MAX_AGE': '300', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'DB_REPLICA_PASS': 'stockone^123', 'TASK_USAGE': '0', 'APPROVAL_ORIGIN_NAME': 'dev-approval-app', 'ENTRYPOINT_FLAG': 'true', 'AWS_S3_REGION_NAME': 'ap-south-1', 'TERM': 'xterm', 'DB_REPLICA_HOST': 'db1', 'DB_REPORTS_PASS': 'stockone^123', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'DB_REPLICA_USER': 'stockone', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'LANG': 'C.UTF-8', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_REPORTS_HOST': 'db1', 'DB_REPORTS_USER': 'stockone', 'PYTHON_VERSION': '3.12.10', 'DB_PASS': 'stockone^123', 'DB_REPLICA_NAME': 'stockone', 'AWS_ID': '********************', 'USE_S3': 'TRUE', 'PWD': '/application', 'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'DB_HOST': 'db1', 'DB_USER': 'stockone', 'DB_REPORTS_NAME': 'stockone'})
[2025-05-20 11:11:52,357] INFO _appmap.env: appmap enabled: False
[2025-05-20 11:12:06,344] INFO _appmap.configuration: file: /application/appmap.yml
[2025-05-20 11:12:06,344] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-05-20 11:12:06,345] INFO _appmap.configuration: env: environ({'APPROVAL_SERVICE_URL': 'http://************:8001/api/v1/', 'HOSTNAME': '0ba8259519b7', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DEBUG': '1', 'HOME': '/root', 'DB_NAME': 'stockone', 'PYTHONUNBUFFERED': '1', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'CONN_MAX_AGE': '300', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'DB_REPLICA_PASS': 'stockone^123', 'TASK_USAGE': '0', 'APPROVAL_ORIGIN_NAME': 'dev-approval-app', 'ENTRYPOINT_FLAG': 'true', 'AWS_S3_REGION_NAME': 'ap-south-1', 'TERM': 'xterm', 'DB_REPLICA_HOST': 'db1', 'DB_REPORTS_PASS': 'stockone^123', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'DB_REPLICA_USER': 'stockone', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'LANG': 'C.UTF-8', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_REPORTS_HOST': 'db1', 'DB_REPORTS_USER': 'stockone', 'PYTHON_VERSION': '3.12.10', 'DB_PASS': 'stockone^123', 'DB_REPLICA_NAME': 'stockone', 'AWS_ID': '********************', 'USE_S3': 'TRUE', 'PWD': '/application', 'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'DB_HOST': 'db1', 'DB_USER': 'stockone', 'DB_REPORTS_NAME': 'stockone'})
[2025-05-20 11:12:06,356] INFO _appmap.env: appmap enabled: False
[2025-05-20 11:12:09,776] INFO _appmap.configuration: file: /application/appmap.yml
[2025-05-20 11:12:09,777] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-05-20 11:12:09,777] INFO _appmap.configuration: env: environ({'APPROVAL_SERVICE_URL': 'http://************:8001/api/v1/', 'HOSTNAME': '0ba8259519b7', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DEBUG': '1', 'HOME': '/root', 'DB_NAME': 'stockone', 'PYTHONUNBUFFERED': '1', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'CONN_MAX_AGE': '300', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'DB_REPLICA_PASS': 'stockone^123', 'TASK_USAGE': '0', 'APPROVAL_ORIGIN_NAME': 'dev-approval-app', 'ENTRYPOINT_FLAG': 'true', 'AWS_S3_REGION_NAME': 'ap-south-1', 'TERM': 'xterm', 'DB_REPLICA_HOST': 'db1', 'DB_REPORTS_PASS': 'stockone^123', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'DB_REPLICA_USER': 'stockone', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'LANG': 'C.UTF-8', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_REPORTS_HOST': 'db1', 'DB_REPORTS_USER': 'stockone', 'PYTHON_VERSION': '3.12.10', 'DB_PASS': 'stockone^123', 'DB_REPLICA_NAME': 'stockone', 'AWS_ID': '********************', 'USE_S3': 'TRUE', 'PWD': '/application', 'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'DB_HOST': 'db1', 'DB_USER': 'stockone', 'DB_REPORTS_NAME': 'stockone'})
[2025-05-20 11:12:09,780] INFO _appmap.env: appmap enabled: False
[2025-05-20 11:12:12,856] INFO _appmap.configuration: file: /application/appmap.yml
[2025-05-20 11:12:12,856] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-05-20 11:12:12,856] INFO _appmap.configuration: env: environ({'APPROVAL_SERVICE_URL': 'http://************:8001/api/v1/', 'HOSTNAME': '0ba8259519b7', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DEBUG': '1', 'HOME': '/root', 'DB_NAME': 'stockone', 'PYTHONUNBUFFERED': '1', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'CONN_MAX_AGE': '300', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'DB_REPLICA_PASS': 'stockone^123', 'TASK_USAGE': '0', 'APPROVAL_ORIGIN_NAME': 'dev-approval-app', 'ENTRYPOINT_FLAG': 'true', 'AWS_S3_REGION_NAME': 'ap-south-1', 'TERM': 'xterm', 'DB_REPLICA_HOST': 'db1', 'DB_REPORTS_PASS': 'stockone^123', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'DB_REPLICA_USER': 'stockone', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'LANG': 'C.UTF-8', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_REPORTS_HOST': 'db1', 'DB_REPORTS_USER': 'stockone', 'PYTHON_VERSION': '3.12.10', 'DB_PASS': 'stockone^123', 'DB_REPLICA_NAME': 'stockone', 'AWS_ID': '********************', 'USE_S3': 'TRUE', 'PWD': '/application', 'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'DB_HOST': 'db1', 'DB_USER': 'stockone', 'DB_REPORTS_NAME': 'stockone'})
[2025-05-20 11:12:12,859] INFO _appmap.env: appmap enabled: False
[2025-05-20 11:12:17,788] INFO _appmap.configuration: file: /application/appmap.yml
[2025-05-20 11:12:17,788] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-05-20 11:12:17,788] INFO _appmap.configuration: env: environ({'APPROVAL_SERVICE_URL': 'http://************:8001/api/v1/', 'HOSTNAME': '0ba8259519b7', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DEBUG': '1', 'HOME': '/root', 'DB_NAME': 'stockone', 'PYTHONUNBUFFERED': '1', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'CONN_MAX_AGE': '300', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'DB_REPLICA_PASS': 'stockone^123', 'TASK_USAGE': '0', 'APPROVAL_ORIGIN_NAME': 'dev-approval-app', 'ENTRYPOINT_FLAG': 'true', 'AWS_S3_REGION_NAME': 'ap-south-1', 'TERM': 'xterm', 'DB_REPLICA_HOST': 'db1', 'DB_REPORTS_PASS': 'stockone^123', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'DB_REPLICA_USER': 'stockone', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'LANG': 'C.UTF-8', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_REPORTS_HOST': 'db1', 'DB_REPORTS_USER': 'stockone', 'PYTHON_VERSION': '3.12.10', 'DB_PASS': 'stockone^123', 'DB_REPLICA_NAME': 'stockone', 'AWS_ID': '********************', 'USE_S3': 'TRUE', 'PWD': '/application', 'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'DB_HOST': 'db1', 'DB_USER': 'stockone', 'DB_REPORTS_NAME': 'stockone'})
[2025-05-20 11:12:17,793] INFO _appmap.env: appmap enabled: False
[2025-05-20 11:12:17,880] INFO _appmap.env: appmap enabled: False
[2025-05-20 11:12:17,891] INFO _appmap.env: appmap enabled: False
[2025-05-20 12:20:32,581] INFO _appmap.env: appmap enabled: False
[2025-05-20 12:23:24,423] INFO _appmap.env: appmap enabled: False
[2025-05-20 12:35:19,219] INFO _appmap.env: appmap enabled: False
[2025-05-20 12:35:46,575] INFO _appmap.env: appmap enabled: False
[2025-05-20 12:52:37,680] INFO _appmap.env: appmap enabled: False
[2025-05-20 13:01:08,055] INFO _appmap.env: appmap enabled: False
[2025-05-21 05:48:46,015] INFO _appmap.env: appmap enabled: False
[2025-05-22 11:17:15,841] INFO _appmap.configuration: file: /application/appmap.yml
[2025-05-22 11:17:15,844] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-05-22 11:17:15,844] INFO _appmap.configuration: env: environ({'APPROVAL_SERVICE_URL': 'http://************:8001/api/v1/', 'HOSTNAME': '3c8911953e77', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DEBUG': '1', 'HOME': '/root', 'DB_NAME': 'stockone', 'PYTHONUNBUFFERED': '1', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'CONN_MAX_AGE': '300', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'DB_REPLICA_PASS': 'stockone^123', 'TASK_USAGE': '0', 'APPROVAL_ORIGIN_NAME': 'dev-approval-app', 'ENTRYPOINT_FLAG': 'true', 'AWS_S3_REGION_NAME': 'ap-south-1', 'TERM': 'xterm', 'DB_REPLICA_HOST': 'db1', 'DB_REPLICA_USER': 'stockone', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'DB_REPORTS_PASS': 'stockone^123', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'LANG': 'C.UTF-8', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_REPORTS_HOST': 'db1', 'DB_REPORTS_USER': 'stockone', 'PYTHON_VERSION': '3.12.10', 'AWS_ID': '********************', 'DB_PASS': 'stockone^123', 'DB_REPLICA_NAME': 'stockone', 'USE_S3': 'TRUE', 'PWD': '/application', 'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'DB_HOST': 'db1', 'DB_USER': 'stockone', 'DB_REPORTS_NAME': 'stockone'})
[2025-05-22 11:17:16,078] INFO _appmap.env: appmap enabled: False
[2025-05-22 11:17:20,581] INFO _appmap.configuration: file: /application/appmap.yml
[2025-05-22 11:17:20,581] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-05-22 11:17:20,581] INFO _appmap.configuration: env: environ({'APPROVAL_SERVICE_URL': 'http://************:8001/api/v1/', 'HOSTNAME': '3c8911953e77', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DEBUG': '1', 'HOME': '/root', 'DB_NAME': 'stockone', 'PYTHONUNBUFFERED': '1', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'CONN_MAX_AGE': '300', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'DB_REPLICA_PASS': 'stockone^123', 'TASK_USAGE': '0', 'APPROVAL_ORIGIN_NAME': 'dev-approval-app', 'ENTRYPOINT_FLAG': 'true', 'AWS_S3_REGION_NAME': 'ap-south-1', 'TERM': 'xterm', 'DB_REPLICA_HOST': 'db1', 'DB_REPLICA_USER': 'stockone', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'DB_REPORTS_PASS': 'stockone^123', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'LANG': 'C.UTF-8', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_REPORTS_HOST': 'db1', 'DB_REPORTS_USER': 'stockone', 'PYTHON_VERSION': '3.12.10', 'AWS_ID': '********************', 'DB_PASS': 'stockone^123', 'DB_REPLICA_NAME': 'stockone', 'USE_S3': 'TRUE', 'PWD': '/application', 'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'DB_HOST': 'db1', 'DB_USER': 'stockone', 'DB_REPORTS_NAME': 'stockone'})
[2025-05-22 11:17:20,586] INFO _appmap.env: appmap enabled: False
[2025-05-22 11:17:22,327] INFO _appmap.configuration: file: /application/appmap.yml
[2025-05-22 11:17:22,327] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-05-22 11:17:22,327] INFO _appmap.configuration: env: environ({'APPROVAL_SERVICE_URL': 'http://************:8001/api/v1/', 'HOSTNAME': '3c8911953e77', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DEBUG': '1', 'HOME': '/root', 'DB_NAME': 'stockone', 'PYTHONUNBUFFERED': '1', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'CONN_MAX_AGE': '300', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'DB_REPLICA_PASS': 'stockone^123', 'TASK_USAGE': '0', 'APPROVAL_ORIGIN_NAME': 'dev-approval-app', 'ENTRYPOINT_FLAG': 'true', 'AWS_S3_REGION_NAME': 'ap-south-1', 'TERM': 'xterm', 'DB_REPLICA_HOST': 'db1', 'DB_REPLICA_USER': 'stockone', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'DB_REPORTS_PASS': 'stockone^123', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'LANG': 'C.UTF-8', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_REPORTS_HOST': 'db1', 'DB_REPORTS_USER': 'stockone', 'PYTHON_VERSION': '3.12.10', 'AWS_ID': '********************', 'DB_PASS': 'stockone^123', 'DB_REPLICA_NAME': 'stockone', 'USE_S3': 'TRUE', 'PWD': '/application', 'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'DB_HOST': 'db1', 'DB_USER': 'stockone', 'DB_REPORTS_NAME': 'stockone'})
[2025-05-22 11:17:22,332] INFO _appmap.env: appmap enabled: False
[2025-05-22 11:17:23,452] INFO _appmap.configuration: file: /application/appmap.yml
[2025-05-22 11:17:23,452] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-05-22 11:17:23,452] INFO _appmap.configuration: env: environ({'APPROVAL_SERVICE_URL': 'http://************:8001/api/v1/', 'HOSTNAME': '3c8911953e77', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DEBUG': '1', 'HOME': '/root', 'DB_NAME': 'stockone', 'PYTHONUNBUFFERED': '1', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'CONN_MAX_AGE': '300', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'DB_REPLICA_PASS': 'stockone^123', 'TASK_USAGE': '0', 'APPROVAL_ORIGIN_NAME': 'dev-approval-app', 'ENTRYPOINT_FLAG': 'true', 'AWS_S3_REGION_NAME': 'ap-south-1', 'TERM': 'xterm', 'DB_REPLICA_HOST': 'db1', 'DB_REPLICA_USER': 'stockone', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'DB_REPORTS_PASS': 'stockone^123', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'LANG': 'C.UTF-8', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_REPORTS_HOST': 'db1', 'DB_REPORTS_USER': 'stockone', 'PYTHON_VERSION': '3.12.10', 'AWS_ID': '********************', 'DB_PASS': 'stockone^123', 'DB_REPLICA_NAME': 'stockone', 'USE_S3': 'TRUE', 'PWD': '/application', 'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'DB_HOST': 'db1', 'DB_USER': 'stockone', 'DB_REPORTS_NAME': 'stockone'})
[2025-05-22 11:17:23,473] INFO _appmap.env: appmap enabled: False
[2025-05-22 11:17:25,089] INFO _appmap.configuration: file: /application/appmap.yml
[2025-05-22 11:17:25,089] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-05-22 11:17:25,090] INFO _appmap.configuration: env: environ({'APPROVAL_SERVICE_URL': 'http://************:8001/api/v1/', 'HOSTNAME': '3c8911953e77', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DEBUG': '1', 'HOME': '/root', 'DB_NAME': 'stockone', 'PYTHONUNBUFFERED': '1', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'CONN_MAX_AGE': '300', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'DB_REPLICA_PASS': 'stockone^123', 'TASK_USAGE': '0', 'APPROVAL_ORIGIN_NAME': 'dev-approval-app', 'ENTRYPOINT_FLAG': 'true', 'AWS_S3_REGION_NAME': 'ap-south-1', 'TERM': 'xterm', 'DB_REPLICA_HOST': 'db1', 'DB_REPLICA_USER': 'stockone', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'DB_REPORTS_PASS': 'stockone^123', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'LANG': 'C.UTF-8', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_REPORTS_HOST': 'db1', 'DB_REPORTS_USER': 'stockone', 'PYTHON_VERSION': '3.12.10', 'AWS_ID': '********************', 'DB_PASS': 'stockone^123', 'DB_REPLICA_NAME': 'stockone', 'USE_S3': 'TRUE', 'PWD': '/application', 'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'DB_HOST': 'db1', 'DB_USER': 'stockone', 'DB_REPORTS_NAME': 'stockone'})
[2025-05-22 11:17:25,102] INFO _appmap.env: appmap enabled: False
[2025-05-22 11:17:25,636] INFO _appmap.env: appmap enabled: False
[2025-05-22 11:17:25,646] INFO _appmap.env: appmap enabled: False
[2025-05-22 11:22:59,982] INFO _appmap.configuration: file: /application/appmap.yml
[2025-05-22 11:22:59,983] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-05-22 11:22:59,984] INFO _appmap.configuration: env: environ({'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'PYTHONUNBUFFERED': '1', 'DB_REPLICA_USER': 'stockone', 'HOSTNAME': '3c8911953e77', 'PYTHON_VERSION': '3.12.10', 'DB_REPORTS_USER': 'stockone', 'DB_REPLICA_HOST': 'db1', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'PWD': '/application', 'USE_S3': 'TRUE', 'DB_USER': 'stockone', 'HOME': '/root', 'LANG': 'C.UTF-8', 'DB_REPLICA_NAME': 'stockone', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'TERM': 'xterm', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_HOST': 'db1', 'AWS_ID': '********************', 'SHLVL': '1', 'DB_REPORTS_NAME': 'stockone', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DB_NAME': 'stockone', 'APPROVAL_SERVICE_URL': 'http://************:8001/api/v1/', 'TASK_USAGE': '0', 'DB_REPORTS_PASS': 'stockone^123', 'DB_REPORTS_HOST': 'db1', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'DB_REPLICA_PASS': 'stockone^123', 'AWS_S3_REGION_NAME': 'ap-south-1', 'DEBUG': '1', 'DB_PASS': 'stockone^123', 'ENTRYPOINT_FLAG': 'true', 'CONN_MAX_AGE': '300', 'APPROVAL_ORIGIN_NAME': 'dev-approval-app', '_': '/usr/local/bin/python'})
[2025-05-22 11:23:00,007] INFO _appmap.env: appmap enabled: False
[2025-05-22 11:23:22,403] INFO _appmap.env: appmap enabled: False
[2025-05-22 11:29:28,973] INFO _appmap.env: appmap enabled: False
[2025-05-22 11:29:32,522] INFO _appmap.env: appmap enabled: False
[2025-05-22 11:29:41,092] INFO _appmap.env: appmap enabled: False
[2025-05-22 11:36:08,718] INFO _appmap.env: appmap enabled: False
[2025-05-23 07:52:53,852] INFO _appmap.env: appmap enabled: False
[2025-05-23 07:52:58,979] INFO _appmap.env: appmap enabled: False
[2025-05-23 12:23:16,705] INFO _appmap.configuration: file: /application/appmap.yml
[2025-05-23 12:23:16,708] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-05-23 12:23:16,708] INFO _appmap.configuration: env: environ({'APPROVAL_SERVICE_URL': 'http://************:8001/api/v1/', 'HOSTNAME': 'cebc96ae592c', 'DEBUG': '1', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'HOME': '/root', 'DB_NAME': 'stockone', 'PYTHONUNBUFFERED': '1', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'CONN_MAX_AGE': '300', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'DB_REPLICA_PASS': 'stockone^123', 'TASK_USAGE': '0', 'APPROVAL_ORIGIN_NAME': 'dev-approval-app', 'ENTRYPOINT_FLAG': 'true', 'AWS_S3_REGION_NAME': 'ap-south-1', 'TERM': 'xterm', 'DB_REPLICA_HOST': 'db1', 'DB_REPLICA_USER': 'stockone', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'DB_REPORTS_PASS': 'stockone^123', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'LANG': 'C.UTF-8', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_REPORTS_HOST': 'db1', 'DB_REPORTS_USER': 'stockone', 'PYTHON_VERSION': '3.12.10', 'AWS_ID': '********************', 'DB_PASS': 'stockone^123', 'DB_REPLICA_NAME': 'stockone', 'USE_S3': 'TRUE', 'PWD': '/application', 'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'DB_HOST': 'db1', 'DB_REPORTS_NAME': 'stockone', 'DB_USER': 'stockone'})
[2025-05-23 12:23:16,735] INFO _appmap.env: appmap enabled: False
[2025-05-23 12:23:31,970] INFO _appmap.configuration: file: /application/appmap.yml
[2025-05-23 12:23:31,971] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-05-23 12:23:31,971] INFO _appmap.configuration: env: environ({'APPROVAL_SERVICE_URL': 'http://************:8001/api/v1/', 'HOSTNAME': 'cebc96ae592c', 'DEBUG': '1', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'HOME': '/root', 'DB_NAME': 'stockone', 'PYTHONUNBUFFERED': '1', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'CONN_MAX_AGE': '300', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'DB_REPLICA_PASS': 'stockone^123', 'TASK_USAGE': '0', 'APPROVAL_ORIGIN_NAME': 'dev-approval-app', 'ENTRYPOINT_FLAG': 'true', 'AWS_S3_REGION_NAME': 'ap-south-1', 'TERM': 'xterm', 'DB_REPLICA_HOST': 'db1', 'DB_REPLICA_USER': 'stockone', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'DB_REPORTS_PASS': 'stockone^123', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'LANG': 'C.UTF-8', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_REPORTS_HOST': 'db1', 'DB_REPORTS_USER': 'stockone', 'PYTHON_VERSION': '3.12.10', 'AWS_ID': '********************', 'DB_PASS': 'stockone^123', 'DB_REPLICA_NAME': 'stockone', 'USE_S3': 'TRUE', 'PWD': '/application', 'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'DB_HOST': 'db1', 'DB_REPORTS_NAME': 'stockone', 'DB_USER': 'stockone'})
[2025-05-23 12:23:31,977] INFO _appmap.env: appmap enabled: False
[2025-05-23 12:23:39,766] INFO _appmap.configuration: file: /application/appmap.yml
[2025-05-23 12:23:39,766] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-05-23 12:23:39,766] INFO _appmap.configuration: env: environ({'APPROVAL_SERVICE_URL': 'http://************:8001/api/v1/', 'HOSTNAME': 'cebc96ae592c', 'DEBUG': '1', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'HOME': '/root', 'DB_NAME': 'stockone', 'PYTHONUNBUFFERED': '1', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'CONN_MAX_AGE': '300', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'DB_REPLICA_PASS': 'stockone^123', 'TASK_USAGE': '0', 'APPROVAL_ORIGIN_NAME': 'dev-approval-app', 'ENTRYPOINT_FLAG': 'true', 'AWS_S3_REGION_NAME': 'ap-south-1', 'TERM': 'xterm', 'DB_REPLICA_HOST': 'db1', 'DB_REPLICA_USER': 'stockone', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'DB_REPORTS_PASS': 'stockone^123', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'LANG': 'C.UTF-8', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_REPORTS_HOST': 'db1', 'DB_REPORTS_USER': 'stockone', 'PYTHON_VERSION': '3.12.10', 'AWS_ID': '********************', 'DB_PASS': 'stockone^123', 'DB_REPLICA_NAME': 'stockone', 'USE_S3': 'TRUE', 'PWD': '/application', 'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'DB_HOST': 'db1', 'DB_REPORTS_NAME': 'stockone', 'DB_USER': 'stockone'})
[2025-05-23 12:23:39,771] INFO _appmap.env: appmap enabled: False
[2025-05-23 12:23:42,925] INFO _appmap.configuration: file: /application/appmap.yml
[2025-05-23 12:23:42,925] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-05-23 12:23:42,925] INFO _appmap.configuration: env: environ({'APPROVAL_SERVICE_URL': 'http://************:8001/api/v1/', 'HOSTNAME': 'cebc96ae592c', 'DEBUG': '1', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'HOME': '/root', 'DB_NAME': 'stockone', 'PYTHONUNBUFFERED': '1', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'CONN_MAX_AGE': '300', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'DB_REPLICA_PASS': 'stockone^123', 'TASK_USAGE': '0', 'APPROVAL_ORIGIN_NAME': 'dev-approval-app', 'ENTRYPOINT_FLAG': 'true', 'AWS_S3_REGION_NAME': 'ap-south-1', 'TERM': 'xterm', 'DB_REPLICA_HOST': 'db1', 'DB_REPLICA_USER': 'stockone', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'DB_REPORTS_PASS': 'stockone^123', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'LANG': 'C.UTF-8', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_REPORTS_HOST': 'db1', 'DB_REPORTS_USER': 'stockone', 'PYTHON_VERSION': '3.12.10', 'AWS_ID': '********************', 'DB_PASS': 'stockone^123', 'DB_REPLICA_NAME': 'stockone', 'USE_S3': 'TRUE', 'PWD': '/application', 'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'DB_HOST': 'db1', 'DB_REPORTS_NAME': 'stockone', 'DB_USER': 'stockone'})
[2025-05-23 12:23:42,930] INFO _appmap.env: appmap enabled: False
[2025-05-23 12:23:49,650] INFO _appmap.configuration: file: /application/appmap.yml
[2025-05-23 12:23:49,650] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-05-23 12:23:49,650] INFO _appmap.configuration: env: environ({'APPROVAL_SERVICE_URL': 'http://************:8001/api/v1/', 'HOSTNAME': 'cebc96ae592c', 'DEBUG': '1', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'HOME': '/root', 'DB_NAME': 'stockone', 'PYTHONUNBUFFERED': '1', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'CONN_MAX_AGE': '300', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'DB_REPLICA_PASS': 'stockone^123', 'TASK_USAGE': '0', 'APPROVAL_ORIGIN_NAME': 'dev-approval-app', 'ENTRYPOINT_FLAG': 'true', 'AWS_S3_REGION_NAME': 'ap-south-1', 'TERM': 'xterm', 'DB_REPLICA_HOST': 'db1', 'DB_REPLICA_USER': 'stockone', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'DB_REPORTS_PASS': 'stockone^123', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'LANG': 'C.UTF-8', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_REPORTS_HOST': 'db1', 'DB_REPORTS_USER': 'stockone', 'PYTHON_VERSION': '3.12.10', 'AWS_ID': '********************', 'DB_PASS': 'stockone^123', 'DB_REPLICA_NAME': 'stockone', 'USE_S3': 'TRUE', 'PWD': '/application', 'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'DB_HOST': 'db1', 'DB_REPORTS_NAME': 'stockone', 'DB_USER': 'stockone'})
[2025-05-23 12:23:49,654] INFO _appmap.env: appmap enabled: False
[2025-05-23 12:23:49,736] INFO _appmap.env: appmap enabled: False
[2025-05-23 12:23:49,738] INFO _appmap.env: appmap enabled: False
[2025-05-23 12:44:21,722] INFO _appmap.configuration: file: /application/appmap.yml
[2025-05-23 12:44:21,725] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-05-23 12:44:21,725] INFO _appmap.configuration: env: environ({'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'PYTHONUNBUFFERED': '1', 'DB_REPLICA_USER': 'stockone', 'HOSTNAME': 'cebc96ae592c', 'PYTHON_VERSION': '3.12.10', 'DB_REPORTS_USER': 'stockone', 'DB_REPLICA_HOST': 'db1', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'PWD': '/application', 'USE_S3': 'TRUE', 'DB_USER': 'stockone', 'HOME': '/root', 'LANG': 'C.UTF-8', 'DB_REPLICA_NAME': 'stockone', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'TERM': 'xterm', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_HOST': 'db1', 'AWS_ID': '********************', 'SHLVL': '1', 'DB_REPORTS_NAME': 'stockone', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DB_NAME': 'stockone', 'APPROVAL_SERVICE_URL': 'http://************:8001/api/v1/', 'TASK_USAGE': '0', 'DB_REPORTS_PASS': 'stockone^123', 'DB_REPORTS_HOST': 'db1', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'DB_REPLICA_PASS': 'stockone^123', 'AWS_S3_REGION_NAME': 'ap-south-1', 'DEBUG': '1', 'DB_PASS': 'stockone^123', 'CONN_MAX_AGE': '300', 'ENTRYPOINT_FLAG': 'true', 'APPROVAL_ORIGIN_NAME': 'dev-approval-app', '_': '/usr/local/bin/python'})
[2025-05-23 12:44:21,736] INFO _appmap.env: appmap enabled: False
[2025-05-23 13:55:28,170] INFO _appmap.env: appmap enabled: False
[2025-05-23 13:55:45,453] INFO _appmap.env: appmap enabled: False
[2025-05-23 14:03:42,170] INFO _appmap.env: appmap enabled: False
[2025-05-23 14:03:51,316] INFO _appmap.env: appmap enabled: False
[2025-05-23 14:04:07,862] INFO _appmap.env: appmap enabled: False
[2025-05-23 14:04:27,647] INFO _appmap.env: appmap enabled: False
[2025-05-23 14:04:38,323] INFO _appmap.env: appmap enabled: False
[2025-05-23 14:04:45,717] INFO _appmap.env: appmap enabled: False
[2025-05-23 14:05:00,011] INFO _appmap.env: appmap enabled: False
[2025-05-23 14:05:09,595] INFO _appmap.env: appmap enabled: False
[2025-05-26 14:18:00,497] INFO _appmap.configuration: file: /application/appmap.yml
[2025-05-26 14:18:00,498] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-05-26 14:18:00,498] INFO _appmap.configuration: env: environ({'APPROVAL_SERVICE_URL': 'http://************:8001/api/v1/', 'HOSTNAME': '02117f210751', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DEBUG': '1', 'HOME': '/root', 'DB_NAME': 'stockone', 'PYTHONUNBUFFERED': '1', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'CONN_MAX_AGE': '300', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'DB_REPLICA_PASS': 'stockone^123', 'TASK_USAGE': '0', 'APPROVAL_ORIGIN_NAME': 'dev-approval-app', 'ENTRYPOINT_FLAG': 'true', 'AWS_S3_REGION_NAME': 'ap-south-1', 'TERM': 'xterm', 'DB_REPLICA_HOST': 'db1', 'DB_REPLICA_USER': 'stockone', 'DB_REPORTS_PASS': 'stockone^123', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'LANG': 'C.UTF-8', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_REPORTS_HOST': 'db1', 'DB_REPORTS_USER': 'stockone', 'PYTHON_VERSION': '3.12.10', 'AWS_ID': '********************', 'DB_REPLICA_NAME': 'stockone', 'DB_PASS': 'stockone^123', 'USE_S3': 'TRUE', 'PWD': '/application', 'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'DB_HOST': 'db1', 'DB_REPORTS_NAME': 'stockone', 'DB_USER': 'stockone'})
[2025-05-26 14:18:00,513] INFO _appmap.env: appmap enabled: False
[2025-05-26 14:18:09,802] INFO _appmap.configuration: file: /application/appmap.yml
[2025-05-26 14:18:09,802] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-05-26 14:18:09,802] INFO _appmap.configuration: env: environ({'APPROVAL_SERVICE_URL': 'http://************:8001/api/v1/', 'HOSTNAME': '02117f210751', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DEBUG': '1', 'HOME': '/root', 'DB_NAME': 'stockone', 'PYTHONUNBUFFERED': '1', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'CONN_MAX_AGE': '300', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'DB_REPLICA_PASS': 'stockone^123', 'TASK_USAGE': '0', 'APPROVAL_ORIGIN_NAME': 'dev-approval-app', 'ENTRYPOINT_FLAG': 'true', 'AWS_S3_REGION_NAME': 'ap-south-1', 'TERM': 'xterm', 'DB_REPLICA_HOST': 'db1', 'DB_REPLICA_USER': 'stockone', 'DB_REPORTS_PASS': 'stockone^123', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'LANG': 'C.UTF-8', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_REPORTS_HOST': 'db1', 'DB_REPORTS_USER': 'stockone', 'PYTHON_VERSION': '3.12.10', 'AWS_ID': '********************', 'DB_REPLICA_NAME': 'stockone', 'DB_PASS': 'stockone^123', 'USE_S3': 'TRUE', 'PWD': '/application', 'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'DB_HOST': 'db1', 'DB_REPORTS_NAME': 'stockone', 'DB_USER': 'stockone'})
[2025-05-26 14:18:09,806] INFO _appmap.env: appmap enabled: False
[2025-05-26 14:18:12,736] INFO _appmap.configuration: file: /application/appmap.yml
[2025-05-26 14:18:12,736] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-05-26 14:18:12,736] INFO _appmap.configuration: env: environ({'APPROVAL_SERVICE_URL': 'http://************:8001/api/v1/', 'HOSTNAME': '02117f210751', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DEBUG': '1', 'HOME': '/root', 'DB_NAME': 'stockone', 'PYTHONUNBUFFERED': '1', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'CONN_MAX_AGE': '300', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'DB_REPLICA_PASS': 'stockone^123', 'TASK_USAGE': '0', 'APPROVAL_ORIGIN_NAME': 'dev-approval-app', 'ENTRYPOINT_FLAG': 'true', 'AWS_S3_REGION_NAME': 'ap-south-1', 'TERM': 'xterm', 'DB_REPLICA_HOST': 'db1', 'DB_REPLICA_USER': 'stockone', 'DB_REPORTS_PASS': 'stockone^123', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'LANG': 'C.UTF-8', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_REPORTS_HOST': 'db1', 'DB_REPORTS_USER': 'stockone', 'PYTHON_VERSION': '3.12.10', 'AWS_ID': '********************', 'DB_REPLICA_NAME': 'stockone', 'DB_PASS': 'stockone^123', 'USE_S3': 'TRUE', 'PWD': '/application', 'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'DB_HOST': 'db1', 'DB_REPORTS_NAME': 'stockone', 'DB_USER': 'stockone'})
[2025-05-26 14:18:12,739] INFO _appmap.env: appmap enabled: False
[2025-05-26 14:18:15,323] INFO _appmap.configuration: file: /application/appmap.yml
[2025-05-26 14:18:15,323] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-05-26 14:18:15,323] INFO _appmap.configuration: env: environ({'APPROVAL_SERVICE_URL': 'http://************:8001/api/v1/', 'HOSTNAME': '02117f210751', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DEBUG': '1', 'HOME': '/root', 'DB_NAME': 'stockone', 'PYTHONUNBUFFERED': '1', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'CONN_MAX_AGE': '300', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'DB_REPLICA_PASS': 'stockone^123', 'TASK_USAGE': '0', 'APPROVAL_ORIGIN_NAME': 'dev-approval-app', 'ENTRYPOINT_FLAG': 'true', 'AWS_S3_REGION_NAME': 'ap-south-1', 'TERM': 'xterm', 'DB_REPLICA_HOST': 'db1', 'DB_REPLICA_USER': 'stockone', 'DB_REPORTS_PASS': 'stockone^123', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'LANG': 'C.UTF-8', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_REPORTS_HOST': 'db1', 'DB_REPORTS_USER': 'stockone', 'PYTHON_VERSION': '3.12.10', 'AWS_ID': '********************', 'DB_REPLICA_NAME': 'stockone', 'DB_PASS': 'stockone^123', 'USE_S3': 'TRUE', 'PWD': '/application', 'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'DB_HOST': 'db1', 'DB_REPORTS_NAME': 'stockone', 'DB_USER': 'stockone'})
[2025-05-26 14:18:15,327] INFO _appmap.env: appmap enabled: False
[2025-05-26 14:18:19,385] INFO _appmap.configuration: file: /application/appmap.yml
[2025-05-26 14:18:19,385] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-05-26 14:18:19,385] INFO _appmap.configuration: env: environ({'APPROVAL_SERVICE_URL': 'http://************:8001/api/v1/', 'HOSTNAME': '02117f210751', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DEBUG': '1', 'HOME': '/root', 'DB_NAME': 'stockone', 'PYTHONUNBUFFERED': '1', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'CONN_MAX_AGE': '300', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'DB_REPLICA_PASS': 'stockone^123', 'TASK_USAGE': '0', 'APPROVAL_ORIGIN_NAME': 'dev-approval-app', 'ENTRYPOINT_FLAG': 'true', 'AWS_S3_REGION_NAME': 'ap-south-1', 'TERM': 'xterm', 'DB_REPLICA_HOST': 'db1', 'DB_REPLICA_USER': 'stockone', 'DB_REPORTS_PASS': 'stockone^123', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'LANG': 'C.UTF-8', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_REPORTS_HOST': 'db1', 'DB_REPORTS_USER': 'stockone', 'PYTHON_VERSION': '3.12.10', 'AWS_ID': '********************', 'DB_REPLICA_NAME': 'stockone', 'DB_PASS': 'stockone^123', 'USE_S3': 'TRUE', 'PWD': '/application', 'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'DB_HOST': 'db1', 'DB_REPORTS_NAME': 'stockone', 'DB_USER': 'stockone'})
[2025-05-26 14:18:19,388] INFO _appmap.env: appmap enabled: False
[2025-05-26 14:18:19,465] INFO _appmap.env: appmap enabled: False
[2025-05-26 14:18:19,472] INFO _appmap.env: appmap enabled: False
[2025-05-26 14:18:59,555] INFO _appmap.configuration: file: /application/appmap.yml
[2025-05-26 14:18:59,556] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-05-26 14:18:59,556] INFO _appmap.configuration: env: environ({'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'PYTHONUNBUFFERED': '1', 'DB_REPLICA_USER': 'stockone', 'HOSTNAME': '02117f210751', 'PYTHON_VERSION': '3.12.10', 'DB_REPORTS_USER': 'stockone', 'DB_REPLICA_HOST': 'db1', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'PWD': '/application', 'USE_S3': 'TRUE', 'DB_USER': 'stockone', 'HOME': '/root', 'LANG': 'C.UTF-8', 'DB_REPLICA_NAME': 'stockone', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'TERM': 'xterm', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_HOST': 'db1', 'AWS_ID': '********************', 'SHLVL': '1', 'DB_REPORTS_NAME': 'stockone', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DB_NAME': 'stockone', 'APPROVAL_SERVICE_URL': 'http://************:8001/api/v1/', 'TASK_USAGE': '0', 'DB_REPORTS_PASS': 'stockone^123', 'DB_REPORTS_HOST': 'db1', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'DB_REPLICA_PASS': 'stockone^123', 'AWS_S3_REGION_NAME': 'ap-south-1', 'DEBUG': '1', 'DB_PASS': 'stockone^123', 'ENTRYPOINT_FLAG': 'true', 'CONN_MAX_AGE': '300', 'APPROVAL_ORIGIN_NAME': 'dev-approval-app', '_': '/usr/local/bin/python'})
[2025-05-26 14:18:59,562] INFO _appmap.env: appmap enabled: False
[2025-05-26 14:19:10,761] INFO _appmap.configuration: file: /application/appmap.yml
[2025-05-26 14:19:10,761] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-05-26 14:19:10,761] INFO _appmap.configuration: env: environ({'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'PYTHONUNBUFFERED': '1', 'DB_REPLICA_USER': 'stockone', 'HOSTNAME': '02117f210751', 'PYTHON_VERSION': '3.12.10', 'DB_REPORTS_USER': 'stockone', 'DB_REPLICA_HOST': 'db1', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'PWD': '/application', 'USE_S3': 'TRUE', 'DB_USER': 'stockone', 'HOME': '/root', 'LANG': 'C.UTF-8', 'DB_REPLICA_NAME': 'stockone', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'TERM': 'xterm', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_HOST': 'db1', 'AWS_ID': '********************', 'SHLVL': '1', 'DB_REPORTS_NAME': 'stockone', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DB_NAME': 'stockone', 'APPROVAL_SERVICE_URL': 'http://************:8001/api/v1/', 'TASK_USAGE': '0', 'DB_REPORTS_PASS': 'stockone^123', 'DB_REPORTS_HOST': 'db1', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'DB_REPLICA_PASS': 'stockone^123', 'AWS_S3_REGION_NAME': 'ap-south-1', 'DEBUG': '1', 'DB_PASS': 'stockone^123', 'ENTRYPOINT_FLAG': 'true', 'CONN_MAX_AGE': '300', 'APPROVAL_ORIGIN_NAME': 'dev-approval-app', '_': '/usr/local/bin/python'})
[2025-05-26 14:19:10,767] INFO _appmap.env: appmap enabled: False
[2025-05-26 14:19:30,374] INFO _appmap.env: appmap enabled: False
[2025-05-26 14:19:58,466] INFO _appmap.env: appmap enabled: False
[2025-05-26 14:20:07,956] INFO _appmap.env: appmap enabled: False
[2025-05-26 14:20:30,006] INFO _appmap.env: appmap enabled: False
[2025-05-26 14:22:16,806] INFO _appmap.env: appmap enabled: False
[2025-05-26 14:22:25,377] INFO _appmap.env: appmap enabled: False
[2025-05-26 14:22:51,148] INFO _appmap.env: appmap enabled: False
[2025-05-26 14:23:30,624] INFO _appmap.env: appmap enabled: False
[2025-05-26 14:24:14,707] INFO _appmap.env: appmap enabled: False
[2025-05-26 14:24:16,537] INFO _appmap.env: appmap enabled: False
[2025-05-26 14:24:26,300] INFO _appmap.env: appmap enabled: False
[2025-05-26 14:24:40,144] INFO _appmap.env: appmap enabled: False
[2025-05-26 14:24:54,892] INFO _appmap.env: appmap enabled: False
[2025-05-26 14:24:56,547] INFO _appmap.env: appmap enabled: False
[2025-05-26 14:24:59,322] INFO _appmap.env: appmap enabled: False
[2025-05-26 14:25:00,270] INFO _appmap.env: appmap enabled: False
[2025-05-26 14:25:01,718] INFO _appmap.env: appmap enabled: False
[2025-05-26 14:25:06,793] INFO _appmap.env: appmap enabled: False
[2025-05-26 14:25:26,733] INFO _appmap.env: appmap enabled: False
[2025-05-26 14:26:04,463] INFO _appmap.env: appmap enabled: False
[2025-05-26 14:28:06,107] INFO _appmap.env: appmap enabled: False
[2025-05-26 14:28:07,022] INFO _appmap.env: appmap enabled: False
[2025-05-26 14:30:05,776] INFO _appmap.env: appmap enabled: False
[2025-05-26 14:30:12,000] INFO _appmap.env: appmap enabled: False
[2025-05-26 14:30:56,872] INFO _appmap.env: appmap enabled: False
[2025-05-26 14:34:45,208] INFO _appmap.env: appmap enabled: False
[2025-05-26 14:34:50,425] INFO _appmap.env: appmap enabled: False
[2025-05-26 14:35:51,812] INFO _appmap.env: appmap enabled: False
[2025-05-26 14:37:22,503] INFO _appmap.env: appmap enabled: False
[2025-05-26 14:37:40,352] INFO _appmap.env: appmap enabled: False
[2025-05-26 14:37:46,374] INFO _appmap.env: appmap enabled: False
[2025-05-26 14:38:34,576] INFO _appmap.env: appmap enabled: False
[2025-05-26 14:38:40,405] INFO _appmap.env: appmap enabled: False
[2025-05-26 14:38:42,193] INFO _appmap.env: appmap enabled: False
[2025-05-26 14:38:48,412] INFO _appmap.env: appmap enabled: False
[2025-05-26 14:39:03,117] INFO _appmap.env: appmap enabled: False
[2025-05-26 14:40:50,816] INFO _appmap.env: appmap enabled: False
[2025-05-26 14:40:56,786] INFO _appmap.env: appmap enabled: False
[2025-05-26 14:41:11,513] INFO _appmap.env: appmap enabled: False
[2025-05-26 14:41:16,548] INFO _appmap.env: appmap enabled: False
[2025-05-26 14:41:29,189] INFO _appmap.env: appmap enabled: False
[2025-05-26 14:42:29,890] INFO _appmap.env: appmap enabled: False
[2025-05-26 14:42:31,503] INFO _appmap.env: appmap enabled: False
[2025-05-27 05:16:15,950] INFO _appmap.configuration: file: /application/appmap.yml
[2025-05-27 05:16:15,951] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-05-27 05:16:15,951] INFO _appmap.configuration: env: environ({'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'PYTHONUNBUFFERED': '1', 'DB_REPLICA_USER': 'stockone', 'HOSTNAME': '02117f210751', 'PYTHON_VERSION': '3.12.10', 'DB_REPORTS_USER': 'stockone', 'DB_REPLICA_HOST': 'db1', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'PWD': '/application', 'USE_S3': 'TRUE', 'DB_USER': 'stockone', 'HOME': '/root', 'LANG': 'C.UTF-8', 'DB_REPLICA_NAME': 'stockone', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'TERM': 'xterm', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_HOST': 'db1', 'AWS_ID': '********************', 'SHLVL': '1', 'DB_REPORTS_NAME': 'stockone', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DB_NAME': 'stockone', 'APPROVAL_SERVICE_URL': 'http://************:8001/api/v1/', 'TASK_USAGE': '0', 'DB_REPORTS_PASS': 'stockone^123', 'DB_REPORTS_HOST': 'db1', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'DB_REPLICA_PASS': 'stockone^123', 'AWS_S3_REGION_NAME': 'ap-south-1', 'DEBUG': '1', 'DB_PASS': 'stockone^123', 'ENTRYPOINT_FLAG': 'true', 'CONN_MAX_AGE': '300', 'APPROVAL_ORIGIN_NAME': 'dev-approval-app', '_': '/usr/local/bin/python'})
[2025-05-27 05:16:15,964] INFO _appmap.env: appmap enabled: False
[2025-05-27 05:36:23,844] INFO _appmap.configuration: file: /application/appmap.yml
[2025-05-27 05:36:23,845] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-05-27 05:36:23,845] INFO _appmap.configuration: env: environ({'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'PYTHONUNBUFFERED': '1', 'DB_REPLICA_USER': 'stockone', 'HOSTNAME': '02117f210751', 'PYTHON_VERSION': '3.12.10', 'DB_REPORTS_USER': 'stockone', 'DB_REPLICA_HOST': 'db1', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'PWD': '/application', 'USE_S3': 'TRUE', 'DB_USER': 'stockone', 'HOME': '/root', 'LANG': 'C.UTF-8', 'DB_REPLICA_NAME': 'stockone', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'TERM': 'xterm', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_HOST': 'db1', 'AWS_ID': '********************', 'SHLVL': '1', 'DB_REPORTS_NAME': 'stockone', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DB_NAME': 'stockone', 'APPROVAL_SERVICE_URL': 'http://************:8001/api/v1/', 'TASK_USAGE': '0', 'DB_REPORTS_PASS': 'stockone^123', 'DB_REPORTS_HOST': 'db1', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'DB_REPLICA_PASS': 'stockone^123', 'AWS_S3_REGION_NAME': 'ap-south-1', 'DEBUG': '1', 'DB_PASS': 'stockone^123', 'ENTRYPOINT_FLAG': 'true', 'CONN_MAX_AGE': '300', 'APPROVAL_ORIGIN_NAME': 'dev-approval-app', '_': '/usr/local/bin/python'})
[2025-05-27 05:36:23,850] INFO _appmap.env: appmap enabled: False
[2025-05-27 05:57:00,195] INFO _appmap.configuration: file: /application/appmap.yml
[2025-05-27 05:57:00,196] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-05-27 05:57:00,196] INFO _appmap.configuration: env: environ({'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'PYTHONUNBUFFERED': '1', 'DB_REPLICA_USER': 'stockone', 'HOSTNAME': '02117f210751', 'PYTHON_VERSION': '3.12.10', 'DB_REPORTS_USER': 'stockone', 'DB_REPLICA_HOST': 'db1', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'PWD': '/application', 'USE_S3': 'TRUE', 'DB_USER': 'stockone', 'HOME': '/root', 'LANG': 'C.UTF-8', 'DB_REPLICA_NAME': 'stockone', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'TERM': 'xterm', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_HOST': 'db1', 'AWS_ID': '********************', 'SHLVL': '1', 'DB_REPORTS_NAME': 'stockone', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DB_NAME': 'stockone', 'APPROVAL_SERVICE_URL': 'http://************:8001/api/v1/', 'TASK_USAGE': '0', 'DB_REPORTS_PASS': 'stockone^123', 'DB_REPORTS_HOST': 'db1', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'DB_REPLICA_PASS': 'stockone^123', 'AWS_S3_REGION_NAME': 'ap-south-1', 'DEBUG': '1', 'DB_PASS': 'stockone^123', 'ENTRYPOINT_FLAG': 'true', 'CONN_MAX_AGE': '300', 'APPROVAL_ORIGIN_NAME': 'dev-approval-app', '_': '/usr/local/bin/python'})
[2025-05-27 05:57:00,210] INFO _appmap.env: appmap enabled: False
[2025-05-27 05:57:42,179] INFO _appmap.env: appmap enabled: False
[2025-05-27 05:57:42,602] INFO _appmap.configuration: file: /application/appmap.yml
[2025-05-27 05:57:42,604] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-05-27 05:57:42,604] INFO _appmap.configuration: env: environ({'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'PYTHONUNBUFFERED': '1', 'DB_REPLICA_USER': 'stockone', 'HOSTNAME': '02117f210751', 'PYTHON_VERSION': '3.12.10', 'DB_REPORTS_USER': 'stockone', 'DB_REPLICA_HOST': 'db1', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'PWD': '/application', 'USE_S3': 'TRUE', 'DB_USER': 'stockone', 'HOME': '/root', 'LANG': 'C.UTF-8', 'DB_REPLICA_NAME': 'stockone', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'TERM': 'xterm', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_HOST': 'db1', 'AWS_ID': '********************', 'SHLVL': '1', 'DB_REPORTS_NAME': 'stockone', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DB_NAME': 'stockone', 'APPROVAL_SERVICE_URL': 'http://************:8001/api/v1/', 'TASK_USAGE': '0', 'DB_REPORTS_PASS': 'stockone^123', 'DB_REPORTS_HOST': 'db1', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'DB_REPLICA_PASS': 'stockone^123', 'AWS_S3_REGION_NAME': 'ap-south-1', 'DEBUG': '1', 'DB_PASS': 'stockone^123', 'ENTRYPOINT_FLAG': 'true', 'CONN_MAX_AGE': '300', 'APPROVAL_ORIGIN_NAME': 'dev-approval-app', '_': '/usr/local/bin/python'})
[2025-05-27 05:57:42,607] INFO _appmap.env: appmap enabled: False
[2025-05-27 06:02:20,250] INFO _appmap.configuration: file: /application/appmap.yml
[2025-05-27 06:02:20,250] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-05-27 06:02:20,251] INFO _appmap.configuration: env: environ({'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'PYTHONUNBUFFERED': '1', 'DB_REPLICA_USER': 'stockone', 'HOSTNAME': '02117f210751', 'PYTHON_VERSION': '3.12.10', 'DB_REPORTS_USER': 'stockone', 'DB_REPLICA_HOST': 'db1', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'PWD': '/application', 'USE_S3': 'TRUE', 'DB_USER': 'stockone', 'HOME': '/root', 'LANG': 'C.UTF-8', 'DB_REPLICA_NAME': 'stockone', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'TERM': 'xterm', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_HOST': 'db1', 'AWS_ID': '********************', 'SHLVL': '1', 'DB_REPORTS_NAME': 'stockone', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DB_NAME': 'stockone', 'APPROVAL_SERVICE_URL': 'http://************:8001/api/v1/', 'TASK_USAGE': '0', 'DB_REPORTS_PASS': 'stockone^123', 'DB_REPORTS_HOST': 'db1', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'DB_REPLICA_PASS': 'stockone^123', 'AWS_S3_REGION_NAME': 'ap-south-1', 'DEBUG': '1', 'DB_PASS': 'stockone^123', 'ENTRYPOINT_FLAG': 'true', 'CONN_MAX_AGE': '300', 'APPROVAL_ORIGIN_NAME': 'dev-approval-app', '_': '/usr/local/bin/python'})
[2025-05-27 06:02:20,256] INFO _appmap.env: appmap enabled: False
[2025-05-27 06:02:52,737] INFO _appmap.env: appmap enabled: False
[2025-05-27 06:02:58,141] INFO _appmap.configuration: file: /application/appmap.yml
[2025-05-27 06:02:58,141] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-05-27 06:02:58,142] INFO _appmap.configuration: env: environ({'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'PYTHONUNBUFFERED': '1', 'DB_REPLICA_USER': 'stockone', 'HOSTNAME': '02117f210751', 'PYTHON_VERSION': '3.12.10', 'DB_REPORTS_USER': 'stockone', 'DB_REPLICA_HOST': 'db1', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'PWD': '/application', 'USE_S3': 'TRUE', 'DB_USER': 'stockone', 'HOME': '/root', 'LANG': 'C.UTF-8', 'DB_REPLICA_NAME': 'stockone', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'TERM': 'xterm', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_HOST': 'db1', 'AWS_ID': '********************', 'SHLVL': '1', 'DB_REPORTS_NAME': 'stockone', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DB_NAME': 'stockone', 'APPROVAL_SERVICE_URL': 'http://************:8001/api/v1/', 'TASK_USAGE': '0', 'DB_REPORTS_PASS': 'stockone^123', 'DB_REPORTS_HOST': 'db1', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'DB_REPLICA_PASS': 'stockone^123', 'AWS_S3_REGION_NAME': 'ap-south-1', 'DEBUG': '1', 'DB_PASS': 'stockone^123', 'ENTRYPOINT_FLAG': 'true', 'CONN_MAX_AGE': '300', 'APPROVAL_ORIGIN_NAME': 'dev-approval-app', '_': '/usr/local/bin/python'})
[2025-05-27 06:02:58,145] INFO _appmap.env: appmap enabled: False
[2025-05-27 06:04:39,125] INFO _appmap.env: appmap enabled: False
[2025-05-27 06:05:31,759] INFO _appmap.env: appmap enabled: False
[2025-05-27 06:06:01,147] INFO _appmap.env: appmap enabled: False
[2025-05-27 06:06:40,771] INFO _appmap.env: appmap enabled: False
[2025-05-27 06:06:52,303] INFO _appmap.env: appmap enabled: False
[2025-05-27 06:07:43,444] INFO _appmap.env: appmap enabled: False
[2025-05-27 06:08:30,538] INFO _appmap.env: appmap enabled: False
[2025-05-27 06:09:01,887] INFO _appmap.env: appmap enabled: False
[2025-05-27 06:09:04,327] INFO _appmap.configuration: file: /application/appmap.yml
[2025-05-27 06:09:04,329] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-05-27 06:09:04,329] INFO _appmap.configuration: env: environ({'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'PYTHONUNBUFFERED': '1', 'DB_REPLICA_USER': 'stockone', 'HOSTNAME': '02117f210751', 'PYTHON_VERSION': '3.12.10', 'DB_REPORTS_USER': 'stockone', 'DB_REPLICA_HOST': 'db1', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'PWD': '/application', 'USE_S3': 'TRUE', 'DB_USER': 'stockone', 'HOME': '/root', 'LANG': 'C.UTF-8', 'DB_REPLICA_NAME': 'stockone', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'TERM': 'xterm', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_HOST': 'db1', 'AWS_ID': '********************', 'SHLVL': '1', 'DB_REPORTS_NAME': 'stockone', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DB_NAME': 'stockone', 'APPROVAL_SERVICE_URL': 'http://************:8001/api/v1/', 'TASK_USAGE': '0', 'DB_REPORTS_PASS': 'stockone^123', 'DB_REPORTS_HOST': 'db1', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'DB_REPLICA_PASS': 'stockone^123', 'AWS_S3_REGION_NAME': 'ap-south-1', 'DEBUG': '1', 'DB_PASS': 'stockone^123', 'ENTRYPOINT_FLAG': 'true', 'CONN_MAX_AGE': '300', 'APPROVAL_ORIGIN_NAME': 'dev-approval-app', '_': '/usr/local/bin/python'})
[2025-05-27 06:09:04,336] INFO _appmap.env: appmap enabled: False
[2025-05-27 06:11:50,734] INFO _appmap.env: appmap enabled: False
[2025-05-27 06:12:30,751] INFO _appmap.env: appmap enabled: False
[2025-05-27 06:12:32,515] INFO _appmap.env: appmap enabled: False
[2025-05-27 06:12:51,243] INFO _appmap.env: appmap enabled: False
[2025-05-27 06:14:04,410] INFO _appmap.env: appmap enabled: False
[2025-05-27 06:14:14,933] INFO _appmap.env: appmap enabled: False
[2025-05-27 06:15:59,869] INFO _appmap.env: appmap enabled: False
[2025-05-27 06:16:04,796] INFO _appmap.env: appmap enabled: False
[2025-05-27 06:16:09,470] INFO _appmap.env: appmap enabled: False
[2025-05-27 06:16:21,705] INFO _appmap.env: appmap enabled: False
[2025-05-27 06:17:02,577] INFO _appmap.env: appmap enabled: False
[2025-05-27 06:17:58,244] INFO _appmap.env: appmap enabled: False
[2025-05-27 06:17:59,600] INFO _appmap.configuration: file: /application/appmap.yml
[2025-05-27 06:17:59,600] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-05-27 06:17:59,600] INFO _appmap.configuration: env: environ({'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'PYTHONUNBUFFERED': '1', 'DB_REPLICA_USER': 'stockone', 'HOSTNAME': '02117f210751', 'PYTHON_VERSION': '3.12.10', 'DB_REPORTS_USER': 'stockone', 'DB_REPLICA_HOST': 'db1', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'PWD': '/application', 'USE_S3': 'TRUE', 'DB_USER': 'stockone', 'HOME': '/root', 'LANG': 'C.UTF-8', 'DB_REPLICA_NAME': 'stockone', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'TERM': 'xterm', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_HOST': 'db1', 'AWS_ID': '********************', 'SHLVL': '1', 'DB_REPORTS_NAME': 'stockone', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DB_NAME': 'stockone', 'APPROVAL_SERVICE_URL': 'http://************:8001/api/v1/', 'TASK_USAGE': '0', 'DB_REPORTS_PASS': 'stockone^123', 'DB_REPORTS_HOST': 'db1', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'DB_REPLICA_PASS': 'stockone^123', 'AWS_S3_REGION_NAME': 'ap-south-1', 'DEBUG': '1', 'DB_PASS': 'stockone^123', 'ENTRYPOINT_FLAG': 'true', 'CONN_MAX_AGE': '300', 'APPROVAL_ORIGIN_NAME': 'dev-approval-app', '_': '/usr/local/bin/python'})
[2025-05-27 06:17:59,604] INFO _appmap.env: appmap enabled: False
[2025-05-27 06:20:40,292] INFO _appmap.env: appmap enabled: False
[2025-05-27 06:22:07,117] INFO _appmap.env: appmap enabled: False
[2025-05-27 06:22:12,801] INFO _appmap.env: appmap enabled: False
[2025-05-27 06:22:17,472] INFO _appmap.env: appmap enabled: False
[2025-05-27 06:24:27,316] INFO _appmap.env: appmap enabled: False
[2025-05-27 06:29:36,405] INFO _appmap.env: appmap enabled: False
[2025-05-27 06:29:55,056] INFO _appmap.env: appmap enabled: False
[2025-05-27 06:32:50,908] INFO _appmap.env: appmap enabled: False
[2025-05-27 06:33:02,450] INFO _appmap.env: appmap enabled: False
[2025-05-27 06:33:53,719] INFO _appmap.env: appmap enabled: False
[2025-05-27 06:34:02,004] INFO _appmap.env: appmap enabled: False
[2025-05-27 06:34:04,742] INFO _appmap.configuration: file: /application/appmap.yml
[2025-05-27 06:34:04,742] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-05-27 06:34:04,743] INFO _appmap.configuration: env: environ({'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'PYTHONUNBUFFERED': '1', 'DB_REPLICA_USER': 'stockone', 'HOSTNAME': '02117f210751', 'PYTHON_VERSION': '3.12.10', 'DB_REPORTS_USER': 'stockone', 'DB_REPLICA_HOST': 'db1', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'PWD': '/application', 'USE_S3': 'TRUE', 'DB_USER': 'stockone', 'HOME': '/root', 'LANG': 'C.UTF-8', 'DB_REPLICA_NAME': 'stockone', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'TERM': 'xterm', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_HOST': 'db1', 'AWS_ID': '********************', 'SHLVL': '1', 'DB_REPORTS_NAME': 'stockone', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DB_NAME': 'stockone', 'APPROVAL_SERVICE_URL': 'http://************:8001/api/v1/', 'TASK_USAGE': '0', 'DB_REPORTS_PASS': 'stockone^123', 'DB_REPORTS_HOST': 'db1', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'DB_REPLICA_PASS': 'stockone^123', 'AWS_S3_REGION_NAME': 'ap-south-1', 'DEBUG': '1', 'DB_PASS': 'stockone^123', 'ENTRYPOINT_FLAG': 'true', 'CONN_MAX_AGE': '300', 'APPROVAL_ORIGIN_NAME': 'dev-approval-app', '_': '/usr/local/bin/python'})
[2025-05-27 06:34:04,747] INFO _appmap.env: appmap enabled: False
[2025-05-27 06:37:50,815] INFO _appmap.env: appmap enabled: False
[2025-05-27 06:38:02,149] INFO _appmap.env: appmap enabled: False
[2025-05-27 06:38:23,273] INFO _appmap.env: appmap enabled: False
[2025-05-27 06:38:32,209] INFO _appmap.env: appmap enabled: False
[2025-05-27 06:38:36,393] INFO _appmap.env: appmap enabled: False
[2025-05-27 06:39:05,043] INFO _appmap.env: appmap enabled: False
[2025-05-27 06:40:14,479] INFO _appmap.env: appmap enabled: False
[2025-05-27 06:40:25,905] INFO _appmap.env: appmap enabled: False
[2025-05-27 06:40:39,393] INFO _appmap.env: appmap enabled: False
[2025-05-27 06:41:07,495] INFO _appmap.env: appmap enabled: False
[2025-05-27 06:41:24,131] INFO _appmap.env: appmap enabled: False
[2025-05-27 06:41:40,417] INFO _appmap.env: appmap enabled: False
[2025-05-27 06:41:47,135] INFO _appmap.env: appmap enabled: False
[2025-05-27 06:44:53,235] INFO _appmap.env: appmap enabled: False
[2025-05-27 06:44:58,762] INFO _appmap.env: appmap enabled: False
[2025-05-27 06:48:00,924] INFO _appmap.env: appmap enabled: False
[2025-05-27 06:48:25,315] INFO _appmap.env: appmap enabled: False
[2025-05-27 06:48:26,591] INFO _appmap.configuration: file: /application/appmap.yml
[2025-05-27 06:48:26,591] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-05-27 06:48:26,591] INFO _appmap.configuration: env: environ({'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'PYTHONUNBUFFERED': '1', 'DB_REPLICA_USER': 'stockone', 'HOSTNAME': '02117f210751', 'PYTHON_VERSION': '3.12.10', 'DB_REPORTS_USER': 'stockone', 'DB_REPLICA_HOST': 'db1', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'PWD': '/application', 'USE_S3': 'TRUE', 'DB_USER': 'stockone', 'HOME': '/root', 'LANG': 'C.UTF-8', 'DB_REPLICA_NAME': 'stockone', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'TERM': 'xterm', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_HOST': 'db1', 'AWS_ID': '********************', 'SHLVL': '1', 'DB_REPORTS_NAME': 'stockone', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DB_NAME': 'stockone', 'APPROVAL_SERVICE_URL': 'http://************:8001/api/v1/', 'TASK_USAGE': '0', 'DB_REPORTS_PASS': 'stockone^123', 'DB_REPORTS_HOST': 'db1', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'DB_REPLICA_PASS': 'stockone^123', 'AWS_S3_REGION_NAME': 'ap-south-1', 'DEBUG': '1', 'DB_PASS': 'stockone^123', 'ENTRYPOINT_FLAG': 'true', 'CONN_MAX_AGE': '300', 'APPROVAL_ORIGIN_NAME': 'dev-approval-app', '_': '/usr/local/bin/python'})
[2025-05-27 06:48:26,597] INFO _appmap.env: appmap enabled: False
[2025-05-27 06:48:38,791] INFO _appmap.configuration: file: /application/appmap.yml
[2025-05-27 06:48:38,791] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-05-27 06:48:38,791] INFO _appmap.configuration: env: environ({'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'PYTHONUNBUFFERED': '1', 'DB_REPLICA_USER': 'stockone', 'HOSTNAME': '02117f210751', 'PYTHON_VERSION': '3.12.10', 'DB_REPORTS_USER': 'stockone', 'DB_REPLICA_HOST': 'db1', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'PWD': '/application', 'USE_S3': 'TRUE', 'DB_USER': 'stockone', 'HOME': '/root', 'LANG': 'C.UTF-8', 'DB_REPLICA_NAME': 'stockone', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'TERM': 'xterm', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_HOST': 'db1', 'AWS_ID': '********************', 'SHLVL': '1', 'DB_REPORTS_NAME': 'stockone', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DB_NAME': 'stockone', 'APPROVAL_SERVICE_URL': 'http://************:8001/api/v1/', 'TASK_USAGE': '0', 'DB_REPORTS_PASS': 'stockone^123', 'DB_REPORTS_HOST': 'db1', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'DB_REPLICA_PASS': 'stockone^123', 'AWS_S3_REGION_NAME': 'ap-south-1', 'DEBUG': '1', 'DB_PASS': 'stockone^123', 'ENTRYPOINT_FLAG': 'true', 'CONN_MAX_AGE': '300', 'APPROVAL_ORIGIN_NAME': 'dev-approval-app', '_': '/usr/local/bin/python'})
[2025-05-27 06:48:38,810] INFO _appmap.env: appmap enabled: False
[2025-05-27 06:49:18,432] INFO _appmap.env: appmap enabled: False
[2025-05-27 06:49:22,890] INFO _appmap.env: appmap enabled: False
[2025-05-27 06:49:24,718] INFO _appmap.configuration: file: /application/appmap.yml
[2025-05-27 06:49:24,718] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-05-27 06:49:24,718] INFO _appmap.configuration: env: environ({'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'PYTHONUNBUFFERED': '1', 'DB_REPLICA_USER': 'stockone', 'HOSTNAME': '02117f210751', 'PYTHON_VERSION': '3.12.10', 'DB_REPORTS_USER': 'stockone', 'DB_REPLICA_HOST': 'db1', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'PWD': '/application', 'USE_S3': 'TRUE', 'DB_USER': 'stockone', 'HOME': '/root', 'LANG': 'C.UTF-8', 'DB_REPLICA_NAME': 'stockone', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'TERM': 'xterm', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_HOST': 'db1', 'AWS_ID': '********************', 'SHLVL': '1', 'DB_REPORTS_NAME': 'stockone', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DB_NAME': 'stockone', 'APPROVAL_SERVICE_URL': 'http://************:8001/api/v1/', 'TASK_USAGE': '0', 'DB_REPORTS_PASS': 'stockone^123', 'DB_REPORTS_HOST': 'db1', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'DB_REPLICA_PASS': 'stockone^123', 'AWS_S3_REGION_NAME': 'ap-south-1', 'DEBUG': '1', 'DB_PASS': 'stockone^123', 'ENTRYPOINT_FLAG': 'true', 'CONN_MAX_AGE': '300', 'APPROVAL_ORIGIN_NAME': 'dev-approval-app', '_': '/usr/local/bin/python'})
[2025-05-27 06:49:24,722] INFO _appmap.env: appmap enabled: False
[2025-05-27 06:51:25,280] INFO _appmap.env: appmap enabled: False
[2025-05-27 06:52:13,730] INFO _appmap.env: appmap enabled: False
[2025-05-27 06:53:29,196] INFO _appmap.env: appmap enabled: False
[2025-05-27 06:54:01,713] INFO _appmap.env: appmap enabled: False
[2025-05-27 06:54:24,716] INFO _appmap.env: appmap enabled: False
[2025-05-27 06:54:43,657] INFO _appmap.env: appmap enabled: False
[2025-05-27 06:55:21,494] INFO _appmap.env: appmap enabled: False
[2025-05-27 06:55:34,042] INFO _appmap.env: appmap enabled: False
[2025-05-27 06:56:01,744] INFO _appmap.configuration: file: /application/appmap.yml
[2025-05-27 06:56:01,746] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-05-27 06:56:01,746] INFO _appmap.configuration: env: environ({'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'PYTHONUNBUFFERED': '1', 'DB_REPLICA_USER': 'stockone', 'HOSTNAME': '02117f210751', 'PYTHON_VERSION': '3.12.10', 'DB_REPORTS_USER': 'stockone', 'DB_REPLICA_HOST': 'db1', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'PWD': '/application', 'USE_S3': 'TRUE', 'DB_USER': 'stockone', 'HOME': '/root', 'LANG': 'C.UTF-8', 'DB_REPLICA_NAME': 'stockone', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'TERM': 'xterm', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_HOST': 'db1', 'AWS_ID': '********************', 'SHLVL': '1', 'DB_REPORTS_NAME': 'stockone', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DB_NAME': 'stockone', 'APPROVAL_SERVICE_URL': 'http://************:8001/api/v1/', 'TASK_USAGE': '0', 'DB_REPORTS_PASS': 'stockone^123', 'DB_REPORTS_HOST': 'db1', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'DB_REPLICA_PASS': 'stockone^123', 'AWS_S3_REGION_NAME': 'ap-south-1', 'DEBUG': '1', 'DB_PASS': 'stockone^123', 'ENTRYPOINT_FLAG': 'true', 'CONN_MAX_AGE': '300', 'APPROVAL_ORIGIN_NAME': 'dev-approval-app', '_': '/usr/local/bin/python'})
[2025-05-27 06:56:01,766] INFO _appmap.env: appmap enabled: False
[2025-05-27 06:58:56,712] INFO _appmap.env: appmap enabled: False
[2025-05-27 07:28:55,098] INFO _appmap.env: appmap enabled: False
[2025-05-27 07:51:12,278] INFO _appmap.env: appmap enabled: False
[2025-05-27 07:57:14,527] INFO _appmap.env: appmap enabled: False
[2025-05-27 08:28:04,647] INFO _appmap.configuration: file: /application/appmap.yml
[2025-05-27 08:28:04,648] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-05-27 08:28:04,648] INFO _appmap.configuration: env: environ({'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'PYTHONUNBUFFERED': '1', 'DB_REPLICA_USER': 'stockone', 'HOSTNAME': '02117f210751', 'PYTHON_VERSION': '3.12.10', 'DB_REPORTS_USER': 'stockone', 'DB_REPLICA_HOST': 'db1', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'PWD': '/application', 'USE_S3': 'TRUE', 'DB_USER': 'stockone', 'HOME': '/root', 'LANG': 'C.UTF-8', 'DB_REPLICA_NAME': 'stockone', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'TERM': 'xterm', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_HOST': 'db1', 'AWS_ID': '********************', 'SHLVL': '1', 'DB_REPORTS_NAME': 'stockone', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DB_NAME': 'stockone', 'APPROVAL_SERVICE_URL': 'http://************:8001/api/v1/', 'TASK_USAGE': '0', 'DB_REPORTS_PASS': 'stockone^123', 'DB_REPORTS_HOST': 'db1', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'DB_REPLICA_PASS': 'stockone^123', 'AWS_S3_REGION_NAME': 'ap-south-1', 'DEBUG': '1', 'DB_PASS': 'stockone^123', 'ENTRYPOINT_FLAG': 'true', 'CONN_MAX_AGE': '300', 'APPROVAL_ORIGIN_NAME': 'dev-approval-app', '_': '/usr/local/bin/python'})
[2025-05-27 08:28:04,670] INFO _appmap.env: appmap enabled: False
[2025-05-27 10:00:39,442] INFO _appmap.env: appmap enabled: False
[2025-05-27 10:00:42,358] INFO _appmap.configuration: file: /application/appmap.yml
[2025-05-27 10:00:42,358] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-05-27 10:00:42,359] INFO _appmap.configuration: env: environ({'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'PYTHONUNBUFFERED': '1', 'DB_REPLICA_USER': 'stockone', 'HOSTNAME': '02117f210751', 'PYTHON_VERSION': '3.12.10', 'DB_REPORTS_USER': 'stockone', 'DB_REPLICA_HOST': 'db1', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'PWD': '/application', 'USE_S3': 'TRUE', 'DB_USER': 'stockone', 'HOME': '/root', 'LANG': 'C.UTF-8', 'DB_REPLICA_NAME': 'stockone', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'TERM': 'xterm', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_HOST': 'db1', 'AWS_ID': '********************', 'SHLVL': '1', 'DB_REPORTS_NAME': 'stockone', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DB_NAME': 'stockone', 'APPROVAL_SERVICE_URL': 'http://************:8001/api/v1/', 'TASK_USAGE': '0', 'DB_REPORTS_PASS': 'stockone^123', 'DB_REPORTS_HOST': 'db1', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'DB_REPLICA_PASS': 'stockone^123', 'AWS_S3_REGION_NAME': 'ap-south-1', 'DEBUG': '1', 'DB_PASS': 'stockone^123', 'ENTRYPOINT_FLAG': 'true', 'CONN_MAX_AGE': '300', 'APPROVAL_ORIGIN_NAME': 'dev-approval-app', '_': '/usr/local/bin/python'})
[2025-05-27 10:00:42,370] INFO _appmap.env: appmap enabled: False
[2025-05-27 10:01:01,850] INFO _appmap.env: appmap enabled: False
[2025-05-27 10:02:21,252] INFO _appmap.env: appmap enabled: False
[2025-05-27 10:02:29,691] INFO _appmap.env: appmap enabled: False
[2025-05-27 10:03:02,577] INFO _appmap.env: appmap enabled: False
[2025-05-27 10:03:04,101] INFO _appmap.configuration: file: /application/appmap.yml
[2025-05-27 10:03:04,101] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-05-27 10:03:04,101] INFO _appmap.configuration: env: environ({'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'PYTHONUNBUFFERED': '1', 'DB_REPLICA_USER': 'stockone', 'HOSTNAME': '02117f210751', 'PYTHON_VERSION': '3.12.10', 'DB_REPORTS_USER': 'stockone', 'DB_REPLICA_HOST': 'db1', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'PWD': '/application', 'USE_S3': 'TRUE', 'DB_USER': 'stockone', 'HOME': '/root', 'LANG': 'C.UTF-8', 'DB_REPLICA_NAME': 'stockone', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'TERM': 'xterm', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_HOST': 'db1', 'AWS_ID': '********************', 'SHLVL': '1', 'DB_REPORTS_NAME': 'stockone', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DB_NAME': 'stockone', 'APPROVAL_SERVICE_URL': 'http://************:8001/api/v1/', 'TASK_USAGE': '0', 'DB_REPORTS_PASS': 'stockone^123', 'DB_REPORTS_HOST': 'db1', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'DB_REPLICA_PASS': 'stockone^123', 'AWS_S3_REGION_NAME': 'ap-south-1', 'DEBUG': '1', 'DB_PASS': 'stockone^123', 'ENTRYPOINT_FLAG': 'true', 'CONN_MAX_AGE': '300', 'APPROVAL_ORIGIN_NAME': 'dev-approval-app', '_': '/usr/local/bin/python'})
[2025-05-27 10:03:04,106] INFO _appmap.env: appmap enabled: False
[2025-05-27 10:14:57,258] INFO _appmap.env: appmap enabled: False
[2025-05-27 10:14:58,478] INFO _appmap.env: appmap enabled: False
[2025-05-27 10:15:44,278] INFO _appmap.env: appmap enabled: False
[2025-05-27 10:17:18,485] INFO _appmap.env: appmap enabled: False
[2025-05-27 10:19:37,784] INFO _appmap.env: appmap enabled: False
[2025-05-27 10:19:57,140] INFO _appmap.env: appmap enabled: False
[2025-05-27 10:20:05,239] INFO _appmap.env: appmap enabled: False
[2025-05-27 10:21:00,449] INFO _appmap.env: appmap enabled: False
[2025-05-27 10:21:16,493] INFO _appmap.env: appmap enabled: False
[2025-05-27 10:21:17,913] INFO _appmap.env: appmap enabled: False
[2025-05-27 10:21:25,809] INFO _appmap.env: appmap enabled: False
[2025-05-27 10:22:26,065] INFO _appmap.env: appmap enabled: False
[2025-05-27 10:22:27,802] INFO _appmap.configuration: file: /application/appmap.yml
[2025-05-27 10:22:27,802] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-05-27 10:22:27,802] INFO _appmap.configuration: env: environ({'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'PYTHONUNBUFFERED': '1', 'DB_REPLICA_USER': 'stockone', 'HOSTNAME': '02117f210751', 'PYTHON_VERSION': '3.12.10', 'DB_REPORTS_USER': 'stockone', 'DB_REPLICA_HOST': 'db1', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'PWD': '/application', 'USE_S3': 'TRUE', 'DB_USER': 'stockone', 'HOME': '/root', 'LANG': 'C.UTF-8', 'DB_REPLICA_NAME': 'stockone', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'TERM': 'xterm', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_HOST': 'db1', 'AWS_ID': '********************', 'SHLVL': '1', 'DB_REPORTS_NAME': 'stockone', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DB_NAME': 'stockone', 'APPROVAL_SERVICE_URL': 'http://************:8001/api/v1/', 'TASK_USAGE': '0', 'DB_REPORTS_PASS': 'stockone^123', 'DB_REPORTS_HOST': 'db1', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'DB_REPLICA_PASS': 'stockone^123', 'AWS_S3_REGION_NAME': 'ap-south-1', 'DEBUG': '1', 'DB_PASS': 'stockone^123', 'ENTRYPOINT_FLAG': 'true', 'CONN_MAX_AGE': '300', 'APPROVAL_ORIGIN_NAME': 'dev-approval-app', '_': '/usr/local/bin/python'})
[2025-05-27 10:22:27,808] INFO _appmap.env: appmap enabled: False
[2025-05-27 10:24:33,233] INFO _appmap.env: appmap enabled: False
[2025-05-27 10:24:34,501] INFO _appmap.configuration: file: /application/appmap.yml
[2025-05-27 10:24:34,501] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-05-27 10:24:34,502] INFO _appmap.configuration: env: environ({'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'PYTHONUNBUFFERED': '1', 'DB_REPLICA_USER': 'stockone', 'HOSTNAME': '02117f210751', 'PYTHON_VERSION': '3.12.10', 'DB_REPORTS_USER': 'stockone', 'DB_REPLICA_HOST': 'db1', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'PWD': '/application', 'USE_S3': 'TRUE', 'DB_USER': 'stockone', 'HOME': '/root', 'LANG': 'C.UTF-8', 'DB_REPLICA_NAME': 'stockone', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'TERM': 'xterm', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_HOST': 'db1', 'AWS_ID': '********************', 'SHLVL': '1', 'DB_REPORTS_NAME': 'stockone', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DB_NAME': 'stockone', 'APPROVAL_SERVICE_URL': 'http://************:8001/api/v1/', 'TASK_USAGE': '0', 'DB_REPORTS_PASS': 'stockone^123', 'DB_REPORTS_HOST': 'db1', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'DB_REPLICA_PASS': 'stockone^123', 'AWS_S3_REGION_NAME': 'ap-south-1', 'DEBUG': '1', 'DB_PASS': 'stockone^123', 'ENTRYPOINT_FLAG': 'true', 'CONN_MAX_AGE': '300', 'APPROVAL_ORIGIN_NAME': 'dev-approval-app', '_': '/usr/local/bin/python'})
[2025-05-27 10:24:34,506] INFO _appmap.env: appmap enabled: False
[2025-05-27 10:26:10,673] INFO _appmap.configuration: file: /application/appmap.yml
[2025-05-27 10:26:10,674] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-05-27 10:26:10,674] INFO _appmap.configuration: env: environ({'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'PYTHONUNBUFFERED': '1', 'DB_REPLICA_USER': 'stockone', 'HOSTNAME': '02117f210751', 'PYTHON_VERSION': '3.12.10', 'DB_REPORTS_USER': 'stockone', 'DB_REPLICA_HOST': 'db1', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'PWD': '/application', 'USE_S3': 'TRUE', 'DB_USER': 'stockone', 'HOME': '/root', 'LANG': 'C.UTF-8', 'DB_REPLICA_NAME': 'stockone', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'TERM': 'xterm', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_HOST': 'db1', 'AWS_ID': '********************', 'SHLVL': '1', 'DB_REPORTS_NAME': 'stockone', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DB_NAME': 'stockone', 'APPROVAL_SERVICE_URL': 'http://************:8001/api/v1/', 'TASK_USAGE': '0', 'DB_REPORTS_PASS': 'stockone^123', 'DB_REPORTS_HOST': 'db1', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'DB_REPLICA_PASS': 'stockone^123', 'AWS_S3_REGION_NAME': 'ap-south-1', 'DEBUG': '1', 'DB_PASS': 'stockone^123', 'ENTRYPOINT_FLAG': 'true', 'CONN_MAX_AGE': '300', 'APPROVAL_ORIGIN_NAME': 'dev-approval-app', '_': '/usr/local/bin/python'})
[2025-05-27 10:26:10,677] INFO _appmap.env: appmap enabled: False
[2025-05-27 10:26:37,041] INFO _appmap.env: appmap enabled: False
[2025-05-27 10:26:40,750] INFO _appmap.env: appmap enabled: False
[2025-05-27 10:26:45,138] INFO _appmap.configuration: file: /application/appmap.yml
[2025-05-27 10:26:45,138] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-05-27 10:26:45,138] INFO _appmap.configuration: env: environ({'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'PYTHONUNBUFFERED': '1', 'DB_REPLICA_USER': 'stockone', 'HOSTNAME': '02117f210751', 'PYTHON_VERSION': '3.12.10', 'DB_REPORTS_USER': 'stockone', 'DB_REPLICA_HOST': 'db1', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'PWD': '/application', 'USE_S3': 'TRUE', 'DB_USER': 'stockone', 'HOME': '/root', 'LANG': 'C.UTF-8', 'DB_REPLICA_NAME': 'stockone', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'TERM': 'xterm', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_HOST': 'db1', 'AWS_ID': '********************', 'SHLVL': '1', 'DB_REPORTS_NAME': 'stockone', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DB_NAME': 'stockone', 'APPROVAL_SERVICE_URL': 'http://************:8001/api/v1/', 'TASK_USAGE': '0', 'DB_REPORTS_PASS': 'stockone^123', 'DB_REPORTS_HOST': 'db1', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'DB_REPLICA_PASS': 'stockone^123', 'AWS_S3_REGION_NAME': 'ap-south-1', 'DEBUG': '1', 'DB_PASS': 'stockone^123', 'ENTRYPOINT_FLAG': 'true', 'CONN_MAX_AGE': '300', 'APPROVAL_ORIGIN_NAME': 'dev-approval-app', '_': '/usr/local/bin/python'})
[2025-05-27 10:26:45,152] INFO _appmap.env: appmap enabled: False
[2025-05-27 10:41:24,295] INFO _appmap.env: appmap enabled: False
[2025-05-27 10:41:44,262] INFO _appmap.env: appmap enabled: False
[2025-05-27 10:43:48,236] INFO _appmap.env: appmap enabled: False
[2025-05-27 10:43:52,098] INFO _appmap.env: appmap enabled: False
[2025-05-27 10:44:14,969] INFO _appmap.env: appmap enabled: False
[2025-05-27 10:44:27,301] INFO _appmap.env: appmap enabled: False
[2025-05-27 10:44:36,478] INFO _appmap.env: appmap enabled: False
[2025-05-27 10:46:03,037] INFO _appmap.env: appmap enabled: False
[2025-05-27 10:47:14,090] INFO _appmap.env: appmap enabled: False
[2025-05-27 10:47:22,214] INFO _appmap.env: appmap enabled: False
[2025-05-27 10:49:24,694] INFO _appmap.env: appmap enabled: False
[2025-05-27 10:49:51,400] INFO _appmap.env: appmap enabled: False
[2025-05-27 10:49:52,655] INFO _appmap.env: appmap enabled: False
[2025-05-27 10:52:09,148] INFO _appmap.env: appmap enabled: False
[2025-05-27 10:52:22,733] INFO _appmap.env: appmap enabled: False
[2025-05-27 10:54:35,130] INFO _appmap.env: appmap enabled: False
[2025-05-27 10:54:37,707] INFO _appmap.env: appmap enabled: False
[2025-05-27 10:54:45,289] INFO _appmap.env: appmap enabled: False
[2025-05-27 10:55:46,555] INFO _appmap.env: appmap enabled: False
[2025-05-27 10:56:26,241] INFO _appmap.env: appmap enabled: False
[2025-05-27 10:57:30,072] INFO _appmap.env: appmap enabled: False
[2025-05-27 10:58:59,936] INFO _appmap.env: appmap enabled: False
[2025-05-27 10:59:21,208] INFO _appmap.env: appmap enabled: False
[2025-05-27 11:00:55,262] INFO _appmap.env: appmap enabled: False
[2025-05-27 11:00:56,471] INFO _appmap.env: appmap enabled: False
[2025-05-27 11:01:01,512] INFO _appmap.env: appmap enabled: False
[2025-05-27 11:18:11,477] INFO _appmap.env: appmap enabled: False
[2025-05-27 11:21:24,170] INFO _appmap.env: appmap enabled: False
[2025-05-27 11:22:17,789] INFO _appmap.env: appmap enabled: False
[2025-05-27 11:22:49,190] INFO _appmap.env: appmap enabled: False
[2025-05-27 11:23:05,578] INFO _appmap.env: appmap enabled: False
[2025-05-27 11:23:25,090] INFO _appmap.env: appmap enabled: False
[2025-05-27 11:23:29,159] INFO _appmap.env: appmap enabled: False
[2025-05-27 11:23:54,746] INFO _appmap.env: appmap enabled: False
[2025-05-27 11:24:34,056] INFO _appmap.env: appmap enabled: False
[2025-05-27 11:24:46,649] INFO _appmap.env: appmap enabled: False
[2025-05-27 11:24:47,875] INFO _appmap.env: appmap enabled: False
[2025-05-27 11:24:56,660] INFO _appmap.env: appmap enabled: False
[2025-05-27 11:25:02,477] INFO _appmap.configuration: file: /application/appmap.yml
[2025-05-27 11:25:02,477] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-05-27 11:25:02,477] INFO _appmap.configuration: env: environ({'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'PYTHONUNBUFFERED': '1', 'DB_REPLICA_USER': 'stockone', 'HOSTNAME': '02117f210751', 'PYTHON_VERSION': '3.12.10', 'DB_REPORTS_USER': 'stockone', 'DB_REPLICA_HOST': 'db1', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'PWD': '/application', 'USE_S3': 'TRUE', 'DB_USER': 'stockone', 'HOME': '/root', 'LANG': 'C.UTF-8', 'DB_REPLICA_NAME': 'stockone', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'TERM': 'xterm', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_HOST': 'db1', 'AWS_ID': '********************', 'SHLVL': '1', 'DB_REPORTS_NAME': 'stockone', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DB_NAME': 'stockone', 'APPROVAL_SERVICE_URL': 'http://************:8001/api/v1/', 'TASK_USAGE': '0', 'DB_REPORTS_PASS': 'stockone^123', 'DB_REPORTS_HOST': 'db1', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'DB_REPLICA_PASS': 'stockone^123', 'AWS_S3_REGION_NAME': 'ap-south-1', 'DEBUG': '1', 'DB_PASS': 'stockone^123', 'ENTRYPOINT_FLAG': 'true', 'CONN_MAX_AGE': '300', 'APPROVAL_ORIGIN_NAME': 'dev-approval-app', '_': '/usr/local/bin/python'})
[2025-05-27 11:25:02,481] INFO _appmap.env: appmap enabled: False
[2025-05-27 11:25:19,682] INFO _appmap.env: appmap enabled: False
[2025-05-27 11:25:25,965] INFO _appmap.env: appmap enabled: False
[2025-05-27 11:25:26,773] INFO _appmap.configuration: file: /application/appmap.yml
[2025-05-27 11:25:26,773] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-05-27 11:25:26,773] INFO _appmap.configuration: env: environ({'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'PYTHONUNBUFFERED': '1', 'DB_REPLICA_USER': 'stockone', 'HOSTNAME': '02117f210751', 'PYTHON_VERSION': '3.12.10', 'DB_REPORTS_USER': 'stockone', 'DB_REPLICA_HOST': 'db1', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'PWD': '/application', 'USE_S3': 'TRUE', 'DB_USER': 'stockone', 'HOME': '/root', 'LANG': 'C.UTF-8', 'DB_REPLICA_NAME': 'stockone', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'TERM': 'xterm', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_HOST': 'db1', 'AWS_ID': '********************', 'SHLVL': '1', 'DB_REPORTS_NAME': 'stockone', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DB_NAME': 'stockone', 'APPROVAL_SERVICE_URL': 'http://************:8001/api/v1/', 'TASK_USAGE': '0', 'DB_REPORTS_PASS': 'stockone^123', 'DB_REPORTS_HOST': 'db1', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'DB_REPLICA_PASS': 'stockone^123', 'AWS_S3_REGION_NAME': 'ap-south-1', 'DEBUG': '1', 'DB_PASS': 'stockone^123', 'ENTRYPOINT_FLAG': 'true', 'CONN_MAX_AGE': '300', 'APPROVAL_ORIGIN_NAME': 'dev-approval-app', '_': '/usr/local/bin/python'})
[2025-05-27 11:25:26,777] INFO _appmap.env: appmap enabled: False
[2025-05-27 11:35:33,810] INFO _appmap.env: appmap enabled: False
[2025-05-27 11:36:16,071] INFO _appmap.env: appmap enabled: False
[2025-05-27 11:36:29,468] INFO _appmap.env: appmap enabled: False
[2025-05-27 11:37:12,265] INFO _appmap.env: appmap enabled: False
[2025-05-27 11:37:30,573] INFO _appmap.env: appmap enabled: False
[2025-05-27 11:37:33,838] INFO _appmap.env: appmap enabled: False
[2025-05-27 11:38:37,389] INFO _appmap.env: appmap enabled: False
[2025-05-27 11:38:38,504] INFO _appmap.env: appmap enabled: False
[2025-05-27 11:38:39,640] INFO _appmap.env: appmap enabled: False
[2025-05-27 11:38:42,509] INFO _appmap.configuration: file: /application/appmap.yml
[2025-05-27 11:38:42,510] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-05-27 11:38:42,510] INFO _appmap.configuration: env: environ({'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'PYTHONUNBUFFERED': '1', 'DB_REPLICA_USER': 'stockone', 'HOSTNAME': '02117f210751', 'PYTHON_VERSION': '3.12.10', 'DB_REPORTS_USER': 'stockone', 'DB_REPLICA_HOST': 'db1', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'PWD': '/application', 'USE_S3': 'TRUE', 'DB_USER': 'stockone', 'HOME': '/root', 'LANG': 'C.UTF-8', 'DB_REPLICA_NAME': 'stockone', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'TERM': 'xterm', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_HOST': 'db1', 'AWS_ID': '********************', 'SHLVL': '1', 'DB_REPORTS_NAME': 'stockone', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DB_NAME': 'stockone', 'APPROVAL_SERVICE_URL': 'http://************:8001/api/v1/', 'TASK_USAGE': '0', 'DB_REPORTS_PASS': 'stockone^123', 'DB_REPORTS_HOST': 'db1', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'DB_REPLICA_PASS': 'stockone^123', 'AWS_S3_REGION_NAME': 'ap-south-1', 'DEBUG': '1', 'DB_PASS': 'stockone^123', 'ENTRYPOINT_FLAG': 'true', 'CONN_MAX_AGE': '300', 'APPROVAL_ORIGIN_NAME': 'dev-approval-app', '_': '/usr/local/bin/python'})
[2025-05-27 11:38:42,515] INFO _appmap.env: appmap enabled: False
[2025-05-27 11:45:38,610] INFO _appmap.env: appmap enabled: False
[2025-05-27 11:45:46,198] INFO _appmap.env: appmap enabled: False
[2025-05-27 11:45:47,721] INFO _appmap.env: appmap enabled: False
[2025-05-27 11:46:01,138] INFO _appmap.env: appmap enabled: False
[2025-05-27 11:46:10,874] INFO _appmap.env: appmap enabled: False
[2025-05-27 11:46:15,573] INFO _appmap.env: appmap enabled: False
[2025-05-27 11:46:16,783] INFO _appmap.env: appmap enabled: False
[2025-05-27 11:46:31,308] INFO _appmap.env: appmap enabled: False
[2025-05-27 11:47:23,215] INFO _appmap.env: appmap enabled: False
[2025-05-27 11:47:52,241] INFO _appmap.env: appmap enabled: False
[2025-05-27 11:47:53,896] INFO _appmap.env: appmap enabled: False
[2025-05-27 11:48:48,138] INFO _appmap.env: appmap enabled: False
[2025-05-27 11:49:20,652] INFO _appmap.env: appmap enabled: False
[2025-05-27 11:49:41,159] INFO _appmap.env: appmap enabled: False
[2025-05-27 11:50:13,245] INFO _appmap.env: appmap enabled: False
[2025-05-27 11:51:08,109] INFO _appmap.env: appmap enabled: False
[2025-05-27 11:51:14,389] INFO _appmap.env: appmap enabled: False
[2025-05-27 11:51:36,557] INFO _appmap.env: appmap enabled: False
[2025-05-27 11:51:48,734] INFO _appmap.env: appmap enabled: False
[2025-05-27 11:51:51,894] INFO _appmap.env: appmap enabled: False
[2025-05-27 11:52:19,502] INFO _appmap.env: appmap enabled: False
[2025-05-27 11:52:21,125] INFO _appmap.env: appmap enabled: False
[2025-05-27 11:52:32,179] INFO _appmap.env: appmap enabled: False
[2025-05-27 11:52:34,371] INFO _appmap.env: appmap enabled: False
[2025-05-27 11:52:36,331] INFO _appmap.configuration: file: /application/appmap.yml
[2025-05-27 11:52:36,332] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-05-27 11:52:36,332] INFO _appmap.configuration: env: environ({'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'PYTHONUNBUFFERED': '1', 'DB_REPLICA_USER': 'stockone', 'HOSTNAME': '02117f210751', 'PYTHON_VERSION': '3.12.10', 'DB_REPORTS_USER': 'stockone', 'DB_REPLICA_HOST': 'db1', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'PWD': '/application', 'USE_S3': 'TRUE', 'DB_USER': 'stockone', 'HOME': '/root', 'LANG': 'C.UTF-8', 'DB_REPLICA_NAME': 'stockone', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'TERM': 'xterm', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_HOST': 'db1', 'AWS_ID': '********************', 'SHLVL': '1', 'DB_REPORTS_NAME': 'stockone', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DB_NAME': 'stockone', 'APPROVAL_SERVICE_URL': 'http://************:8001/api/v1/', 'TASK_USAGE': '0', 'DB_REPORTS_PASS': 'stockone^123', 'DB_REPORTS_HOST': 'db1', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'DB_REPLICA_PASS': 'stockone^123', 'AWS_S3_REGION_NAME': 'ap-south-1', 'DEBUG': '1', 'DB_PASS': 'stockone^123', 'ENTRYPOINT_FLAG': 'true', 'CONN_MAX_AGE': '300', 'APPROVAL_ORIGIN_NAME': 'dev-approval-app', '_': '/usr/local/bin/python'})
[2025-05-27 11:52:36,336] INFO _appmap.env: appmap enabled: False
[2025-05-27 11:54:01,046] INFO _appmap.env: appmap enabled: False
[2025-05-27 11:54:37,817] INFO _appmap.env: appmap enabled: False
[2025-05-27 11:56:35,065] INFO _appmap.env: appmap enabled: False
[2025-05-27 11:56:44,061] INFO _appmap.env: appmap enabled: False
[2025-05-27 11:56:51,126] INFO _appmap.env: appmap enabled: False
[2025-05-27 11:57:01,874] INFO _appmap.env: appmap enabled: False
[2025-05-27 11:57:08,144] INFO _appmap.env: appmap enabled: False
[2025-05-27 11:57:10,913] INFO _appmap.env: appmap enabled: False
[2025-05-27 11:57:14,638] INFO _appmap.env: appmap enabled: False
[2025-05-27 11:57:30,124] INFO _appmap.env: appmap enabled: False
[2025-05-27 11:57:39,548] INFO _appmap.env: appmap enabled: False
[2025-05-27 11:57:53,913] INFO _appmap.configuration: file: /application/appmap.yml
[2025-05-27 11:57:53,913] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-05-27 11:57:53,913] INFO _appmap.configuration: env: environ({'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'PYTHONUNBUFFERED': '1', 'DB_REPLICA_USER': 'stockone', 'HOSTNAME': '02117f210751', 'PYTHON_VERSION': '3.12.10', 'DB_REPORTS_USER': 'stockone', 'DB_REPLICA_HOST': 'db1', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'PWD': '/application', 'USE_S3': 'TRUE', 'DB_USER': 'stockone', 'HOME': '/root', 'LANG': 'C.UTF-8', 'DB_REPLICA_NAME': 'stockone', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'TERM': 'xterm', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_HOST': 'db1', 'AWS_ID': '********************', 'SHLVL': '1', 'DB_REPORTS_NAME': 'stockone', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DB_NAME': 'stockone', 'APPROVAL_SERVICE_URL': 'http://************:8001/api/v1/', 'TASK_USAGE': '0', 'DB_REPORTS_PASS': 'stockone^123', 'DB_REPORTS_HOST': 'db1', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'DB_REPLICA_PASS': 'stockone^123', 'AWS_S3_REGION_NAME': 'ap-south-1', 'DEBUG': '1', 'DB_PASS': 'stockone^123', 'ENTRYPOINT_FLAG': 'true', 'CONN_MAX_AGE': '300', 'APPROVAL_ORIGIN_NAME': 'dev-approval-app', '_': '/usr/local/bin/python'})
[2025-05-27 11:57:53,918] INFO _appmap.env: appmap enabled: False
[2025-05-27 12:00:31,450] INFO _appmap.env: appmap enabled: False
[2025-05-27 12:19:22,958] INFO _appmap.env: appmap enabled: False
[2025-05-27 12:20:18,218] INFO _appmap.env: appmap enabled: False
[2025-05-27 12:20:55,719] INFO _appmap.configuration: file: /application/appmap.yml
[2025-05-27 12:20:55,720] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-05-27 12:20:55,720] INFO _appmap.configuration: env: environ({'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'PYTHONUNBUFFERED': '1', 'DB_REPLICA_USER': 'stockone', 'HOSTNAME': '02117f210751', 'PYTHON_VERSION': '3.12.10', 'DB_REPORTS_USER': 'stockone', 'DB_REPLICA_HOST': 'db1', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'PWD': '/application', 'USE_S3': 'TRUE', 'DB_USER': 'stockone', 'HOME': '/root', 'LANG': 'C.UTF-8', 'DB_REPLICA_NAME': 'stockone', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'TERM': 'xterm', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_HOST': 'db1', 'AWS_ID': '********************', 'SHLVL': '1', 'DB_REPORTS_NAME': 'stockone', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DB_NAME': 'stockone', 'APPROVAL_SERVICE_URL': 'http://************:8001/api/v1/', 'TASK_USAGE': '0', 'DB_REPORTS_PASS': 'stockone^123', 'DB_REPORTS_HOST': 'db1', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'DB_REPLICA_PASS': 'stockone^123', 'AWS_S3_REGION_NAME': 'ap-south-1', 'DEBUG': '1', 'DB_PASS': 'stockone^123', 'ENTRYPOINT_FLAG': 'true', 'CONN_MAX_AGE': '300', 'APPROVAL_ORIGIN_NAME': 'dev-approval-app', '_': '/usr/local/bin/python'})
[2025-05-27 12:20:55,724] INFO _appmap.env: appmap enabled: False
[2025-05-27 12:21:30,378] INFO _appmap.env: appmap enabled: False
[2025-05-27 12:21:50,303] INFO _appmap.env: appmap enabled: False
[2025-05-27 12:22:43,871] INFO _appmap.env: appmap enabled: False
[2025-05-27 12:24:15,914] INFO _appmap.env: appmap enabled: False
[2025-05-27 12:24:40,504] INFO _appmap.env: appmap enabled: False
[2025-05-27 12:24:47,495] INFO _appmap.env: appmap enabled: False
[2025-05-27 12:25:06,960] INFO _appmap.env: appmap enabled: False
[2025-05-27 12:25:08,214] INFO _appmap.configuration: file: /application/appmap.yml
[2025-05-27 12:25:08,215] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-05-27 12:25:08,215] INFO _appmap.configuration: env: environ({'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'PYTHONUNBUFFERED': '1', 'DB_REPLICA_USER': 'stockone', 'HOSTNAME': '02117f210751', 'PYTHON_VERSION': '3.12.10', 'DB_REPORTS_USER': 'stockone', 'DB_REPLICA_HOST': 'db1', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'PWD': '/application', 'USE_S3': 'TRUE', 'DB_USER': 'stockone', 'HOME': '/root', 'LANG': 'C.UTF-8', 'DB_REPLICA_NAME': 'stockone', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'TERM': 'xterm', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_HOST': 'db1', 'AWS_ID': '********************', 'SHLVL': '1', 'DB_REPORTS_NAME': 'stockone', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DB_NAME': 'stockone', 'APPROVAL_SERVICE_URL': 'http://************:8001/api/v1/', 'TASK_USAGE': '0', 'DB_REPORTS_PASS': 'stockone^123', 'DB_REPORTS_HOST': 'db1', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'DB_REPLICA_PASS': 'stockone^123', 'AWS_S3_REGION_NAME': 'ap-south-1', 'DEBUG': '1', 'DB_PASS': 'stockone^123', 'ENTRYPOINT_FLAG': 'true', 'CONN_MAX_AGE': '300', 'APPROVAL_ORIGIN_NAME': 'dev-approval-app', '_': '/usr/local/bin/python'})
[2025-05-27 12:25:08,219] INFO _appmap.env: appmap enabled: False
[2025-05-27 12:29:45,414] INFO _appmap.env: appmap enabled: False
[2025-05-27 12:30:12,571] INFO _appmap.env: appmap enabled: False
[2025-05-27 12:30:20,621] INFO _appmap.env: appmap enabled: False
[2025-05-27 12:32:35,693] INFO _appmap.env: appmap enabled: False
[2025-05-27 12:32:57,624] INFO _appmap.env: appmap enabled: False
[2025-05-27 12:34:27,120] INFO _appmap.env: appmap enabled: False
[2025-05-27 12:34:34,585] INFO _appmap.env: appmap enabled: False
[2025-05-27 12:34:47,533] INFO _appmap.env: appmap enabled: False
[2025-05-27 12:35:05,781] INFO _appmap.env: appmap enabled: False
[2025-05-27 12:35:09,066] INFO _appmap.env: appmap enabled: False
[2025-05-27 12:35:11,137] INFO _appmap.env: appmap enabled: False
[2025-05-27 12:35:21,943] INFO _appmap.env: appmap enabled: False
[2025-05-27 12:35:22,760] INFO _appmap.env: appmap enabled: False
[2025-05-27 12:35:25,264] INFO _appmap.env: appmap enabled: False
[2025-05-27 12:35:35,361] INFO _appmap.env: appmap enabled: False
[2025-05-27 12:35:41,000] INFO _appmap.configuration: file: /application/appmap.yml
[2025-05-27 12:35:41,001] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-05-27 12:35:41,001] INFO _appmap.configuration: env: environ({'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'PYTHONUNBUFFERED': '1', 'DB_REPLICA_USER': 'stockone', 'HOSTNAME': '02117f210751', 'PYTHON_VERSION': '3.12.10', 'DB_REPORTS_USER': 'stockone', 'DB_REPLICA_HOST': 'db1', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'PWD': '/application', 'USE_S3': 'TRUE', 'DB_USER': 'stockone', 'HOME': '/root', 'LANG': 'C.UTF-8', 'DB_REPLICA_NAME': 'stockone', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'TERM': 'xterm', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_HOST': 'db1', 'AWS_ID': '********************', 'SHLVL': '1', 'DB_REPORTS_NAME': 'stockone', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DB_NAME': 'stockone', 'APPROVAL_SERVICE_URL': 'http://************:8001/api/v1/', 'TASK_USAGE': '0', 'DB_REPORTS_PASS': 'stockone^123', 'DB_REPORTS_HOST': 'db1', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'DB_REPLICA_PASS': 'stockone^123', 'AWS_S3_REGION_NAME': 'ap-south-1', 'DEBUG': '1', 'DB_PASS': 'stockone^123', 'ENTRYPOINT_FLAG': 'true', 'CONN_MAX_AGE': '300', 'APPROVAL_ORIGIN_NAME': 'dev-approval-app', '_': '/usr/local/bin/python'})
[2025-05-27 12:35:41,005] INFO _appmap.env: appmap enabled: False
[2025-05-27 12:37:32,644] INFO _appmap.env: appmap enabled: False
[2025-05-27 12:37:37,948] INFO _appmap.env: appmap enabled: False
[2025-05-27 12:37:42,169] INFO _appmap.env: appmap enabled: False
[2025-05-27 12:39:55,169] INFO _appmap.env: appmap enabled: False
[2025-05-27 12:39:56,177] INFO _appmap.env: appmap enabled: False
[2025-05-27 12:40:43,423] INFO _appmap.env: appmap enabled: False
[2025-05-27 12:41:50,042] INFO _appmap.env: appmap enabled: False
[2025-05-27 12:41:58,670] INFO _appmap.env: appmap enabled: False
[2025-05-27 12:42:03,982] INFO _appmap.env: appmap enabled: False
[2025-05-27 12:42:26,461] INFO _appmap.env: appmap enabled: False
[2025-05-27 12:44:10,517] INFO _appmap.env: appmap enabled: False
[2025-05-27 12:45:59,407] INFO _appmap.env: appmap enabled: False
[2025-05-27 12:46:00,343] INFO _appmap.env: appmap enabled: False
[2025-05-27 12:46:19,138] INFO _appmap.env: appmap enabled: False
[2025-05-27 12:55:14,652] INFO _appmap.env: appmap enabled: False
[2025-05-27 12:57:10,257] INFO _appmap.env: appmap enabled: False
[2025-05-27 12:57:50,945] INFO _appmap.env: appmap enabled: False
[2025-05-27 12:58:21,277] INFO _appmap.env: appmap enabled: False
[2025-05-27 12:58:28,894] INFO _appmap.env: appmap enabled: False
[2025-05-27 12:58:30,381] INFO _appmap.configuration: file: /application/appmap.yml
[2025-05-27 12:58:30,381] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-05-27 12:58:30,382] INFO _appmap.configuration: env: environ({'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'PYTHONUNBUFFERED': '1', 'DB_REPLICA_USER': 'stockone', 'HOSTNAME': '02117f210751', 'PYTHON_VERSION': '3.12.10', 'DB_REPORTS_USER': 'stockone', 'DB_REPLICA_HOST': 'db1', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'PWD': '/application', 'USE_S3': 'TRUE', 'DB_USER': 'stockone', 'HOME': '/root', 'LANG': 'C.UTF-8', 'DB_REPLICA_NAME': 'stockone', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'TERM': 'xterm', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_HOST': 'db1', 'AWS_ID': '********************', 'SHLVL': '1', 'DB_REPORTS_NAME': 'stockone', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DB_NAME': 'stockone', 'APPROVAL_SERVICE_URL': 'http://************:8001/api/v1/', 'TASK_USAGE': '0', 'DB_REPORTS_PASS': 'stockone^123', 'DB_REPORTS_HOST': 'db1', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'DB_REPLICA_PASS': 'stockone^123', 'AWS_S3_REGION_NAME': 'ap-south-1', 'DEBUG': '1', 'DB_PASS': 'stockone^123', 'ENTRYPOINT_FLAG': 'true', 'CONN_MAX_AGE': '300', 'APPROVAL_ORIGIN_NAME': 'dev-approval-app', '_': '/usr/local/bin/python'})
[2025-05-27 12:58:30,386] INFO _appmap.env: appmap enabled: False
[2025-05-27 12:58:51,846] INFO _appmap.configuration: file: /application/appmap.yml
[2025-05-27 12:58:51,847] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-05-27 12:58:51,847] INFO _appmap.configuration: env: environ({'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'PYTHONUNBUFFERED': '1', 'DB_REPLICA_USER': 'stockone', 'HOSTNAME': '02117f210751', 'PYTHON_VERSION': '3.12.10', 'DB_REPORTS_USER': 'stockone', 'DB_REPLICA_HOST': 'db1', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'PWD': '/application', 'USE_S3': 'TRUE', 'DB_USER': 'stockone', 'HOME': '/root', 'LANG': 'C.UTF-8', 'DB_REPLICA_NAME': 'stockone', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'TERM': 'xterm', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_HOST': 'db1', 'AWS_ID': '********************', 'SHLVL': '1', 'DB_REPORTS_NAME': 'stockone', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DB_NAME': 'stockone', 'APPROVAL_SERVICE_URL': 'http://************:8001/api/v1/', 'TASK_USAGE': '0', 'DB_REPORTS_PASS': 'stockone^123', 'DB_REPORTS_HOST': 'db1', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'DB_REPLICA_PASS': 'stockone^123', 'AWS_S3_REGION_NAME': 'ap-south-1', 'DEBUG': '1', 'DB_PASS': 'stockone^123', 'ENTRYPOINT_FLAG': 'true', 'CONN_MAX_AGE': '300', 'APPROVAL_ORIGIN_NAME': 'dev-approval-app', '_': '/usr/local/bin/python'})
[2025-05-27 12:58:51,852] INFO _appmap.env: appmap enabled: False
[2025-05-27 12:59:34,521] INFO _appmap.env: appmap enabled: False
[2025-05-27 13:00:55,041] INFO _appmap.configuration: file: /application/appmap.yml
[2025-05-27 13:00:55,042] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-05-27 13:00:55,042] INFO _appmap.configuration: env: environ({'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'PYTHONUNBUFFERED': '1', 'DB_REPLICA_USER': 'stockone', 'HOSTNAME': '02117f210751', 'PYTHON_VERSION': '3.12.10', 'DB_REPORTS_USER': 'stockone', 'DB_REPLICA_HOST': 'db1', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'PWD': '/application', 'USE_S3': 'TRUE', 'DB_USER': 'stockone', 'HOME': '/root', 'LANG': 'C.UTF-8', 'DB_REPLICA_NAME': 'stockone', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'TERM': 'xterm', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_HOST': 'db1', 'AWS_ID': '********************', 'SHLVL': '1', 'DB_REPORTS_NAME': 'stockone', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DB_NAME': 'stockone', 'APPROVAL_SERVICE_URL': 'http://************:8001/api/v1/', 'TASK_USAGE': '0', 'DB_REPORTS_PASS': 'stockone^123', 'DB_REPORTS_HOST': 'db1', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'DB_REPLICA_PASS': 'stockone^123', 'AWS_S3_REGION_NAME': 'ap-south-1', 'DEBUG': '1', 'DB_PASS': 'stockone^123', 'ENTRYPOINT_FLAG': 'true', 'CONN_MAX_AGE': '300', 'APPROVAL_ORIGIN_NAME': 'dev-approval-app', '_': '/usr/local/bin/python'})
[2025-05-27 13:00:55,046] INFO _appmap.env: appmap enabled: False
[2025-05-27 13:05:24,033] INFO _appmap.env: appmap enabled: False
[2025-05-27 13:06:50,994] INFO _appmap.env: appmap enabled: False
[2025-05-27 13:07:06,578] INFO _appmap.env: appmap enabled: False
[2025-05-27 13:07:11,722] INFO _appmap.env: appmap enabled: False
[2025-05-27 13:07:19,092] INFO _appmap.env: appmap enabled: False
[2025-05-27 13:07:26,463] INFO _appmap.env: appmap enabled: False
[2025-05-27 13:07:54,265] INFO _appmap.env: appmap enabled: False
[2025-05-27 13:08:16,974] INFO _appmap.env: appmap enabled: False
[2025-05-27 13:10:46,364] INFO _appmap.env: appmap enabled: False
[2025-05-28 05:05:59,221] INFO _appmap.env: appmap enabled: False
[2025-05-28 05:06:51,456] INFO _appmap.env: appmap enabled: False
[2025-05-28 05:06:54,040] INFO _appmap.env: appmap enabled: False
[2025-05-28 05:07:44,004] INFO _appmap.env: appmap enabled: False
[2025-05-28 05:08:06,883] INFO _appmap.env: appmap enabled: False
[2025-05-28 05:08:08,231] INFO _appmap.env: appmap enabled: False
[2025-05-28 05:08:10,942] INFO _appmap.configuration: file: /application/appmap.yml
[2025-05-28 05:08:10,943] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-05-28 05:08:10,943] INFO _appmap.configuration: env: environ({'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'PYTHONUNBUFFERED': '1', 'DB_REPLICA_USER': 'stockone', 'HOSTNAME': '02117f210751', 'PYTHON_VERSION': '3.12.10', 'DB_REPORTS_USER': 'stockone', 'DB_REPLICA_HOST': 'db1', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'PWD': '/application', 'USE_S3': 'TRUE', 'DB_USER': 'stockone', 'HOME': '/root', 'LANG': 'C.UTF-8', 'DB_REPLICA_NAME': 'stockone', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'TERM': 'xterm', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_HOST': 'db1', 'AWS_ID': '********************', 'SHLVL': '1', 'DB_REPORTS_NAME': 'stockone', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DB_NAME': 'stockone', 'APPROVAL_SERVICE_URL': 'http://************:8001/api/v1/', 'TASK_USAGE': '0', 'DB_REPORTS_PASS': 'stockone^123', 'DB_REPORTS_HOST': 'db1', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'DB_REPLICA_PASS': 'stockone^123', 'AWS_S3_REGION_NAME': 'ap-south-1', 'DEBUG': '1', 'DB_PASS': 'stockone^123', 'ENTRYPOINT_FLAG': 'true', 'CONN_MAX_AGE': '300', 'APPROVAL_ORIGIN_NAME': 'dev-approval-app', '_': '/usr/local/bin/python'})
[2025-05-28 05:08:10,951] INFO _appmap.env: appmap enabled: False
[2025-05-28 05:10:41,943] INFO _appmap.env: appmap enabled: False
[2025-05-28 05:11:00,771] INFO _appmap.env: appmap enabled: False
[2025-05-28 05:12:36,625] INFO _appmap.env: appmap enabled: False
[2025-05-28 05:14:10,400] INFO _appmap.env: appmap enabled: False
[2025-05-28 05:18:27,517] INFO _appmap.env: appmap enabled: False
[2025-05-28 05:25:52,382] INFO _appmap.env: appmap enabled: False
[2025-05-28 05:26:06,740] INFO _appmap.env: appmap enabled: False
[2025-05-28 05:26:20,151] INFO _appmap.env: appmap enabled: False
[2025-05-28 05:26:21,387] INFO _appmap.env: appmap enabled: False
[2025-05-28 05:26:23,481] INFO _appmap.env: appmap enabled: False
[2025-05-28 05:26:25,115] INFO _appmap.env: appmap enabled: False
[2025-05-28 05:26:26,967] INFO _appmap.configuration: file: /application/appmap.yml
[2025-05-28 05:26:26,967] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-05-28 05:26:26,967] INFO _appmap.configuration: env: environ({'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'PYTHONUNBUFFERED': '1', 'DB_REPLICA_USER': 'stockone', 'HOSTNAME': '02117f210751', 'PYTHON_VERSION': '3.12.10', 'DB_REPORTS_USER': 'stockone', 'DB_REPLICA_HOST': 'db1', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'PWD': '/application', 'USE_S3': 'TRUE', 'DB_USER': 'stockone', 'HOME': '/root', 'LANG': 'C.UTF-8', 'DB_REPLICA_NAME': 'stockone', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'TERM': 'xterm', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_HOST': 'db1', 'AWS_ID': '********************', 'SHLVL': '1', 'DB_REPORTS_NAME': 'stockone', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DB_NAME': 'stockone', 'APPROVAL_SERVICE_URL': 'http://************:8001/api/v1/', 'TASK_USAGE': '0', 'DB_REPORTS_PASS': 'stockone^123', 'DB_REPORTS_HOST': 'db1', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'DB_REPLICA_PASS': 'stockone^123', 'AWS_S3_REGION_NAME': 'ap-south-1', 'DEBUG': '1', 'DB_PASS': 'stockone^123', 'ENTRYPOINT_FLAG': 'true', 'CONN_MAX_AGE': '300', 'APPROVAL_ORIGIN_NAME': 'dev-approval-app', '_': '/usr/local/bin/python'})
[2025-05-28 05:26:26,972] INFO _appmap.env: appmap enabled: False
[2025-05-28 05:32:59,885] INFO _appmap.env: appmap enabled: False
[2025-05-28 05:33:00,697] INFO _appmap.env: appmap enabled: False
[2025-05-28 05:33:56,091] INFO _appmap.env: appmap enabled: False
[2025-05-28 05:33:56,904] INFO _appmap.env: appmap enabled: False
[2025-05-28 05:34:08,632] INFO _appmap.env: appmap enabled: False
[2025-05-28 05:34:11,699] INFO _appmap.configuration: file: /application/appmap.yml
[2025-05-28 05:34:11,699] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-05-28 05:34:11,699] INFO _appmap.configuration: env: environ({'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'PYTHONUNBUFFERED': '1', 'DB_REPLICA_USER': 'stockone', 'HOSTNAME': '02117f210751', 'PYTHON_VERSION': '3.12.10', 'DB_REPORTS_USER': 'stockone', 'DB_REPLICA_HOST': 'db1', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'PWD': '/application', 'USE_S3': 'TRUE', 'DB_USER': 'stockone', 'HOME': '/root', 'LANG': 'C.UTF-8', 'DB_REPLICA_NAME': 'stockone', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'TERM': 'xterm', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_HOST': 'db1', 'AWS_ID': '********************', 'SHLVL': '1', 'DB_REPORTS_NAME': 'stockone', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DB_NAME': 'stockone', 'APPROVAL_SERVICE_URL': 'http://************:8001/api/v1/', 'TASK_USAGE': '0', 'DB_REPORTS_PASS': 'stockone^123', 'DB_REPORTS_HOST': 'db1', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'DB_REPLICA_PASS': 'stockone^123', 'AWS_S3_REGION_NAME': 'ap-south-1', 'DEBUG': '1', 'DB_PASS': 'stockone^123', 'ENTRYPOINT_FLAG': 'true', 'CONN_MAX_AGE': '300', 'APPROVAL_ORIGIN_NAME': 'dev-approval-app', '_': '/usr/local/bin/python'})
[2025-05-28 05:34:11,702] INFO _appmap.env: appmap enabled: False
[2025-05-28 05:36:37,905] INFO _appmap.env: appmap enabled: False
[2025-05-28 05:36:42,841] INFO _appmap.env: appmap enabled: False
[2025-05-28 05:37:41,671] INFO _appmap.env: appmap enabled: False
[2025-05-28 05:37:41,976] INFO _appmap.configuration: file: /application/appmap.yml
[2025-05-28 05:37:41,976] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-05-28 05:37:41,977] INFO _appmap.configuration: env: environ({'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'PYTHONUNBUFFERED': '1', 'DB_REPLICA_USER': 'stockone', 'HOSTNAME': '02117f210751', 'PYTHON_VERSION': '3.12.10', 'DB_REPORTS_USER': 'stockone', 'DB_REPLICA_HOST': 'db1', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'PWD': '/application', 'USE_S3': 'TRUE', 'DB_USER': 'stockone', 'HOME': '/root', 'LANG': 'C.UTF-8', 'DB_REPLICA_NAME': 'stockone', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'TERM': 'xterm', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_HOST': 'db1', 'AWS_ID': '********************', 'SHLVL': '1', 'DB_REPORTS_NAME': 'stockone', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DB_NAME': 'stockone', 'APPROVAL_SERVICE_URL': 'http://************:8001/api/v1/', 'TASK_USAGE': '0', 'DB_REPORTS_PASS': 'stockone^123', 'DB_REPORTS_HOST': 'db1', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'DB_REPLICA_PASS': 'stockone^123', 'AWS_S3_REGION_NAME': 'ap-south-1', 'DEBUG': '1', 'DB_PASS': 'stockone^123', 'ENTRYPOINT_FLAG': 'true', 'CONN_MAX_AGE': '300', 'APPROVAL_ORIGIN_NAME': 'dev-approval-app', '_': '/usr/local/bin/python'})
[2025-05-28 05:37:41,984] INFO _appmap.env: appmap enabled: False
[2025-05-28 05:40:34,449] INFO _appmap.env: appmap enabled: False
[2025-05-28 05:41:46,390] INFO _appmap.env: appmap enabled: False
[2025-05-28 06:14:31,853] INFO _appmap.configuration: file: /application/appmap.yml
[2025-05-28 06:14:31,854] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-05-28 06:14:31,854] INFO _appmap.configuration: env: environ({'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'PYTHONUNBUFFERED': '1', 'DB_REPLICA_USER': 'stockone', 'HOSTNAME': '02117f210751', 'PYTHON_VERSION': '3.12.10', 'DB_REPORTS_USER': 'stockone', 'DB_REPLICA_HOST': 'db1', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'PWD': '/application', 'USE_S3': 'TRUE', 'DB_USER': 'stockone', 'HOME': '/root', 'LANG': 'C.UTF-8', 'DB_REPLICA_NAME': 'stockone', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'TERM': 'xterm', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_HOST': 'db1', 'AWS_ID': '********************', 'SHLVL': '1', 'DB_REPORTS_NAME': 'stockone', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DB_NAME': 'stockone', 'APPROVAL_SERVICE_URL': 'http://************:8001/api/v1/', 'TASK_USAGE': '0', 'DB_REPORTS_PASS': 'stockone^123', 'DB_REPORTS_HOST': 'db1', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'DB_REPLICA_PASS': 'stockone^123', 'AWS_S3_REGION_NAME': 'ap-south-1', 'DEBUG': '1', 'DB_PASS': 'stockone^123', 'ENTRYPOINT_FLAG': 'true', 'CONN_MAX_AGE': '300', 'APPROVAL_ORIGIN_NAME': 'dev-approval-app', '_': '/usr/local/bin/python'})
[2025-05-28 06:14:31,862] INFO _appmap.env: appmap enabled: False
[2025-05-28 06:14:59,853] INFO _appmap.env: appmap enabled: False
[2025-05-28 06:15:10,583] INFO _appmap.env: appmap enabled: False
[2025-05-28 06:15:11,156] INFO _appmap.configuration: file: /application/appmap.yml
[2025-05-28 06:15:11,157] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-05-28 06:15:11,157] INFO _appmap.configuration: env: environ({'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'PYTHONUNBUFFERED': '1', 'DB_REPLICA_USER': 'stockone', 'HOSTNAME': '02117f210751', 'PYTHON_VERSION': '3.12.10', 'DB_REPORTS_USER': 'stockone', 'DB_REPLICA_HOST': 'db1', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'PWD': '/application', 'USE_S3': 'TRUE', 'DB_USER': 'stockone', 'HOME': '/root', 'LANG': 'C.UTF-8', 'DB_REPLICA_NAME': 'stockone', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'TERM': 'xterm', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_HOST': 'db1', 'AWS_ID': '********************', 'SHLVL': '1', 'DB_REPORTS_NAME': 'stockone', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DB_NAME': 'stockone', 'APPROVAL_SERVICE_URL': 'http://************:8001/api/v1/', 'TASK_USAGE': '0', 'DB_REPORTS_PASS': 'stockone^123', 'DB_REPORTS_HOST': 'db1', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'DB_REPLICA_PASS': 'stockone^123', 'AWS_S3_REGION_NAME': 'ap-south-1', 'DEBUG': '1', 'DB_PASS': 'stockone^123', 'ENTRYPOINT_FLAG': 'true', 'CONN_MAX_AGE': '300', 'APPROVAL_ORIGIN_NAME': 'dev-approval-app', '_': '/usr/local/bin/python'})
[2025-05-28 06:15:11,162] INFO _appmap.env: appmap enabled: False
[2025-05-28 06:18:10,168] INFO _appmap.env: appmap enabled: False
[2025-05-28 06:18:55,864] INFO _appmap.env: appmap enabled: False
[2025-05-28 06:18:58,963] INFO _appmap.configuration: file: /application/appmap.yml
[2025-05-28 06:18:58,963] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-05-28 06:18:58,963] INFO _appmap.configuration: env: environ({'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'PYTHONUNBUFFERED': '1', 'DB_REPLICA_USER': 'stockone', 'HOSTNAME': '02117f210751', 'PYTHON_VERSION': '3.12.10', 'DB_REPORTS_USER': 'stockone', 'DB_REPLICA_HOST': 'db1', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'PWD': '/application', 'USE_S3': 'TRUE', 'DB_USER': 'stockone', 'HOME': '/root', 'LANG': 'C.UTF-8', 'DB_REPLICA_NAME': 'stockone', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'TERM': 'xterm', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_HOST': 'db1', 'AWS_ID': '********************', 'SHLVL': '1', 'DB_REPORTS_NAME': 'stockone', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DB_NAME': 'stockone', 'APPROVAL_SERVICE_URL': 'http://************:8001/api/v1/', 'TASK_USAGE': '0', 'DB_REPORTS_PASS': 'stockone^123', 'DB_REPORTS_HOST': 'db1', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'DB_REPLICA_PASS': 'stockone^123', 'AWS_S3_REGION_NAME': 'ap-south-1', 'DEBUG': '1', 'DB_PASS': 'stockone^123', 'ENTRYPOINT_FLAG': 'true', 'CONN_MAX_AGE': '300', 'APPROVAL_ORIGIN_NAME': 'dev-approval-app', '_': '/usr/local/bin/python'})
[2025-05-28 06:18:58,969] INFO _appmap.env: appmap enabled: False
[2025-05-28 06:23:24,033] INFO _appmap.env: appmap enabled: False
[2025-05-28 06:23:29,895] INFO _appmap.env: appmap enabled: False
[2025-05-28 06:23:33,408] INFO _appmap.configuration: file: /application/appmap.yml
[2025-05-28 06:23:33,409] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-05-28 06:23:33,409] INFO _appmap.configuration: env: environ({'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'PYTHONUNBUFFERED': '1', 'DB_REPLICA_USER': 'stockone', 'HOSTNAME': '02117f210751', 'PYTHON_VERSION': '3.12.10', 'DB_REPORTS_USER': 'stockone', 'DB_REPLICA_HOST': 'db1', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'PWD': '/application', 'USE_S3': 'TRUE', 'DB_USER': 'stockone', 'HOME': '/root', 'LANG': 'C.UTF-8', 'DB_REPLICA_NAME': 'stockone', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'TERM': 'xterm', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_HOST': 'db1', 'AWS_ID': '********************', 'SHLVL': '1', 'DB_REPORTS_NAME': 'stockone', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DB_NAME': 'stockone', 'APPROVAL_SERVICE_URL': 'http://************:8001/api/v1/', 'TASK_USAGE': '0', 'DB_REPORTS_PASS': 'stockone^123', 'DB_REPORTS_HOST': 'db1', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'DB_REPLICA_PASS': 'stockone^123', 'AWS_S3_REGION_NAME': 'ap-south-1', 'DEBUG': '1', 'DB_PASS': 'stockone^123', 'ENTRYPOINT_FLAG': 'true', 'CONN_MAX_AGE': '300', 'APPROVAL_ORIGIN_NAME': 'dev-approval-app', '_': '/usr/local/bin/python'})
[2025-05-28 06:23:33,415] INFO _appmap.env: appmap enabled: False
[2025-05-28 06:30:04,598] INFO _appmap.env: appmap enabled: False
[2025-05-28 06:30:26,279] INFO _appmap.env: appmap enabled: False
[2025-05-28 06:31:51,812] INFO _appmap.env: appmap enabled: False
[2025-05-28 06:35:10,190] INFO _appmap.env: appmap enabled: False
[2025-05-28 06:35:11,006] INFO _appmap.env: appmap enabled: False
[2025-05-28 06:35:37,148] INFO _appmap.env: appmap enabled: False
[2025-05-28 06:35:39,418] INFO _appmap.configuration: file: /application/appmap.yml
[2025-05-28 06:35:39,418] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-05-28 06:35:39,418] INFO _appmap.configuration: env: environ({'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'PYTHONUNBUFFERED': '1', 'DB_REPLICA_USER': 'stockone', 'HOSTNAME': '02117f210751', 'PYTHON_VERSION': '3.12.10', 'DB_REPORTS_USER': 'stockone', 'DB_REPLICA_HOST': 'db1', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'PWD': '/application', 'USE_S3': 'TRUE', 'DB_USER': 'stockone', 'HOME': '/root', 'LANG': 'C.UTF-8', 'DB_REPLICA_NAME': 'stockone', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'TERM': 'xterm', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_HOST': 'db1', 'AWS_ID': '********************', 'SHLVL': '1', 'DB_REPORTS_NAME': 'stockone', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DB_NAME': 'stockone', 'APPROVAL_SERVICE_URL': 'http://************:8001/api/v1/', 'TASK_USAGE': '0', 'DB_REPORTS_PASS': 'stockone^123', 'DB_REPORTS_HOST': 'db1', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'DB_REPLICA_PASS': 'stockone^123', 'AWS_S3_REGION_NAME': 'ap-south-1', 'DEBUG': '1', 'DB_PASS': 'stockone^123', 'ENTRYPOINT_FLAG': 'true', 'CONN_MAX_AGE': '300', 'APPROVAL_ORIGIN_NAME': 'dev-approval-app', '_': '/usr/local/bin/python'})
[2025-05-28 06:35:39,423] INFO _appmap.env: appmap enabled: False
[2025-05-28 06:41:05,384] INFO _appmap.env: appmap enabled: False
[2025-05-28 06:42:44,129] INFO _appmap.env: appmap enabled: False
[2025-05-28 06:42:49,272] INFO _appmap.env: appmap enabled: False
[2025-05-28 06:43:23,448] INFO _appmap.env: appmap enabled: False
[2025-05-28 06:43:35,006] INFO _appmap.env: appmap enabled: False
[2025-05-28 06:43:44,255] INFO _appmap.env: appmap enabled: False
[2025-05-28 06:45:14,862] INFO _appmap.env: appmap enabled: False
[2025-05-28 06:45:26,074] INFO _appmap.env: appmap enabled: False
[2025-05-28 06:47:40,670] INFO _appmap.env: appmap enabled: False
[2025-05-28 06:47:53,207] INFO _appmap.env: appmap enabled: False
[2025-05-28 06:47:54,544] INFO _appmap.env: appmap enabled: False
[2025-05-28 06:47:59,037] INFO _appmap.env: appmap enabled: False
[2025-05-28 06:51:37,603] INFO _appmap.env: appmap enabled: False
[2025-05-28 06:51:43,884] INFO _appmap.env: appmap enabled: False
[2025-05-28 06:51:49,895] INFO _appmap.env: appmap enabled: False
[2025-05-28 06:52:26,443] INFO _appmap.env: appmap enabled: False
[2025-05-28 06:52:55,018] INFO _appmap.env: appmap enabled: False
[2025-05-28 06:54:11,835] INFO _appmap.env: appmap enabled: False
[2025-05-28 06:54:42,895] INFO _appmap.env: appmap enabled: False
[2025-05-28 06:54:44,561] INFO _appmap.env: appmap enabled: False
[2025-05-28 06:56:02,536] INFO _appmap.env: appmap enabled: False
[2025-05-28 06:59:01,751] INFO _appmap.env: appmap enabled: False
[2025-05-28 06:59:12,035] INFO _appmap.env: appmap enabled: False
[2025-05-28 07:02:16,380] INFO _appmap.env: appmap enabled: False
[2025-05-28 07:02:30,398] INFO _appmap.env: appmap enabled: False
[2025-05-28 07:02:34,696] INFO _appmap.env: appmap enabled: False
[2025-05-28 07:03:55,940] INFO _appmap.env: appmap enabled: False
[2025-05-28 07:04:05,051] INFO _appmap.env: appmap enabled: False
[2025-05-28 07:04:34,058] INFO _appmap.env: appmap enabled: False
[2025-05-28 07:04:43,365] INFO _appmap.env: appmap enabled: False
[2025-05-28 07:04:50,266] INFO _appmap.env: appmap enabled: False
[2025-05-28 07:04:54,242] INFO _appmap.env: appmap enabled: False
[2025-05-28 07:05:13,178] INFO _appmap.env: appmap enabled: False
[2025-05-28 07:05:43,762] INFO _appmap.env: appmap enabled: False
[2025-05-28 07:05:46,283] INFO _appmap.env: appmap enabled: False
[2025-05-28 07:06:01,182] INFO _appmap.env: appmap enabled: False
[2025-05-28 07:06:09,106] INFO _appmap.env: appmap enabled: False
[2025-05-28 07:06:10,762] INFO _appmap.env: appmap enabled: False
[2025-05-28 07:08:01,771] INFO _appmap.env: appmap enabled: False
[2025-05-28 07:08:03,405] INFO _appmap.env: appmap enabled: False
[2025-05-28 07:08:07,318] INFO _appmap.env: appmap enabled: False
[2025-05-28 07:08:51,057] INFO _appmap.env: appmap enabled: False
[2025-05-28 07:09:01,012] INFO _appmap.env: appmap enabled: False
[2025-05-28 07:09:52,334] INFO _appmap.env: appmap enabled: False
[2025-05-28 07:10:50,336] INFO _appmap.env: appmap enabled: False
[2025-05-28 07:11:00,610] INFO _appmap.env: appmap enabled: False
[2025-05-28 07:11:30,535] INFO _appmap.env: appmap enabled: False
[2025-05-28 07:11:32,862] INFO _appmap.env: appmap enabled: False
[2025-05-28 07:12:45,077] INFO _appmap.env: appmap enabled: False
[2025-05-28 07:13:45,575] INFO _appmap.env: appmap enabled: False
[2025-05-28 07:14:16,818] INFO _appmap.env: appmap enabled: False
[2025-05-28 07:14:23,816] INFO _appmap.env: appmap enabled: False
[2025-05-28 07:14:24,696] INFO _appmap.configuration: file: /application/appmap.yml
[2025-05-28 07:14:24,696] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-05-28 07:14:24,696] INFO _appmap.configuration: env: environ({'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'PYTHONUNBUFFERED': '1', 'DB_REPLICA_USER': 'stockone', 'HOSTNAME': '02117f210751', 'PYTHON_VERSION': '3.12.10', 'DB_REPORTS_USER': 'stockone', 'DB_REPLICA_HOST': 'db1', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'PWD': '/application', 'USE_S3': 'TRUE', 'DB_USER': 'stockone', 'HOME': '/root', 'LANG': 'C.UTF-8', 'DB_REPLICA_NAME': 'stockone', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'TERM': 'xterm', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_HOST': 'db1', 'AWS_ID': '********************', 'SHLVL': '1', 'DB_REPORTS_NAME': 'stockone', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DB_NAME': 'stockone', 'APPROVAL_SERVICE_URL': 'http://************:8001/api/v1/', 'TASK_USAGE': '0', 'DB_REPORTS_PASS': 'stockone^123', 'DB_REPORTS_HOST': 'db1', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'DB_REPLICA_PASS': 'stockone^123', 'AWS_S3_REGION_NAME': 'ap-south-1', 'DEBUG': '1', 'DB_PASS': 'stockone^123', 'ENTRYPOINT_FLAG': 'true', 'CONN_MAX_AGE': '300', 'APPROVAL_ORIGIN_NAME': 'dev-approval-app', '_': '/usr/local/bin/python'})
[2025-05-28 07:14:24,703] INFO _appmap.env: appmap enabled: False
[2025-05-28 07:14:52,900] INFO _appmap.env: appmap enabled: False
[2025-05-28 07:14:54,167] INFO _appmap.env: appmap enabled: False
[2025-05-28 07:15:10,873] INFO _appmap.env: appmap enabled: False
[2025-05-28 07:15:11,392] INFO _appmap.configuration: file: /application/appmap.yml
[2025-05-28 07:15:11,392] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-05-28 07:15:11,393] INFO _appmap.configuration: env: environ({'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'PYTHONUNBUFFERED': '1', 'DB_REPLICA_USER': 'stockone', 'HOSTNAME': '02117f210751', 'PYTHON_VERSION': '3.12.10', 'DB_REPORTS_USER': 'stockone', 'DB_REPLICA_HOST': 'db1', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'PWD': '/application', 'USE_S3': 'TRUE', 'DB_USER': 'stockone', 'HOME': '/root', 'LANG': 'C.UTF-8', 'DB_REPLICA_NAME': 'stockone', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'TERM': 'xterm', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_HOST': 'db1', 'AWS_ID': '********************', 'SHLVL': '1', 'DB_REPORTS_NAME': 'stockone', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DB_NAME': 'stockone', 'APPROVAL_SERVICE_URL': 'http://************:8001/api/v1/', 'TASK_USAGE': '0', 'DB_REPORTS_PASS': 'stockone^123', 'DB_REPORTS_HOST': 'db1', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'DB_REPLICA_PASS': 'stockone^123', 'AWS_S3_REGION_NAME': 'ap-south-1', 'DEBUG': '1', 'DB_PASS': 'stockone^123', 'ENTRYPOINT_FLAG': 'true', 'CONN_MAX_AGE': '300', 'APPROVAL_ORIGIN_NAME': 'dev-approval-app', '_': '/usr/local/bin/python'})
[2025-05-28 07:15:11,399] INFO _appmap.env: appmap enabled: False
[2025-05-28 07:15:31,447] INFO _appmap.env: appmap enabled: False
[2025-05-28 07:15:31,899] INFO _appmap.configuration: file: /application/appmap.yml
[2025-05-28 07:15:31,900] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-05-28 07:15:31,900] INFO _appmap.configuration: env: environ({'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'PYTHONUNBUFFERED': '1', 'DB_REPLICA_USER': 'stockone', 'HOSTNAME': '02117f210751', 'PYTHON_VERSION': '3.12.10', 'DB_REPORTS_USER': 'stockone', 'DB_REPLICA_HOST': 'db1', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'PWD': '/application', 'USE_S3': 'TRUE', 'DB_USER': 'stockone', 'HOME': '/root', 'LANG': 'C.UTF-8', 'DB_REPLICA_NAME': 'stockone', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'TERM': 'xterm', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_HOST': 'db1', 'AWS_ID': '********************', 'SHLVL': '1', 'DB_REPORTS_NAME': 'stockone', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DB_NAME': 'stockone', 'APPROVAL_SERVICE_URL': 'http://************:8001/api/v1/', 'TASK_USAGE': '0', 'DB_REPORTS_PASS': 'stockone^123', 'DB_REPORTS_HOST': 'db1', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'DB_REPLICA_PASS': 'stockone^123', 'AWS_S3_REGION_NAME': 'ap-south-1', 'DEBUG': '1', 'DB_PASS': 'stockone^123', 'ENTRYPOINT_FLAG': 'true', 'CONN_MAX_AGE': '300', 'APPROVAL_ORIGIN_NAME': 'dev-approval-app', '_': '/usr/local/bin/python'})
[2025-05-28 07:15:31,903] INFO _appmap.env: appmap enabled: False
[2025-05-28 07:17:21,302] INFO _appmap.env: appmap enabled: False
[2025-05-28 07:18:05,285] INFO _appmap.env: appmap enabled: False
[2025-05-28 07:18:18,270] INFO _appmap.configuration: file: /application/appmap.yml
[2025-05-28 07:18:18,270] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-05-28 07:18:18,270] INFO _appmap.configuration: env: environ({'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'PYTHONUNBUFFERED': '1', 'DB_REPLICA_USER': 'stockone', 'HOSTNAME': '02117f210751', 'PYTHON_VERSION': '3.12.10', 'DB_REPORTS_USER': 'stockone', 'DB_REPLICA_HOST': 'db1', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'PWD': '/application', 'USE_S3': 'TRUE', 'DB_USER': 'stockone', 'HOME': '/root', 'LANG': 'C.UTF-8', 'DB_REPLICA_NAME': 'stockone', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'TERM': 'xterm', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_HOST': 'db1', 'AWS_ID': '********************', 'SHLVL': '1', 'DB_REPORTS_NAME': 'stockone', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DB_NAME': 'stockone', 'APPROVAL_SERVICE_URL': 'http://************:8001/api/v1/', 'TASK_USAGE': '0', 'DB_REPORTS_PASS': 'stockone^123', 'DB_REPORTS_HOST': 'db1', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'DB_REPLICA_PASS': 'stockone^123', 'AWS_S3_REGION_NAME': 'ap-south-1', 'DEBUG': '1', 'DB_PASS': 'stockone^123', 'ENTRYPOINT_FLAG': 'true', 'CONN_MAX_AGE': '300', 'APPROVAL_ORIGIN_NAME': 'dev-approval-app', '_': '/usr/local/bin/python'})
[2025-05-28 07:18:18,274] INFO _appmap.env: appmap enabled: False
[2025-05-28 07:19:52,260] INFO _appmap.env: appmap enabled: False
[2025-05-28 07:20:19,423] INFO _appmap.env: appmap enabled: False
[2025-05-28 07:20:30,229] INFO _appmap.env: appmap enabled: False
[2025-05-28 07:20:30,985] INFO _appmap.configuration: file: /application/appmap.yml
[2025-05-28 07:20:30,985] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-05-28 07:20:30,985] INFO _appmap.configuration: env: environ({'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'PYTHONUNBUFFERED': '1', 'DB_REPLICA_USER': 'stockone', 'HOSTNAME': '02117f210751', 'PYTHON_VERSION': '3.12.10', 'DB_REPORTS_USER': 'stockone', 'DB_REPLICA_HOST': 'db1', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'PWD': '/application', 'USE_S3': 'TRUE', 'DB_USER': 'stockone', 'HOME': '/root', 'LANG': 'C.UTF-8', 'DB_REPLICA_NAME': 'stockone', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'TERM': 'xterm', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_HOST': 'db1', 'AWS_ID': '********************', 'SHLVL': '1', 'DB_REPORTS_NAME': 'stockone', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DB_NAME': 'stockone', 'APPROVAL_SERVICE_URL': 'http://************:8001/api/v1/', 'TASK_USAGE': '0', 'DB_REPORTS_PASS': 'stockone^123', 'DB_REPORTS_HOST': 'db1', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'DB_REPLICA_PASS': 'stockone^123', 'AWS_S3_REGION_NAME': 'ap-south-1', 'DEBUG': '1', 'DB_PASS': 'stockone^123', 'ENTRYPOINT_FLAG': 'true', 'CONN_MAX_AGE': '300', 'APPROVAL_ORIGIN_NAME': 'dev-approval-app', '_': '/usr/local/bin/python'})
[2025-05-28 07:20:30,990] INFO _appmap.env: appmap enabled: False
[2025-05-28 07:20:40,563] INFO _appmap.configuration: file: /application/appmap.yml
[2025-05-28 07:20:40,563] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-05-28 07:20:40,563] INFO _appmap.configuration: env: environ({'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'PYTHONUNBUFFERED': '1', 'DB_REPLICA_USER': 'stockone', 'HOSTNAME': '02117f210751', 'PYTHON_VERSION': '3.12.10', 'DB_REPORTS_USER': 'stockone', 'DB_REPLICA_HOST': 'db1', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'PWD': '/application', 'USE_S3': 'TRUE', 'DB_USER': 'stockone', 'HOME': '/root', 'LANG': 'C.UTF-8', 'DB_REPLICA_NAME': 'stockone', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'TERM': 'xterm', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_HOST': 'db1', 'AWS_ID': '********************', 'SHLVL': '1', 'DB_REPORTS_NAME': 'stockone', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DB_NAME': 'stockone', 'APPROVAL_SERVICE_URL': 'http://************:8001/api/v1/', 'TASK_USAGE': '0', 'DB_REPORTS_PASS': 'stockone^123', 'DB_REPORTS_HOST': 'db1', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'DB_REPLICA_PASS': 'stockone^123', 'AWS_S3_REGION_NAME': 'ap-south-1', 'DEBUG': '1', 'DB_PASS': 'stockone^123', 'ENTRYPOINT_FLAG': 'true', 'CONN_MAX_AGE': '300', 'APPROVAL_ORIGIN_NAME': 'dev-approval-app', '_': '/usr/local/bin/python'})
[2025-05-28 07:20:40,567] INFO _appmap.env: appmap enabled: False
[2025-05-28 07:21:02,989] INFO _appmap.env: appmap enabled: False
[2025-05-28 07:21:06,381] INFO _appmap.env: appmap enabled: False
[2025-05-28 07:21:07,925] INFO _appmap.configuration: file: /application/appmap.yml
[2025-05-28 07:21:07,926] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-05-28 07:21:07,927] INFO _appmap.configuration: env: environ({'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'PYTHONUNBUFFERED': '1', 'DB_REPLICA_USER': 'stockone', 'HOSTNAME': '02117f210751', 'PYTHON_VERSION': '3.12.10', 'DB_REPORTS_USER': 'stockone', 'DB_REPLICA_HOST': 'db1', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'PWD': '/application', 'USE_S3': 'TRUE', 'DB_USER': 'stockone', 'HOME': '/root', 'LANG': 'C.UTF-8', 'DB_REPLICA_NAME': 'stockone', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'TERM': 'xterm', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_HOST': 'db1', 'AWS_ID': '********************', 'SHLVL': '1', 'DB_REPORTS_NAME': 'stockone', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DB_NAME': 'stockone', 'APPROVAL_SERVICE_URL': 'http://************:8001/api/v1/', 'TASK_USAGE': '0', 'DB_REPORTS_PASS': 'stockone^123', 'DB_REPORTS_HOST': 'db1', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'DB_REPLICA_PASS': 'stockone^123', 'AWS_S3_REGION_NAME': 'ap-south-1', 'DEBUG': '1', 'DB_PASS': 'stockone^123', 'ENTRYPOINT_FLAG': 'true', 'CONN_MAX_AGE': '300', 'APPROVAL_ORIGIN_NAME': 'dev-approval-app', '_': '/usr/local/bin/python'})
[2025-05-28 07:21:07,934] INFO _appmap.env: appmap enabled: False
[2025-05-28 07:22:11,574] INFO _appmap.env: appmap enabled: False
[2025-05-28 07:22:28,492] INFO _appmap.env: appmap enabled: False
[2025-05-28 08:19:52,378] INFO _appmap.env: appmap enabled: False
[2025-05-28 08:20:08,429] INFO _appmap.env: appmap enabled: False
[2025-05-28 08:20:11,695] INFO _appmap.env: appmap enabled: False
[2025-05-28 08:20:16,166] INFO _appmap.env: appmap enabled: False
[2025-05-28 08:20:21,596] INFO _appmap.env: appmap enabled: False
[2025-05-28 08:21:26,197] INFO _appmap.env: appmap enabled: False
[2025-05-28 08:24:22,936] INFO _appmap.env: appmap enabled: False
[2025-05-28 08:24:55,079] INFO _appmap.env: appmap enabled: False
[2025-05-28 08:24:56,328] INFO _appmap.env: appmap enabled: False
[2025-05-28 08:27:40,117] INFO _appmap.env: appmap enabled: False
[2025-05-28 08:28:08,116] INFO _appmap.env: appmap enabled: False
[2025-05-28 08:28:16,332] INFO _appmap.env: appmap enabled: False
[2025-05-28 08:28:32,391] INFO _appmap.env: appmap enabled: False
[2025-05-28 08:28:49,049] INFO _appmap.env: appmap enabled: False
[2025-05-28 08:29:02,478] INFO _appmap.env: appmap enabled: False
[2025-05-28 08:29:03,405] INFO _appmap.env: appmap enabled: False
[2025-05-28 08:30:59,078] INFO _appmap.env: appmap enabled: False
[2025-05-28 08:31:11,555] INFO _appmap.env: appmap enabled: False
[2025-05-28 08:31:24,450] INFO _appmap.env: appmap enabled: False
[2025-05-28 09:07:10,792] INFO _appmap.env: appmap enabled: False
[2025-05-28 09:07:15,302] INFO _appmap.env: appmap enabled: False
[2025-05-28 09:38:16,062] INFO _appmap.env: appmap enabled: False
[2025-05-28 09:38:56,018] INFO _appmap.env: appmap enabled: False
[2025-05-28 09:39:20,264] INFO _appmap.env: appmap enabled: False
[2025-05-28 09:39:25,808] INFO _appmap.env: appmap enabled: False
[2025-05-28 09:39:32,659] INFO _appmap.env: appmap enabled: False
[2025-05-28 09:39:41,698] INFO _appmap.env: appmap enabled: False
[2025-05-28 09:39:47,796] INFO _appmap.env: appmap enabled: False
[2025-05-28 09:39:48,933] INFO _appmap.configuration: file: /application/appmap.yml
[2025-05-28 09:39:48,933] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-05-28 09:39:48,934] INFO _appmap.configuration: env: environ({'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'PYTHONUNBUFFERED': '1', 'DB_REPLICA_USER': 'stockone', 'HOSTNAME': '02117f210751', 'PYTHON_VERSION': '3.12.10', 'DB_REPORTS_USER': 'stockone', 'DB_REPLICA_HOST': 'db1', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'PWD': '/application', 'USE_S3': 'TRUE', 'DB_USER': 'stockone', 'HOME': '/root', 'LANG': 'C.UTF-8', 'DB_REPLICA_NAME': 'stockone', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'TERM': 'xterm', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_HOST': 'db1', 'AWS_ID': '********************', 'SHLVL': '1', 'DB_REPORTS_NAME': 'stockone', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DB_NAME': 'stockone', 'APPROVAL_SERVICE_URL': 'http://************:8001/api/v1/', 'TASK_USAGE': '0', 'DB_REPORTS_PASS': 'stockone^123', 'DB_REPORTS_HOST': 'db1', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'DB_REPLICA_PASS': 'stockone^123', 'AWS_S3_REGION_NAME': 'ap-south-1', 'DEBUG': '1', 'DB_PASS': 'stockone^123', 'ENTRYPOINT_FLAG': 'true', 'CONN_MAX_AGE': '300', 'APPROVAL_ORIGIN_NAME': 'dev-approval-app', '_': '/usr/local/bin/python'})
[2025-05-28 09:39:48,938] INFO _appmap.env: appmap enabled: False
[2025-05-28 09:42:01,099] INFO _appmap.env: appmap enabled: False
[2025-05-28 09:44:05,354] INFO _appmap.env: appmap enabled: False
[2025-05-28 09:44:53,640] INFO _appmap.env: appmap enabled: False
[2025-05-28 09:47:29,343] INFO _appmap.env: appmap enabled: False
[2025-05-28 09:47:31,202] INFO _appmap.env: appmap enabled: False
[2025-05-28 09:49:09,643] INFO _appmap.env: appmap enabled: False
[2025-05-28 09:49:11,686] INFO _appmap.env: appmap enabled: False
[2025-05-28 09:49:25,710] INFO _appmap.env: appmap enabled: False
[2025-05-28 09:49:29,541] INFO _appmap.env: appmap enabled: False
[2025-05-28 09:49:41,071] INFO _appmap.env: appmap enabled: False
[2025-05-28 09:50:10,847] INFO _appmap.env: appmap enabled: False
[2025-05-28 09:50:58,703] INFO _appmap.env: appmap enabled: False
[2025-05-28 09:51:43,250] INFO _appmap.env: appmap enabled: False
[2025-05-28 09:52:18,836] INFO _appmap.env: appmap enabled: False
[2025-05-28 09:52:26,538] INFO _appmap.env: appmap enabled: False
[2025-05-28 09:52:41,075] INFO _appmap.env: appmap enabled: False
[2025-05-28 09:52:48,223] INFO _appmap.env: appmap enabled: False
[2025-05-28 09:52:53,798] INFO _appmap.env: appmap enabled: False
[2025-05-28 09:53:00,181] INFO _appmap.env: appmap enabled: False
[2025-05-28 09:53:02,936] INFO _appmap.env: appmap enabled: False
[2025-05-28 09:53:09,968] INFO _appmap.env: appmap enabled: False
[2025-05-28 09:53:10,464] INFO _appmap.configuration: file: /application/appmap.yml
[2025-05-28 09:53:10,464] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-05-28 09:53:10,464] INFO _appmap.configuration: env: environ({'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'PYTHONUNBUFFERED': '1', 'DB_REPLICA_USER': 'stockone', 'HOSTNAME': '02117f210751', 'PYTHON_VERSION': '3.12.10', 'DB_REPORTS_USER': 'stockone', 'DB_REPLICA_HOST': 'db1', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'PWD': '/application', 'USE_S3': 'TRUE', 'DB_USER': 'stockone', 'HOME': '/root', 'LANG': 'C.UTF-8', 'DB_REPLICA_NAME': 'stockone', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'TERM': 'xterm', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_HOST': 'db1', 'AWS_ID': '********************', 'SHLVL': '1', 'DB_REPORTS_NAME': 'stockone', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DB_NAME': 'stockone', 'APPROVAL_SERVICE_URL': 'http://************:8001/api/v1/', 'TASK_USAGE': '0', 'DB_REPORTS_PASS': 'stockone^123', 'DB_REPORTS_HOST': 'db1', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'DB_REPLICA_PASS': 'stockone^123', 'AWS_S3_REGION_NAME': 'ap-south-1', 'DEBUG': '1', 'DB_PASS': 'stockone^123', 'ENTRYPOINT_FLAG': 'true', 'CONN_MAX_AGE': '300', 'APPROVAL_ORIGIN_NAME': 'dev-approval-app', '_': '/usr/local/bin/python'})
[2025-05-28 09:53:10,468] INFO _appmap.env: appmap enabled: False
[2025-05-28 09:53:21,867] INFO _appmap.configuration: file: /application/appmap.yml
[2025-05-28 09:53:21,868] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-05-28 09:53:21,868] INFO _appmap.configuration: env: environ({'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'PYTHONUNBUFFERED': '1', 'DB_REPLICA_USER': 'stockone', 'HOSTNAME': '02117f210751', 'PYTHON_VERSION': '3.12.10', 'DB_REPORTS_USER': 'stockone', 'DB_REPLICA_HOST': 'db1', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'PWD': '/application', 'USE_S3': 'TRUE', 'DB_USER': 'stockone', 'HOME': '/root', 'LANG': 'C.UTF-8', 'DB_REPLICA_NAME': 'stockone', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'TERM': 'xterm', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_HOST': 'db1', 'AWS_ID': '********************', 'SHLVL': '1', 'DB_REPORTS_NAME': 'stockone', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DB_NAME': 'stockone', 'APPROVAL_SERVICE_URL': 'http://************:8001/api/v1/', 'TASK_USAGE': '0', 'DB_REPORTS_PASS': 'stockone^123', 'DB_REPORTS_HOST': 'db1', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'DB_REPLICA_PASS': 'stockone^123', 'AWS_S3_REGION_NAME': 'ap-south-1', 'DEBUG': '1', 'DB_PASS': 'stockone^123', 'ENTRYPOINT_FLAG': 'true', 'CONN_MAX_AGE': '300', 'APPROVAL_ORIGIN_NAME': 'dev-approval-app', '_': '/usr/local/bin/python'})
[2025-05-28 09:53:21,873] INFO _appmap.env: appmap enabled: False
[2025-05-28 09:53:52,507] INFO _appmap.env: appmap enabled: False
[2025-05-28 09:54:00,333] INFO _appmap.env: appmap enabled: False
[2025-05-28 09:54:51,815] INFO _appmap.env: appmap enabled: False
[2025-05-28 09:54:52,385] INFO _appmap.configuration: file: /application/appmap.yml
[2025-05-28 09:54:52,385] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-05-28 09:54:52,385] INFO _appmap.configuration: env: environ({'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'PYTHONUNBUFFERED': '1', 'DB_REPLICA_USER': 'stockone', 'HOSTNAME': '02117f210751', 'PYTHON_VERSION': '3.12.10', 'DB_REPORTS_USER': 'stockone', 'DB_REPLICA_HOST': 'db1', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'PWD': '/application', 'USE_S3': 'TRUE', 'DB_USER': 'stockone', 'HOME': '/root', 'LANG': 'C.UTF-8', 'DB_REPLICA_NAME': 'stockone', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'TERM': 'xterm', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_HOST': 'db1', 'AWS_ID': '********************', 'SHLVL': '1', 'DB_REPORTS_NAME': 'stockone', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DB_NAME': 'stockone', 'APPROVAL_SERVICE_URL': 'http://************:8001/api/v1/', 'TASK_USAGE': '0', 'DB_REPORTS_PASS': 'stockone^123', 'DB_REPORTS_HOST': 'db1', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'DB_REPLICA_PASS': 'stockone^123', 'AWS_S3_REGION_NAME': 'ap-south-1', 'DEBUG': '1', 'DB_PASS': 'stockone^123', 'ENTRYPOINT_FLAG': 'true', 'CONN_MAX_AGE': '300', 'APPROVAL_ORIGIN_NAME': 'dev-approval-app', '_': '/usr/local/bin/python'})
[2025-05-28 09:54:52,388] INFO _appmap.env: appmap enabled: False
[2025-05-28 09:57:03,219] INFO _appmap.env: appmap enabled: False
[2025-05-28 09:57:17,361] INFO _appmap.configuration: file: /application/appmap.yml
[2025-05-28 09:57:17,361] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-05-28 09:57:17,361] INFO _appmap.configuration: env: environ({'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'PYTHONUNBUFFERED': '1', 'DB_REPLICA_USER': 'stockone', 'HOSTNAME': '02117f210751', 'PYTHON_VERSION': '3.12.10', 'DB_REPORTS_USER': 'stockone', 'DB_REPLICA_HOST': 'db1', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'PWD': '/application', 'USE_S3': 'TRUE', 'DB_USER': 'stockone', 'HOME': '/root', 'LANG': 'C.UTF-8', 'DB_REPLICA_NAME': 'stockone', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'TERM': 'xterm', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_HOST': 'db1', 'AWS_ID': '********************', 'SHLVL': '1', 'DB_REPORTS_NAME': 'stockone', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DB_NAME': 'stockone', 'APPROVAL_SERVICE_URL': 'http://************:8001/api/v1/', 'TASK_USAGE': '0', 'DB_REPORTS_PASS': 'stockone^123', 'DB_REPORTS_HOST': 'db1', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'DB_REPLICA_PASS': 'stockone^123', 'AWS_S3_REGION_NAME': 'ap-south-1', 'DEBUG': '1', 'DB_PASS': 'stockone^123', 'ENTRYPOINT_FLAG': 'true', 'CONN_MAX_AGE': '300', 'APPROVAL_ORIGIN_NAME': 'dev-approval-app', '_': '/usr/local/bin/python'})
[2025-05-28 09:57:17,365] INFO _appmap.env: appmap enabled: False
[2025-05-28 09:57:52,658] INFO _appmap.env: appmap enabled: False
[2025-05-28 09:57:57,109] INFO _appmap.env: appmap enabled: False
[2025-05-28 09:58:23,381] INFO _appmap.env: appmap enabled: False
[2025-05-28 09:58:36,444] INFO _appmap.env: appmap enabled: False
[2025-05-28 09:59:03,512] INFO _appmap.env: appmap enabled: False
[2025-05-28 09:59:04,619] INFO _appmap.configuration: file: /application/appmap.yml
[2025-05-28 09:59:04,619] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-05-28 09:59:04,619] INFO _appmap.configuration: env: environ({'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'PYTHONUNBUFFERED': '1', 'DB_REPLICA_USER': 'stockone', 'HOSTNAME': '02117f210751', 'PYTHON_VERSION': '3.12.10', 'DB_REPORTS_USER': 'stockone', 'DB_REPLICA_HOST': 'db1', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'PWD': '/application', 'USE_S3': 'TRUE', 'DB_USER': 'stockone', 'HOME': '/root', 'LANG': 'C.UTF-8', 'DB_REPLICA_NAME': 'stockone', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'TERM': 'xterm', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_HOST': 'db1', 'AWS_ID': '********************', 'SHLVL': '1', 'DB_REPORTS_NAME': 'stockone', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DB_NAME': 'stockone', 'APPROVAL_SERVICE_URL': 'http://************:8001/api/v1/', 'TASK_USAGE': '0', 'DB_REPORTS_PASS': 'stockone^123', 'DB_REPORTS_HOST': 'db1', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'DB_REPLICA_PASS': 'stockone^123', 'AWS_S3_REGION_NAME': 'ap-south-1', 'DEBUG': '1', 'DB_PASS': 'stockone^123', 'ENTRYPOINT_FLAG': 'true', 'CONN_MAX_AGE': '300', 'APPROVAL_ORIGIN_NAME': 'dev-approval-app', '_': '/usr/local/bin/python'})
[2025-05-28 09:59:04,623] INFO _appmap.env: appmap enabled: False
[2025-05-28 10:00:44,214] INFO _appmap.configuration: file: /application/appmap.yml
[2025-05-28 10:00:44,214] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-05-28 10:00:44,214] INFO _appmap.configuration: env: environ({'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'PYTHONUNBUFFERED': '1', 'DB_REPLICA_USER': 'stockone', 'HOSTNAME': '02117f210751', 'PYTHON_VERSION': '3.12.10', 'DB_REPORTS_USER': 'stockone', 'DB_REPLICA_HOST': 'db1', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'PWD': '/application', 'USE_S3': 'TRUE', 'DB_USER': 'stockone', 'HOME': '/root', 'LANG': 'C.UTF-8', 'DB_REPLICA_NAME': 'stockone', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'TERM': 'xterm', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_HOST': 'db1', 'AWS_ID': '********************', 'SHLVL': '1', 'DB_REPORTS_NAME': 'stockone', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DB_NAME': 'stockone', 'APPROVAL_SERVICE_URL': 'http://************:8001/api/v1/', 'TASK_USAGE': '0', 'DB_REPORTS_PASS': 'stockone^123', 'DB_REPORTS_HOST': 'db1', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'DB_REPLICA_PASS': 'stockone^123', 'AWS_S3_REGION_NAME': 'ap-south-1', 'DEBUG': '1', 'DB_PASS': 'stockone^123', 'ENTRYPOINT_FLAG': 'true', 'CONN_MAX_AGE': '300', 'APPROVAL_ORIGIN_NAME': 'dev-approval-app', '_': '/usr/local/bin/python'})
[2025-05-28 10:00:44,218] INFO _appmap.env: appmap enabled: False
[2025-05-28 10:01:20,685] INFO _appmap.env: appmap enabled: False
[2025-05-28 10:01:28,908] INFO _appmap.env: appmap enabled: False
[2025-05-28 10:01:33,069] INFO _appmap.env: appmap enabled: False
[2025-05-28 10:01:34,253] INFO _appmap.configuration: file: /application/appmap.yml
[2025-05-28 10:01:34,254] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-05-28 10:01:34,255] INFO _appmap.configuration: env: environ({'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'PYTHONUNBUFFERED': '1', 'DB_REPLICA_USER': 'stockone', 'HOSTNAME': '02117f210751', 'PYTHON_VERSION': '3.12.10', 'DB_REPORTS_USER': 'stockone', 'DB_REPLICA_HOST': 'db1', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'PWD': '/application', 'USE_S3': 'TRUE', 'DB_USER': 'stockone', 'HOME': '/root', 'LANG': 'C.UTF-8', 'DB_REPLICA_NAME': 'stockone', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'TERM': 'xterm', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_HOST': 'db1', 'AWS_ID': '********************', 'SHLVL': '1', 'DB_REPORTS_NAME': 'stockone', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DB_NAME': 'stockone', 'APPROVAL_SERVICE_URL': 'http://************:8001/api/v1/', 'TASK_USAGE': '0', 'DB_REPORTS_PASS': 'stockone^123', 'DB_REPORTS_HOST': 'db1', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'DB_REPLICA_PASS': 'stockone^123', 'AWS_S3_REGION_NAME': 'ap-south-1', 'DEBUG': '1', 'DB_PASS': 'stockone^123', 'ENTRYPOINT_FLAG': 'true', 'CONN_MAX_AGE': '300', 'APPROVAL_ORIGIN_NAME': 'dev-approval-app', '_': '/usr/local/bin/python'})
[2025-05-28 10:01:34,259] INFO _appmap.env: appmap enabled: False
[2025-05-28 10:02:34,261] INFO _appmap.env: appmap enabled: False
[2025-05-28 10:02:46,522] INFO _appmap.env: appmap enabled: False
[2025-05-28 10:02:48,242] INFO _appmap.configuration: file: /application/appmap.yml
[2025-05-28 10:02:48,242] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-05-28 10:02:48,242] INFO _appmap.configuration: env: environ({'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'PYTHONUNBUFFERED': '1', 'DB_REPLICA_USER': 'stockone', 'HOSTNAME': '02117f210751', 'PYTHON_VERSION': '3.12.10', 'DB_REPORTS_USER': 'stockone', 'DB_REPLICA_HOST': 'db1', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'PWD': '/application', 'USE_S3': 'TRUE', 'DB_USER': 'stockone', 'HOME': '/root', 'LANG': 'C.UTF-8', 'DB_REPLICA_NAME': 'stockone', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'TERM': 'xterm', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_HOST': 'db1', 'AWS_ID': '********************', 'SHLVL': '1', 'DB_REPORTS_NAME': 'stockone', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DB_NAME': 'stockone', 'APPROVAL_SERVICE_URL': 'http://************:8001/api/v1/', 'TASK_USAGE': '0', 'DB_REPORTS_PASS': 'stockone^123', 'DB_REPORTS_HOST': 'db1', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'DB_REPLICA_PASS': 'stockone^123', 'AWS_S3_REGION_NAME': 'ap-south-1', 'DEBUG': '1', 'DB_PASS': 'stockone^123', 'ENTRYPOINT_FLAG': 'true', 'CONN_MAX_AGE': '300', 'APPROVAL_ORIGIN_NAME': 'dev-approval-app', '_': '/usr/local/bin/python'})
[2025-05-28 10:02:48,248] INFO _appmap.env: appmap enabled: False
[2025-05-28 10:05:44,776] INFO _appmap.configuration: file: /application/appmap.yml
[2025-05-28 10:05:44,776] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-05-28 10:05:44,776] INFO _appmap.configuration: env: environ({'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'PYTHONUNBUFFERED': '1', 'DB_REPLICA_USER': 'stockone', 'HOSTNAME': '02117f210751', 'PYTHON_VERSION': '3.12.10', 'DB_REPORTS_USER': 'stockone', 'DB_REPLICA_HOST': 'db1', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'PWD': '/application', 'USE_S3': 'TRUE', 'DB_USER': 'stockone', 'HOME': '/root', 'LANG': 'C.UTF-8', 'DB_REPLICA_NAME': 'stockone', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'TERM': 'xterm', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_HOST': 'db1', 'AWS_ID': '********************', 'SHLVL': '1', 'DB_REPORTS_NAME': 'stockone', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DB_NAME': 'stockone', 'APPROVAL_SERVICE_URL': 'http://************:8001/api/v1/', 'TASK_USAGE': '0', 'DB_REPORTS_PASS': 'stockone^123', 'DB_REPORTS_HOST': 'db1', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'DB_REPLICA_PASS': 'stockone^123', 'AWS_S3_REGION_NAME': 'ap-south-1', 'DEBUG': '1', 'DB_PASS': 'stockone^123', 'ENTRYPOINT_FLAG': 'true', 'CONN_MAX_AGE': '300', 'APPROVAL_ORIGIN_NAME': 'dev-approval-app', '_': '/usr/local/bin/python'})
[2025-05-28 10:05:44,779] INFO _appmap.env: appmap enabled: False
[2025-05-28 10:12:28,284] INFO _appmap.configuration: file: /application/appmap.yml
[2025-05-28 10:12:28,284] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-05-28 10:12:28,285] INFO _appmap.configuration: env: environ({'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'PYTHONUNBUFFERED': '1', 'DB_REPLICA_USER': 'stockone', 'HOSTNAME': '02117f210751', 'PYTHON_VERSION': '3.12.10', 'DB_REPORTS_USER': 'stockone', 'DB_REPLICA_HOST': 'db1', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'PWD': '/application', 'USE_S3': 'TRUE', 'DB_USER': 'stockone', 'HOME': '/root', 'LANG': 'C.UTF-8', 'DB_REPLICA_NAME': 'stockone', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'TERM': 'xterm', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_HOST': 'db1', 'AWS_ID': '********************', 'SHLVL': '1', 'DB_REPORTS_NAME': 'stockone', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DB_NAME': 'stockone', 'APPROVAL_SERVICE_URL': 'http://************:8001/api/v1/', 'TASK_USAGE': '0', 'DB_REPORTS_PASS': 'stockone^123', 'DB_REPORTS_HOST': 'db1', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'DB_REPLICA_PASS': 'stockone^123', 'AWS_S3_REGION_NAME': 'ap-south-1', 'DEBUG': '1', 'DB_PASS': 'stockone^123', 'ENTRYPOINT_FLAG': 'true', 'CONN_MAX_AGE': '300', 'APPROVAL_ORIGIN_NAME': 'dev-approval-app', '_': '/usr/local/bin/python'})
[2025-05-28 10:12:28,290] INFO _appmap.env: appmap enabled: False
[2025-05-28 10:12:28,364] INFO _appmap.env: appmap enabled: False
[2025-05-28 10:19:15,483] INFO _appmap.env: appmap enabled: False
[2025-05-28 10:19:30,331] INFO _appmap.env: appmap enabled: False
[2025-05-28 10:19:42,209] INFO _appmap.env: appmap enabled: False
[2025-05-28 10:19:43,134] INFO _appmap.env: appmap enabled: False
[2025-05-28 10:19:46,539] INFO _appmap.env: appmap enabled: False
[2025-05-28 10:19:52,555] INFO _appmap.configuration: file: /application/appmap.yml
[2025-05-28 10:19:52,555] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-05-28 10:19:52,555] INFO _appmap.configuration: env: environ({'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'PYTHONUNBUFFERED': '1', 'DB_REPLICA_USER': 'stockone', 'HOSTNAME': '02117f210751', 'PYTHON_VERSION': '3.12.10', 'DB_REPORTS_USER': 'stockone', 'DB_REPLICA_HOST': 'db1', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'PWD': '/application', 'USE_S3': 'TRUE', 'DB_USER': 'stockone', 'HOME': '/root', 'LANG': 'C.UTF-8', 'DB_REPLICA_NAME': 'stockone', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'TERM': 'xterm', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_HOST': 'db1', 'AWS_ID': '********************', 'SHLVL': '1', 'DB_REPORTS_NAME': 'stockone', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DB_NAME': 'stockone', 'APPROVAL_SERVICE_URL': 'http://************:8001/api/v1/', 'TASK_USAGE': '0', 'DB_REPORTS_PASS': 'stockone^123', 'DB_REPORTS_HOST': 'db1', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'DB_REPLICA_PASS': 'stockone^123', 'AWS_S3_REGION_NAME': 'ap-south-1', 'DEBUG': '1', 'DB_PASS': 'stockone^123', 'ENTRYPOINT_FLAG': 'true', 'CONN_MAX_AGE': '300', 'APPROVAL_ORIGIN_NAME': 'dev-approval-app', '_': '/usr/local/bin/python'})
[2025-05-28 10:19:52,559] INFO _appmap.env: appmap enabled: False
[2025-05-28 10:27:44,640] INFO _appmap.env: appmap enabled: False
[2025-05-28 10:27:49,785] INFO _appmap.env: appmap enabled: False
[2025-05-28 10:28:33,346] INFO _appmap.env: appmap enabled: False
[2025-05-28 10:28:46,984] INFO _appmap.env: appmap enabled: False
[2025-05-28 10:28:55,639] INFO _appmap.env: appmap enabled: False
[2025-05-28 10:30:32,575] INFO _appmap.env: appmap enabled: False
[2025-05-28 10:36:11,627] INFO _appmap.env: appmap enabled: False
[2025-05-28 10:36:15,148] INFO _appmap.env: appmap enabled: False
[2025-05-28 10:37:36,495] INFO _appmap.env: appmap enabled: False
[2025-05-28 10:37:51,249] INFO _appmap.env: appmap enabled: False
[2025-05-28 10:40:14,846] INFO _appmap.env: appmap enabled: False
[2025-05-28 10:40:17,167] INFO _appmap.env: appmap enabled: False
[2025-05-28 10:40:27,256] INFO _appmap.env: appmap enabled: False
[2025-05-28 10:51:11,340] INFO _appmap.env: appmap enabled: False
[2025-05-28 10:53:36,253] INFO _appmap.env: appmap enabled: False
[2025-05-28 10:53:52,801] INFO _appmap.env: appmap enabled: False
[2025-05-28 10:54:00,802] INFO _appmap.env: appmap enabled: False
[2025-05-28 10:54:03,361] INFO _appmap.configuration: file: /application/appmap.yml
[2025-05-28 10:54:03,362] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-05-28 10:54:03,362] INFO _appmap.configuration: env: environ({'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'PYTHONUNBUFFERED': '1', 'DB_REPLICA_USER': 'stockone', 'HOSTNAME': '02117f210751', 'PYTHON_VERSION': '3.12.10', 'DB_REPORTS_USER': 'stockone', 'DB_REPLICA_HOST': 'db1', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'PWD': '/application', 'USE_S3': 'TRUE', 'DB_USER': 'stockone', 'HOME': '/root', 'LANG': 'C.UTF-8', 'DB_REPLICA_NAME': 'stockone', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'TERM': 'xterm', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_HOST': 'db1', 'AWS_ID': '********************', 'SHLVL': '1', 'DB_REPORTS_NAME': 'stockone', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DB_NAME': 'stockone', 'APPROVAL_SERVICE_URL': 'http://************:8001/api/v1/', 'TASK_USAGE': '0', 'DB_REPORTS_PASS': 'stockone^123', 'DB_REPORTS_HOST': 'db1', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'DB_REPLICA_PASS': 'stockone^123', 'AWS_S3_REGION_NAME': 'ap-south-1', 'DEBUG': '1', 'DB_PASS': 'stockone^123', 'ENTRYPOINT_FLAG': 'true', 'CONN_MAX_AGE': '300', 'APPROVAL_ORIGIN_NAME': 'dev-approval-app', '_': '/usr/local/bin/python'})
[2025-05-28 10:54:03,366] INFO _appmap.env: appmap enabled: False
[2025-05-28 10:54:36,633] INFO _appmap.env: appmap enabled: False
[2025-05-28 10:54:38,703] INFO _appmap.env: appmap enabled: False
[2025-05-28 10:54:55,981] INFO _appmap.env: appmap enabled: False
[2025-05-28 10:55:04,286] INFO _appmap.env: appmap enabled: False
[2025-05-28 10:55:06,719] INFO _appmap.env: appmap enabled: False
[2025-05-28 10:57:08,408] INFO _appmap.env: appmap enabled: False
[2025-05-28 10:58:48,518] INFO _appmap.env: appmap enabled: False
[2025-05-28 10:59:19,690] INFO _appmap.env: appmap enabled: False
[2025-05-28 10:59:24,205] INFO _appmap.env: appmap enabled: False
[2025-05-28 10:59:52,589] INFO _appmap.env: appmap enabled: False
[2025-05-28 11:00:07,690] INFO _appmap.env: appmap enabled: False
[2025-05-28 11:00:09,117] INFO _appmap.env: appmap enabled: False
[2025-05-28 11:00:18,348] INFO _appmap.env: appmap enabled: False
[2025-05-28 11:00:58,266] INFO _appmap.env: appmap enabled: False
[2025-05-28 11:00:59,233] INFO _appmap.env: appmap enabled: False
[2025-05-28 11:01:54,943] INFO _appmap.env: appmap enabled: False
[2025-05-28 11:02:26,109] INFO _appmap.env: appmap enabled: False
[2025-05-28 11:02:41,805] INFO _appmap.env: appmap enabled: False
[2025-05-28 11:02:44,307] INFO _appmap.env: appmap enabled: False
[2025-05-28 11:02:48,022] INFO _appmap.env: appmap enabled: False
[2025-05-28 11:02:49,510] INFO _appmap.env: appmap enabled: False
[2025-05-28 11:02:50,804] INFO _appmap.env: appmap enabled: False
[2025-05-28 11:05:35,702] INFO _appmap.env: appmap enabled: False
[2025-05-28 11:06:15,700] INFO _appmap.configuration: file: /application/appmap.yml
[2025-05-28 11:06:15,701] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-05-28 11:06:15,701] INFO _appmap.configuration: env: environ({'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'PYTHONUNBUFFERED': '1', 'DB_REPLICA_USER': 'stockone', 'HOSTNAME': '02117f210751', 'PYTHON_VERSION': '3.12.10', 'DB_REPORTS_USER': 'stockone', 'DB_REPLICA_HOST': 'db1', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'PWD': '/application', 'USE_S3': 'TRUE', 'DB_USER': 'stockone', 'HOME': '/root', 'LANG': 'C.UTF-8', 'DB_REPLICA_NAME': 'stockone', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'TERM': 'xterm', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_HOST': 'db1', 'AWS_ID': '********************', 'SHLVL': '1', 'DB_REPORTS_NAME': 'stockone', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DB_NAME': 'stockone', 'APPROVAL_SERVICE_URL': 'http://************:8001/api/v1/', 'TASK_USAGE': '0', 'DB_REPORTS_PASS': 'stockone^123', 'DB_REPORTS_HOST': 'db1', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'DB_REPLICA_PASS': 'stockone^123', 'AWS_S3_REGION_NAME': 'ap-south-1', 'DEBUG': '1', 'DB_PASS': 'stockone^123', 'ENTRYPOINT_FLAG': 'true', 'CONN_MAX_AGE': '300', 'APPROVAL_ORIGIN_NAME': 'dev-approval-app', '_': '/usr/local/bin/python'})
[2025-05-28 11:06:15,709] INFO _appmap.env: appmap enabled: False
[2025-05-28 11:24:59,556] INFO _appmap.env: appmap enabled: False
[2025-05-28 11:41:37,210] INFO _appmap.configuration: file: /application/appmap.yml
[2025-05-28 11:41:37,211] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-05-28 11:41:37,211] INFO _appmap.configuration: env: environ({'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'PYTHONUNBUFFERED': '1', 'DB_REPLICA_USER': 'stockone', 'HOSTNAME': '02117f210751', 'PYTHON_VERSION': '3.12.10', 'DB_REPORTS_USER': 'stockone', 'DB_REPLICA_HOST': 'db1', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'PWD': '/application', 'USE_S3': 'TRUE', 'DB_USER': 'stockone', 'HOME': '/root', 'LANG': 'C.UTF-8', 'DB_REPLICA_NAME': 'stockone', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'TERM': 'xterm', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_HOST': 'db1', 'AWS_ID': '********************', 'SHLVL': '1', 'DB_REPORTS_NAME': 'stockone', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DB_NAME': 'stockone', 'APPROVAL_SERVICE_URL': 'http://************:8001/api/v1/', 'TASK_USAGE': '0', 'DB_REPORTS_PASS': 'stockone^123', 'DB_REPORTS_HOST': 'db1', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'DB_REPLICA_PASS': 'stockone^123', 'AWS_S3_REGION_NAME': 'ap-south-1', 'DEBUG': '1', 'DB_PASS': 'stockone^123', 'ENTRYPOINT_FLAG': 'true', 'CONN_MAX_AGE': '300', 'APPROVAL_ORIGIN_NAME': 'dev-approval-app', '_': '/usr/local/bin/python'})
[2025-05-28 11:41:37,223] INFO _appmap.env: appmap enabled: False
[2025-05-28 11:51:13,179] INFO _appmap.configuration: file: /application/appmap.yml
[2025-05-28 11:51:13,180] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-05-28 11:51:13,180] INFO _appmap.configuration: env: environ({'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'PYTHONUNBUFFERED': '1', 'DB_REPLICA_USER': 'stockone', 'HOSTNAME': '02117f210751', 'PYTHON_VERSION': '3.12.10', 'DB_REPORTS_USER': 'stockone', 'DB_REPLICA_HOST': 'db1', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'PWD': '/application', 'USE_S3': 'TRUE', 'DB_USER': 'stockone', 'HOME': '/root', 'LANG': 'C.UTF-8', 'DB_REPLICA_NAME': 'stockone', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'TERM': 'xterm', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_HOST': 'db1', 'AWS_ID': '********************', 'SHLVL': '1', 'DB_REPORTS_NAME': 'stockone', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DB_NAME': 'stockone', 'APPROVAL_SERVICE_URL': 'http://************:8001/api/v1/', 'TASK_USAGE': '0', 'DB_REPORTS_PASS': 'stockone^123', 'DB_REPORTS_HOST': 'db1', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'DB_REPLICA_PASS': 'stockone^123', 'AWS_S3_REGION_NAME': 'ap-south-1', 'DEBUG': '1', 'DB_PASS': 'stockone^123', 'ENTRYPOINT_FLAG': 'true', 'CONN_MAX_AGE': '300', 'APPROVAL_ORIGIN_NAME': 'dev-approval-app', '_': '/usr/local/bin/python'})
[2025-05-28 11:51:13,186] INFO _appmap.env: appmap enabled: False
[2025-05-28 12:45:13,849] INFO _appmap.configuration: file: /application/appmap.yml
[2025-05-28 12:45:13,850] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-05-28 12:45:13,850] INFO _appmap.configuration: env: environ({'APPROVAL_SERVICE_URL': 'http://************:8001/api/v1/', 'HOSTNAME': '3939c9a8890f', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DEBUG': '1', 'HOME': '/root', 'DB_NAME': 'stockone', 'PYTHONUNBUFFERED': '1', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'CONN_MAX_AGE': '300', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'DB_REPLICA_PASS': 'stockone^123', 'TASK_USAGE': '0', 'APPROVAL_ORIGIN_NAME': 'dev-approval-app', 'ENTRYPOINT_FLAG': 'true', 'AWS_S3_REGION_NAME': 'ap-south-1', 'TERM': 'xterm', 'DB_REPLICA_HOST': 'db1', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'DB_REPLICA_USER': 'stockone', 'DB_REPORTS_PASS': 'stockone^123', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'LANG': 'C.UTF-8', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_REPORTS_HOST': 'db1', 'DB_REPORTS_USER': 'stockone', 'PYTHON_VERSION': '3.12.10', 'AWS_ID': '********************', 'DB_REPLICA_NAME': 'stockone', 'DB_PASS': 'stockone^123', 'USE_S3': 'TRUE', 'PWD': '/application', 'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'DB_HOST': 'db1', 'DB_REPORTS_NAME': 'stockone', 'DB_USER': 'stockone'})
[2025-05-28 12:45:13,859] INFO _appmap.env: appmap enabled: False
[2025-05-28 12:45:22,185] INFO _appmap.configuration: file: /application/appmap.yml
[2025-05-28 12:45:22,185] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-05-28 12:45:22,185] INFO _appmap.configuration: env: environ({'APPROVAL_SERVICE_URL': 'http://************:8001/api/v1/', 'HOSTNAME': '3939c9a8890f', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DEBUG': '1', 'HOME': '/root', 'DB_NAME': 'stockone', 'PYTHONUNBUFFERED': '1', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'CONN_MAX_AGE': '300', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'DB_REPLICA_PASS': 'stockone^123', 'TASK_USAGE': '0', 'APPROVAL_ORIGIN_NAME': 'dev-approval-app', 'ENTRYPOINT_FLAG': 'true', 'AWS_S3_REGION_NAME': 'ap-south-1', 'TERM': 'xterm', 'DB_REPLICA_HOST': 'db1', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'DB_REPLICA_USER': 'stockone', 'DB_REPORTS_PASS': 'stockone^123', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'LANG': 'C.UTF-8', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_REPORTS_HOST': 'db1', 'DB_REPORTS_USER': 'stockone', 'PYTHON_VERSION': '3.12.10', 'AWS_ID': '********************', 'DB_REPLICA_NAME': 'stockone', 'DB_PASS': 'stockone^123', 'USE_S3': 'TRUE', 'PWD': '/application', 'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'DB_HOST': 'db1', 'DB_REPORTS_NAME': 'stockone', 'DB_USER': 'stockone'})
[2025-05-28 12:45:22,188] INFO _appmap.env: appmap enabled: False
[2025-05-28 12:45:24,996] INFO _appmap.configuration: file: /application/appmap.yml
[2025-05-28 12:45:24,997] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-05-28 12:45:24,997] INFO _appmap.configuration: env: environ({'APPROVAL_SERVICE_URL': 'http://************:8001/api/v1/', 'HOSTNAME': '3939c9a8890f', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DEBUG': '1', 'HOME': '/root', 'DB_NAME': 'stockone', 'PYTHONUNBUFFERED': '1', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'CONN_MAX_AGE': '300', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'DB_REPLICA_PASS': 'stockone^123', 'TASK_USAGE': '0', 'APPROVAL_ORIGIN_NAME': 'dev-approval-app', 'ENTRYPOINT_FLAG': 'true', 'AWS_S3_REGION_NAME': 'ap-south-1', 'TERM': 'xterm', 'DB_REPLICA_HOST': 'db1', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'DB_REPLICA_USER': 'stockone', 'DB_REPORTS_PASS': 'stockone^123', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'LANG': 'C.UTF-8', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_REPORTS_HOST': 'db1', 'DB_REPORTS_USER': 'stockone', 'PYTHON_VERSION': '3.12.10', 'AWS_ID': '********************', 'DB_REPLICA_NAME': 'stockone', 'DB_PASS': 'stockone^123', 'USE_S3': 'TRUE', 'PWD': '/application', 'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'DB_HOST': 'db1', 'DB_REPORTS_NAME': 'stockone', 'DB_USER': 'stockone'})
[2025-05-28 12:45:25,000] INFO _appmap.env: appmap enabled: False
[2025-05-28 12:45:27,843] INFO _appmap.configuration: file: /application/appmap.yml
[2025-05-28 12:45:27,843] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-05-28 12:45:27,843] INFO _appmap.configuration: env: environ({'APPROVAL_SERVICE_URL': 'http://************:8001/api/v1/', 'HOSTNAME': '3939c9a8890f', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DEBUG': '1', 'HOME': '/root', 'DB_NAME': 'stockone', 'PYTHONUNBUFFERED': '1', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'CONN_MAX_AGE': '300', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'DB_REPLICA_PASS': 'stockone^123', 'TASK_USAGE': '0', 'APPROVAL_ORIGIN_NAME': 'dev-approval-app', 'ENTRYPOINT_FLAG': 'true', 'AWS_S3_REGION_NAME': 'ap-south-1', 'TERM': 'xterm', 'DB_REPLICA_HOST': 'db1', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'DB_REPLICA_USER': 'stockone', 'DB_REPORTS_PASS': 'stockone^123', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'LANG': 'C.UTF-8', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_REPORTS_HOST': 'db1', 'DB_REPORTS_USER': 'stockone', 'PYTHON_VERSION': '3.12.10', 'AWS_ID': '********************', 'DB_REPLICA_NAME': 'stockone', 'DB_PASS': 'stockone^123', 'USE_S3': 'TRUE', 'PWD': '/application', 'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'DB_HOST': 'db1', 'DB_REPORTS_NAME': 'stockone', 'DB_USER': 'stockone'})
[2025-05-28 12:45:27,846] INFO _appmap.env: appmap enabled: False
[2025-05-28 12:45:32,200] INFO _appmap.configuration: file: /application/appmap.yml
[2025-05-28 12:45:32,201] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-05-28 12:45:32,201] INFO _appmap.configuration: env: environ({'APPROVAL_SERVICE_URL': 'http://************:8001/api/v1/', 'HOSTNAME': '3939c9a8890f', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DEBUG': '1', 'HOME': '/root', 'DB_NAME': 'stockone', 'PYTHONUNBUFFERED': '1', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'CONN_MAX_AGE': '300', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'DB_REPLICA_PASS': 'stockone^123', 'TASK_USAGE': '0', 'APPROVAL_ORIGIN_NAME': 'dev-approval-app', 'ENTRYPOINT_FLAG': 'true', 'AWS_S3_REGION_NAME': 'ap-south-1', 'TERM': 'xterm', 'DB_REPLICA_HOST': 'db1', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'DB_REPLICA_USER': 'stockone', 'DB_REPORTS_PASS': 'stockone^123', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'LANG': 'C.UTF-8', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_REPORTS_HOST': 'db1', 'DB_REPORTS_USER': 'stockone', 'PYTHON_VERSION': '3.12.10', 'AWS_ID': '********************', 'DB_REPLICA_NAME': 'stockone', 'DB_PASS': 'stockone^123', 'USE_S3': 'TRUE', 'PWD': '/application', 'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'DB_HOST': 'db1', 'DB_REPORTS_NAME': 'stockone', 'DB_USER': 'stockone'})
[2025-05-28 12:45:32,211] INFO _appmap.env: appmap enabled: False
[2025-05-28 12:45:32,319] INFO _appmap.env: appmap enabled: False
[2025-05-28 12:45:32,321] INFO _appmap.env: appmap enabled: False
[2025-05-28 12:46:30,322] INFO _appmap.configuration: file: /application/appmap.yml
[2025-05-28 12:46:30,323] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-05-28 12:46:30,323] INFO _appmap.configuration: env: environ({'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'PYTHONUNBUFFERED': '1', 'DB_REPLICA_USER': 'stockone', 'HOSTNAME': '3939c9a8890f', 'PYTHON_VERSION': '3.12.10', 'DB_REPORTS_USER': 'stockone', 'DB_REPLICA_HOST': 'db1', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'PWD': '/application', 'USE_S3': 'TRUE', 'DB_USER': 'stockone', 'HOME': '/root', 'LANG': 'C.UTF-8', 'DB_REPLICA_NAME': 'stockone', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'TERM': 'xterm', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_HOST': 'db1', 'AWS_ID': '********************', 'SHLVL': '1', 'DB_REPORTS_NAME': 'stockone', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DB_NAME': 'stockone', 'APPROVAL_SERVICE_URL': 'http://************:8001/api/v1/', 'TASK_USAGE': '0', 'DB_REPORTS_PASS': 'stockone^123', 'DB_REPORTS_HOST': 'db1', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'DB_REPLICA_PASS': 'stockone^123', 'AWS_S3_REGION_NAME': 'ap-south-1', 'DEBUG': '1', 'DB_PASS': 'stockone^123', 'ENTRYPOINT_FLAG': 'true', 'CONN_MAX_AGE': '300', 'APPROVAL_ORIGIN_NAME': 'dev-approval-app', '_': '/usr/local/bin/python'})
[2025-05-28 12:46:30,331] INFO _appmap.env: appmap enabled: False
[2025-05-28 13:02:58,471] INFO _appmap.env: appmap enabled: False
[2025-05-28 13:03:12,256] INFO _appmap.env: appmap enabled: False
[2025-05-28 13:03:14,253] INFO _appmap.env: appmap enabled: False
[2025-05-28 13:03:17,358] INFO _appmap.configuration: file: /application/appmap.yml
[2025-05-28 13:03:17,359] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-05-28 13:03:17,359] INFO _appmap.configuration: env: environ({'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'PYTHONUNBUFFERED': '1', 'DB_REPLICA_USER': 'stockone', 'HOSTNAME': '3939c9a8890f', 'PYTHON_VERSION': '3.12.10', 'DB_REPORTS_USER': 'stockone', 'DB_REPLICA_HOST': 'db1', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'PWD': '/application', 'USE_S3': 'TRUE', 'DB_USER': 'stockone', 'HOME': '/root', 'LANG': 'C.UTF-8', 'DB_REPLICA_NAME': 'stockone', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'TERM': 'xterm', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_HOST': 'db1', 'AWS_ID': '********************', 'SHLVL': '1', 'DB_REPORTS_NAME': 'stockone', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DB_NAME': 'stockone', 'APPROVAL_SERVICE_URL': 'http://************:8001/api/v1/', 'TASK_USAGE': '0', 'DB_REPORTS_PASS': 'stockone^123', 'DB_REPORTS_HOST': 'db1', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'DB_REPLICA_PASS': 'stockone^123', 'AWS_S3_REGION_NAME': 'ap-south-1', 'DEBUG': '1', 'DB_PASS': 'stockone^123', 'ENTRYPOINT_FLAG': 'true', 'CONN_MAX_AGE': '300', 'APPROVAL_ORIGIN_NAME': 'dev-approval-app', '_': '/usr/local/bin/python'})
[2025-05-28 13:03:17,364] INFO _appmap.env: appmap enabled: False
[2025-05-28 13:03:27,044] INFO _appmap.env: appmap enabled: False
[2025-05-28 13:03:28,583] INFO _appmap.configuration: file: /application/appmap.yml
[2025-05-28 13:03:28,583] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-05-28 13:03:28,583] INFO _appmap.configuration: env: environ({'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'PYTHONUNBUFFERED': '1', 'DB_REPLICA_USER': 'stockone', 'HOSTNAME': '3939c9a8890f', 'PYTHON_VERSION': '3.12.10', 'DB_REPORTS_USER': 'stockone', 'DB_REPLICA_HOST': 'db1', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'PWD': '/application', 'USE_S3': 'TRUE', 'DB_USER': 'stockone', 'HOME': '/root', 'LANG': 'C.UTF-8', 'DB_REPLICA_NAME': 'stockone', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'TERM': 'xterm', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_HOST': 'db1', 'AWS_ID': '********************', 'SHLVL': '1', 'DB_REPORTS_NAME': 'stockone', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DB_NAME': 'stockone', 'APPROVAL_SERVICE_URL': 'http://************:8001/api/v1/', 'TASK_USAGE': '0', 'DB_REPORTS_PASS': 'stockone^123', 'DB_REPORTS_HOST': 'db1', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'DB_REPLICA_PASS': 'stockone^123', 'AWS_S3_REGION_NAME': 'ap-south-1', 'DEBUG': '1', 'DB_PASS': 'stockone^123', 'ENTRYPOINT_FLAG': 'true', 'CONN_MAX_AGE': '300', 'APPROVAL_ORIGIN_NAME': 'dev-approval-app', '_': '/usr/local/bin/python'})
[2025-05-28 13:03:28,592] INFO _appmap.env: appmap enabled: False
[2025-05-28 13:04:29,540] INFO _appmap.env: appmap enabled: False
[2025-05-28 13:04:35,261] INFO _appmap.env: appmap enabled: False
[2025-05-28 13:05:27,825] INFO _appmap.env: appmap enabled: False
[2025-05-28 13:05:30,850] INFO _appmap.configuration: file: /application/appmap.yml
[2025-05-28 13:05:30,851] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-05-28 13:05:30,851] INFO _appmap.configuration: env: environ({'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'PYTHONUNBUFFERED': '1', 'DB_REPLICA_USER': 'stockone', 'HOSTNAME': '3939c9a8890f', 'PYTHON_VERSION': '3.12.10', 'DB_REPORTS_USER': 'stockone', 'DB_REPLICA_HOST': 'db1', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'PWD': '/application', 'USE_S3': 'TRUE', 'DB_USER': 'stockone', 'HOME': '/root', 'LANG': 'C.UTF-8', 'DB_REPLICA_NAME': 'stockone', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'TERM': 'xterm', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_HOST': 'db1', 'AWS_ID': '********************', 'SHLVL': '1', 'DB_REPORTS_NAME': 'stockone', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DB_NAME': 'stockone', 'APPROVAL_SERVICE_URL': 'http://************:8001/api/v1/', 'TASK_USAGE': '0', 'DB_REPORTS_PASS': 'stockone^123', 'DB_REPORTS_HOST': 'db1', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'DB_REPLICA_PASS': 'stockone^123', 'AWS_S3_REGION_NAME': 'ap-south-1', 'DEBUG': '1', 'DB_PASS': 'stockone^123', 'ENTRYPOINT_FLAG': 'true', 'CONN_MAX_AGE': '300', 'APPROVAL_ORIGIN_NAME': 'dev-approval-app', '_': '/usr/local/bin/python'})
[2025-05-28 13:05:30,857] INFO _appmap.env: appmap enabled: False
[2025-05-28 13:10:24,549] INFO _appmap.configuration: file: /application/appmap.yml
[2025-05-28 13:10:24,550] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-05-28 13:10:24,550] INFO _appmap.configuration: env: environ({'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'PYTHONUNBUFFERED': '1', 'DB_REPLICA_USER': 'stockone', 'HOSTNAME': '3939c9a8890f', 'PYTHON_VERSION': '3.12.10', 'DB_REPORTS_USER': 'stockone', 'DB_REPLICA_HOST': 'db1', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'PWD': '/application', 'USE_S3': 'TRUE', 'DB_USER': 'stockone', 'HOME': '/root', 'LANG': 'C.UTF-8', 'DB_REPLICA_NAME': 'stockone', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'TERM': 'xterm', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_HOST': 'db1', 'AWS_ID': '********************', 'SHLVL': '1', 'DB_REPORTS_NAME': 'stockone', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DB_NAME': 'stockone', 'APPROVAL_SERVICE_URL': 'http://************:8001/api/v1/', 'TASK_USAGE': '0', 'DB_REPORTS_PASS': 'stockone^123', 'DB_REPORTS_HOST': 'db1', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'DB_REPLICA_PASS': 'stockone^123', 'AWS_S3_REGION_NAME': 'ap-south-1', 'DEBUG': '1', 'DB_PASS': 'stockone^123', 'ENTRYPOINT_FLAG': 'true', 'CONN_MAX_AGE': '300', 'APPROVAL_ORIGIN_NAME': 'dev-approval-app', '_': '/usr/local/bin/python'})
[2025-05-28 13:10:24,554] INFO _appmap.env: appmap enabled: False
[2025-05-28 13:14:45,951] INFO _appmap.env: appmap enabled: False
[2025-05-28 13:15:07,383] INFO _appmap.env: appmap enabled: False
[2025-05-28 13:42:52,004] INFO _appmap.env: appmap enabled: False
[2025-05-28 13:42:56,349] INFO _appmap.env: appmap enabled: False
[2025-05-28 13:43:01,298] INFO _appmap.env: appmap enabled: False
[2025-05-28 13:43:18,626] INFO _appmap.env: appmap enabled: False
[2025-05-28 13:43:24,920] INFO _appmap.env: appmap enabled: False
[2025-05-28 13:43:36,720] INFO _appmap.env: appmap enabled: False
[2025-05-28 13:43:37,811] INFO _appmap.configuration: file: /application/appmap.yml
[2025-05-28 13:43:37,813] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-05-28 13:43:37,813] INFO _appmap.configuration: env: environ({'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'PYTHONUNBUFFERED': '1', 'DB_REPLICA_USER': 'stockone', 'HOSTNAME': '3939c9a8890f', 'PYTHON_VERSION': '3.12.10', 'DB_REPORTS_USER': 'stockone', 'DB_REPLICA_HOST': 'db1', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'PWD': '/application', 'USE_S3': 'TRUE', 'DB_USER': 'stockone', 'HOME': '/root', 'LANG': 'C.UTF-8', 'DB_REPLICA_NAME': 'stockone', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'TERM': 'xterm', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_HOST': 'db1', 'AWS_ID': '********************', 'SHLVL': '1', 'DB_REPORTS_NAME': 'stockone', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DB_NAME': 'stockone', 'APPROVAL_SERVICE_URL': 'http://************:8001/api/v1/', 'TASK_USAGE': '0', 'DB_REPORTS_PASS': 'stockone^123', 'DB_REPORTS_HOST': 'db1', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'DB_REPLICA_PASS': 'stockone^123', 'AWS_S3_REGION_NAME': 'ap-south-1', 'DEBUG': '1', 'DB_PASS': 'stockone^123', 'ENTRYPOINT_FLAG': 'true', 'CONN_MAX_AGE': '300', 'APPROVAL_ORIGIN_NAME': 'dev-approval-app', '_': '/usr/local/bin/python'})
[2025-05-28 13:43:37,818] INFO _appmap.env: appmap enabled: False
[2025-05-28 13:51:35,660] INFO _appmap.env: appmap enabled: False
[2025-05-28 13:51:57,794] INFO _appmap.env: appmap enabled: False
[2025-05-28 13:52:06,941] INFO _appmap.env: appmap enabled: False
[2025-05-28 13:52:13,390] INFO _appmap.env: appmap enabled: False
[2025-05-28 13:52:15,249] INFO _appmap.env: appmap enabled: False
[2025-05-28 13:52:39,221] INFO _appmap.env: appmap enabled: False
[2025-05-28 13:52:39,947] INFO _appmap.configuration: file: /application/appmap.yml
[2025-05-28 13:52:39,947] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-05-28 13:52:39,947] INFO _appmap.configuration: env: environ({'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'PYTHONUNBUFFERED': '1', 'DB_REPLICA_USER': 'stockone', 'HOSTNAME': '3939c9a8890f', 'PYTHON_VERSION': '3.12.10', 'DB_REPORTS_USER': 'stockone', 'DB_REPLICA_HOST': 'db1', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'PWD': '/application', 'USE_S3': 'TRUE', 'DB_USER': 'stockone', 'HOME': '/root', 'LANG': 'C.UTF-8', 'DB_REPLICA_NAME': 'stockone', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'TERM': 'xterm', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_HOST': 'db1', 'AWS_ID': '********************', 'SHLVL': '1', 'DB_REPORTS_NAME': 'stockone', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DB_NAME': 'stockone', 'APPROVAL_SERVICE_URL': 'http://************:8001/api/v1/', 'TASK_USAGE': '0', 'DB_REPORTS_PASS': 'stockone^123', 'DB_REPORTS_HOST': 'db1', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'DB_REPLICA_PASS': 'stockone^123', 'AWS_S3_REGION_NAME': 'ap-south-1', 'DEBUG': '1', 'DB_PASS': 'stockone^123', 'ENTRYPOINT_FLAG': 'true', 'CONN_MAX_AGE': '300', 'APPROVAL_ORIGIN_NAME': 'dev-approval-app', '_': '/usr/local/bin/python'})
[2025-05-28 13:52:39,951] INFO _appmap.env: appmap enabled: False
[2025-05-28 13:52:49,075] INFO _appmap.configuration: file: /application/appmap.yml
[2025-05-28 13:52:49,076] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-05-28 13:52:49,076] INFO _appmap.configuration: env: environ({'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'PYTHONUNBUFFERED': '1', 'DB_REPLICA_USER': 'stockone', 'HOSTNAME': '3939c9a8890f', 'PYTHON_VERSION': '3.12.10', 'DB_REPORTS_USER': 'stockone', 'DB_REPLICA_HOST': 'db1', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'PWD': '/application', 'USE_S3': 'TRUE', 'DB_USER': 'stockone', 'HOME': '/root', 'LANG': 'C.UTF-8', 'DB_REPLICA_NAME': 'stockone', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'TERM': 'xterm', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_HOST': 'db1', 'AWS_ID': '********************', 'SHLVL': '1', 'DB_REPORTS_NAME': 'stockone', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DB_NAME': 'stockone', 'APPROVAL_SERVICE_URL': 'http://************:8001/api/v1/', 'TASK_USAGE': '0', 'DB_REPORTS_PASS': 'stockone^123', 'DB_REPORTS_HOST': 'db1', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'DB_REPLICA_PASS': 'stockone^123', 'AWS_S3_REGION_NAME': 'ap-south-1', 'DEBUG': '1', 'DB_PASS': 'stockone^123', 'ENTRYPOINT_FLAG': 'true', 'CONN_MAX_AGE': '300', 'APPROVAL_ORIGIN_NAME': 'dev-approval-app', '_': '/usr/local/bin/python'})
[2025-05-28 13:52:49,080] INFO _appmap.env: appmap enabled: False
[2025-05-28 13:58:15,864] INFO _appmap.configuration: file: /application/appmap.yml
[2025-05-28 13:58:15,865] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-05-28 13:58:15,865] INFO _appmap.configuration: env: environ({'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'PYTHONUNBUFFERED': '1', 'DB_REPLICA_USER': 'stockone', 'HOSTNAME': '3939c9a8890f', 'PYTHON_VERSION': '3.12.10', 'DB_REPORTS_USER': 'stockone', 'DB_REPLICA_HOST': 'db1', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'PWD': '/application', 'USE_S3': 'TRUE', 'DB_USER': 'stockone', 'HOME': '/root', 'LANG': 'C.UTF-8', 'DB_REPLICA_NAME': 'stockone', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'TERM': 'xterm', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_HOST': 'db1', 'AWS_ID': '********************', 'SHLVL': '1', 'DB_REPORTS_NAME': 'stockone', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DB_NAME': 'stockone', 'APPROVAL_SERVICE_URL': 'http://************:8001/api/v1/', 'TASK_USAGE': '0', 'DB_REPORTS_PASS': 'stockone^123', 'DB_REPORTS_HOST': 'db1', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'DB_REPLICA_PASS': 'stockone^123', 'AWS_S3_REGION_NAME': 'ap-south-1', 'DEBUG': '1', 'DB_PASS': 'stockone^123', 'ENTRYPOINT_FLAG': 'true', 'CONN_MAX_AGE': '300', 'APPROVAL_ORIGIN_NAME': 'dev-approval-app', '_': '/usr/local/bin/python'})
[2025-05-28 13:58:15,869] INFO _appmap.env: appmap enabled: False
[2025-05-28 14:03:59,858] INFO _appmap.configuration: file: /application/appmap.yml
[2025-05-28 14:03:59,859] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-05-28 14:03:59,859] INFO _appmap.configuration: env: environ({'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'PYTHONUNBUFFERED': '1', 'DB_REPLICA_USER': 'stockone', 'HOSTNAME': '3939c9a8890f', 'PYTHON_VERSION': '3.12.10', 'DB_REPORTS_USER': 'stockone', 'DB_REPLICA_HOST': 'db1', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'PWD': '/application', 'USE_S3': 'TRUE', 'DB_USER': 'stockone', 'HOME': '/root', 'LANG': 'C.UTF-8', 'DB_REPLICA_NAME': 'stockone', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'TERM': 'xterm', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_HOST': 'db1', 'AWS_ID': '********************', 'SHLVL': '1', 'DB_REPORTS_NAME': 'stockone', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DB_NAME': 'stockone', 'APPROVAL_SERVICE_URL': 'http://************:8001/api/v1/', 'TASK_USAGE': '0', 'DB_REPORTS_PASS': 'stockone^123', 'DB_REPORTS_HOST': 'db1', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'DB_REPLICA_PASS': 'stockone^123', 'AWS_S3_REGION_NAME': 'ap-south-1', 'DEBUG': '1', 'DB_PASS': 'stockone^123', 'ENTRYPOINT_FLAG': 'true', 'CONN_MAX_AGE': '300', 'APPROVAL_ORIGIN_NAME': 'dev-approval-app', '_': '/usr/local/bin/python'})
[2025-05-28 14:03:59,866] INFO _appmap.env: appmap enabled: False
[2025-05-29 05:34:14,253] INFO _appmap.configuration: file: /application/appmap.yml
[2025-05-29 05:34:14,254] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-05-29 05:34:14,254] INFO _appmap.configuration: env: environ({'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'PYTHONUNBUFFERED': '1', 'DB_REPLICA_USER': 'stockone', 'HOSTNAME': '3939c9a8890f', 'PYTHON_VERSION': '3.12.10', 'DB_REPORTS_USER': 'stockone', 'DB_REPLICA_HOST': 'db1', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'PWD': '/application', 'USE_S3': 'TRUE', 'DB_USER': 'stockone', 'HOME': '/root', 'LANG': 'C.UTF-8', 'DB_REPLICA_NAME': 'stockone', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'TERM': 'xterm', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_HOST': 'db1', 'AWS_ID': '********************', 'SHLVL': '1', 'DB_REPORTS_NAME': 'stockone', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DB_NAME': 'stockone', 'APPROVAL_SERVICE_URL': 'http://************:8001/api/v1/', 'TASK_USAGE': '0', 'DB_REPORTS_PASS': 'stockone^123', 'DB_REPORTS_HOST': 'db1', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'DB_REPLICA_PASS': 'stockone^123', 'AWS_S3_REGION_NAME': 'ap-south-1', 'DEBUG': '1', 'DB_PASS': 'stockone^123', 'ENTRYPOINT_FLAG': 'true', 'CONN_MAX_AGE': '300', 'APPROVAL_ORIGIN_NAME': 'dev-approval-app', '_': '/usr/local/bin/python'})
[2025-05-29 05:34:14,264] INFO _appmap.env: appmap enabled: False
[2025-05-29 05:38:43,097] INFO _appmap.env: appmap enabled: False
[2025-05-29 05:39:04,742] INFO _appmap.configuration: file: /application/appmap.yml
[2025-05-29 05:39:04,743] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-05-29 05:39:04,743] INFO _appmap.configuration: env: environ({'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'PYTHONUNBUFFERED': '1', 'DB_REPLICA_USER': 'stockone', 'HOSTNAME': '3939c9a8890f', 'PYTHON_VERSION': '3.12.10', 'DB_REPORTS_USER': 'stockone', 'DB_REPLICA_HOST': 'db1', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'PWD': '/application', 'USE_S3': 'TRUE', 'DB_USER': 'stockone', 'HOME': '/root', 'LANG': 'C.UTF-8', 'DB_REPLICA_NAME': 'stockone', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'TERM': 'xterm', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_HOST': 'db1', 'AWS_ID': '********************', 'SHLVL': '1', 'DB_REPORTS_NAME': 'stockone', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DB_NAME': 'stockone', 'APPROVAL_SERVICE_URL': 'http://************:8001/api/v1/', 'TASK_USAGE': '0', 'DB_REPORTS_PASS': 'stockone^123', 'DB_REPORTS_HOST': 'db1', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'DB_REPLICA_PASS': 'stockone^123', 'AWS_S3_REGION_NAME': 'ap-south-1', 'DEBUG': '1', 'DB_PASS': 'stockone^123', 'ENTRYPOINT_FLAG': 'true', 'CONN_MAX_AGE': '300', 'APPROVAL_ORIGIN_NAME': 'dev-approval-app', '_': '/usr/local/bin/python'})
[2025-05-29 05:39:04,750] INFO _appmap.env: appmap enabled: False
[2025-05-29 05:41:50,385] INFO _appmap.env: appmap enabled: False
[2025-05-29 05:50:32,924] INFO _appmap.env: appmap enabled: False
[2025-05-29 05:50:42,845] INFO _appmap.env: appmap enabled: False
[2025-05-29 05:51:37,884] INFO _appmap.env: appmap enabled: False
[2025-05-29 05:52:09,290] INFO _appmap.env: appmap enabled: False
[2025-05-29 05:53:20,577] INFO _appmap.env: appmap enabled: False
[2025-05-29 05:53:21,495] INFO _appmap.env: appmap enabled: False
[2025-05-29 05:53:58,632] INFO _appmap.env: appmap enabled: False
[2025-05-29 05:55:18,775] INFO _appmap.env: appmap enabled: False
[2025-05-29 05:55:46,736] INFO _appmap.env: appmap enabled: False
[2025-05-29 05:56:26,511] INFO _appmap.env: appmap enabled: False
[2025-05-29 05:56:34,273] INFO _appmap.configuration: file: /application/appmap.yml
[2025-05-29 05:56:34,274] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-05-29 05:56:34,274] INFO _appmap.configuration: env: environ({'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'PYTHONUNBUFFERED': '1', 'DB_REPLICA_USER': 'stockone', 'HOSTNAME': '3939c9a8890f', 'PYTHON_VERSION': '3.12.10', 'DB_REPORTS_USER': 'stockone', 'DB_REPLICA_HOST': 'db1', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'PWD': '/application', 'USE_S3': 'TRUE', 'DB_USER': 'stockone', 'HOME': '/root', 'LANG': 'C.UTF-8', 'DB_REPLICA_NAME': 'stockone', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'TERM': 'xterm', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_HOST': 'db1', 'AWS_ID': '********************', 'SHLVL': '1', 'DB_REPORTS_NAME': 'stockone', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DB_NAME': 'stockone', 'APPROVAL_SERVICE_URL': 'http://************:8001/api/v1/', 'TASK_USAGE': '0', 'DB_REPORTS_PASS': 'stockone^123', 'DB_REPORTS_HOST': 'db1', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'DB_REPLICA_PASS': 'stockone^123', 'AWS_S3_REGION_NAME': 'ap-south-1', 'DEBUG': '1', 'DB_PASS': 'stockone^123', 'ENTRYPOINT_FLAG': 'true', 'CONN_MAX_AGE': '300', 'APPROVAL_ORIGIN_NAME': 'dev-approval-app', '_': '/usr/local/bin/python'})
[2025-05-29 05:56:34,281] INFO _appmap.env: appmap enabled: False
[2025-05-29 06:00:26,372] INFO _appmap.env: appmap enabled: False
[2025-05-29 06:08:33,966] INFO _appmap.env: appmap enabled: False
[2025-05-29 06:12:19,432] INFO _appmap.configuration: file: /application/appmap.yml
[2025-05-29 06:12:19,433] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-05-29 06:12:19,433] INFO _appmap.configuration: env: environ({'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'PYTHONUNBUFFERED': '1', 'DB_REPLICA_USER': 'stockone', 'HOSTNAME': '3939c9a8890f', 'PYTHON_VERSION': '3.12.10', 'DB_REPORTS_USER': 'stockone', 'DB_REPLICA_HOST': 'db1', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'PWD': '/application', 'USE_S3': 'TRUE', 'DB_USER': 'stockone', 'HOME': '/root', 'LANG': 'C.UTF-8', 'DB_REPLICA_NAME': 'stockone', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'TERM': 'xterm', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_HOST': 'db1', 'AWS_ID': '********************', 'SHLVL': '1', 'DB_REPORTS_NAME': 'stockone', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DB_NAME': 'stockone', 'APPROVAL_SERVICE_URL': 'http://************:8001/api/v1/', 'TASK_USAGE': '0', 'DB_REPORTS_PASS': 'stockone^123', 'DB_REPORTS_HOST': 'db1', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'DB_REPLICA_PASS': 'stockone^123', 'AWS_S3_REGION_NAME': 'ap-south-1', 'DEBUG': '1', 'DB_PASS': 'stockone^123', 'ENTRYPOINT_FLAG': 'true', 'CONN_MAX_AGE': '300', 'APPROVAL_ORIGIN_NAME': 'dev-approval-app', '_': '/usr/local/bin/python'})
[2025-05-29 06:12:19,451] INFO _appmap.env: appmap enabled: False
[2025-05-29 06:14:03,082] INFO _appmap.env: appmap enabled: False
[2025-05-29 06:14:21,156] INFO _appmap.env: appmap enabled: False
[2025-05-29 06:14:26,514] INFO _appmap.env: appmap enabled: False
[2025-05-29 06:15:46,199] INFO _appmap.env: appmap enabled: False
[2025-05-29 06:15:47,478] INFO _appmap.env: appmap enabled: False
[2025-05-29 06:15:57,932] INFO _appmap.env: appmap enabled: False
[2025-05-29 06:16:03,041] INFO _appmap.configuration: file: /application/appmap.yml
[2025-05-29 06:16:03,041] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-05-29 06:16:03,041] INFO _appmap.configuration: env: environ({'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'PYTHONUNBUFFERED': '1', 'DB_REPLICA_USER': 'stockone', 'HOSTNAME': '3939c9a8890f', 'PYTHON_VERSION': '3.12.10', 'DB_REPORTS_USER': 'stockone', 'DB_REPLICA_HOST': 'db1', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'PWD': '/application', 'USE_S3': 'TRUE', 'DB_USER': 'stockone', 'HOME': '/root', 'LANG': 'C.UTF-8', 'DB_REPLICA_NAME': 'stockone', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'TERM': 'xterm', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_HOST': 'db1', 'AWS_ID': '********************', 'SHLVL': '1', 'DB_REPORTS_NAME': 'stockone', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DB_NAME': 'stockone', 'APPROVAL_SERVICE_URL': 'http://************:8001/api/v1/', 'TASK_USAGE': '0', 'DB_REPORTS_PASS': 'stockone^123', 'DB_REPORTS_HOST': 'db1', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'DB_REPLICA_PASS': 'stockone^123', 'AWS_S3_REGION_NAME': 'ap-south-1', 'DEBUG': '1', 'DB_PASS': 'stockone^123', 'ENTRYPOINT_FLAG': 'true', 'CONN_MAX_AGE': '300', 'APPROVAL_ORIGIN_NAME': 'dev-approval-app', '_': '/usr/local/bin/python'})
[2025-05-29 06:16:03,045] INFO _appmap.env: appmap enabled: False
[2025-05-29 06:16:45,430] INFO _appmap.configuration: file: /application/appmap.yml
[2025-05-29 06:16:45,431] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-05-29 06:16:45,431] INFO _appmap.configuration: env: environ({'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'PYTHONUNBUFFERED': '1', 'DB_REPLICA_USER': 'stockone', 'HOSTNAME': '3939c9a8890f', 'PYTHON_VERSION': '3.12.10', 'DB_REPORTS_USER': 'stockone', 'DB_REPLICA_HOST': 'db1', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'PWD': '/application', 'USE_S3': 'TRUE', 'DB_USER': 'stockone', 'HOME': '/root', 'LANG': 'C.UTF-8', 'DB_REPLICA_NAME': 'stockone', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'TERM': 'xterm', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_HOST': 'db1', 'AWS_ID': '********************', 'SHLVL': '1', 'DB_REPORTS_NAME': 'stockone', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DB_NAME': 'stockone', 'APPROVAL_SERVICE_URL': 'http://************:8001/api/v1/', 'TASK_USAGE': '0', 'DB_REPORTS_PASS': 'stockone^123', 'DB_REPORTS_HOST': 'db1', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'DB_REPLICA_PASS': 'stockone^123', 'AWS_S3_REGION_NAME': 'ap-south-1', 'DEBUG': '1', 'DB_PASS': 'stockone^123', 'ENTRYPOINT_FLAG': 'true', 'CONN_MAX_AGE': '300', 'APPROVAL_ORIGIN_NAME': 'dev-approval-app', '_': '/usr/local/bin/python'})
[2025-05-29 06:16:45,436] INFO _appmap.env: appmap enabled: False
[2025-05-29 06:27:20,943] INFO _appmap.env: appmap enabled: False
[2025-05-29 06:27:52,386] INFO _appmap.env: appmap enabled: False
[2025-05-29 06:28:10,321] INFO _appmap.env: appmap enabled: False
[2025-05-29 06:28:24,566] INFO _appmap.env: appmap enabled: False
[2025-05-29 06:28:40,422] INFO _appmap.env: appmap enabled: False
[2025-05-29 06:28:44,359] INFO _appmap.env: appmap enabled: False
[2025-05-29 06:29:12,064] INFO _appmap.env: appmap enabled: False
[2025-05-29 06:29:16,054] INFO _appmap.env: appmap enabled: False
[2025-05-29 06:29:16,906] INFO _appmap.env: appmap enabled: False
[2025-05-29 06:29:18,196] INFO _appmap.env: appmap enabled: False
[2025-05-29 06:29:19,990] INFO _appmap.env: appmap enabled: False
[2025-05-29 06:29:43,763] INFO _appmap.env: appmap enabled: False
[2025-05-29 06:29:50,818] INFO _appmap.env: appmap enabled: False
[2025-05-29 06:30:01,469] INFO _appmap.configuration: file: /application/appmap.yml
[2025-05-29 06:30:01,469] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-05-29 06:30:01,469] INFO _appmap.configuration: env: environ({'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'PYTHONUNBUFFERED': '1', 'DB_REPLICA_USER': 'stockone', 'HOSTNAME': '3939c9a8890f', 'PYTHON_VERSION': '3.12.10', 'DB_REPORTS_USER': 'stockone', 'DB_REPLICA_HOST': 'db1', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'PWD': '/application', 'USE_S3': 'TRUE', 'DB_USER': 'stockone', 'HOME': '/root', 'LANG': 'C.UTF-8', 'DB_REPLICA_NAME': 'stockone', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'TERM': 'xterm', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_HOST': 'db1', 'AWS_ID': '********************', 'SHLVL': '1', 'DB_REPORTS_NAME': 'stockone', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DB_NAME': 'stockone', 'APPROVAL_SERVICE_URL': 'http://************:8001/api/v1/', 'TASK_USAGE': '0', 'DB_REPORTS_PASS': 'stockone^123', 'DB_REPORTS_HOST': 'db1', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'DB_REPLICA_PASS': 'stockone^123', 'AWS_S3_REGION_NAME': 'ap-south-1', 'DEBUG': '1', 'DB_PASS': 'stockone^123', 'ENTRYPOINT_FLAG': 'true', 'CONN_MAX_AGE': '300', 'APPROVAL_ORIGIN_NAME': 'dev-approval-app', '_': '/usr/local/bin/python'})
[2025-05-29 06:30:01,474] INFO _appmap.env: appmap enabled: False
[2025-05-29 06:32:25,822] INFO _appmap.env: appmap enabled: False
[2025-05-29 06:32:30,401] INFO _appmap.env: appmap enabled: False
[2025-05-29 06:32:31,780] INFO _appmap.env: appmap enabled: False
[2025-05-29 06:32:36,104] INFO _appmap.env: appmap enabled: False
[2025-05-29 06:32:37,434] INFO _appmap.env: appmap enabled: False
[2025-05-29 06:32:47,871] INFO _appmap.env: appmap enabled: False
[2025-05-29 06:33:36,180] INFO _appmap.env: appmap enabled: False
[2025-05-29 06:34:03,974] INFO _appmap.env: appmap enabled: False
[2025-05-29 06:34:09,339] INFO _appmap.configuration: file: /application/appmap.yml
[2025-05-29 06:34:09,339] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-05-29 06:34:09,339] INFO _appmap.configuration: env: environ({'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'PYTHONUNBUFFERED': '1', 'DB_REPLICA_USER': 'stockone', 'HOSTNAME': '3939c9a8890f', 'PYTHON_VERSION': '3.12.10', 'DB_REPORTS_USER': 'stockone', 'DB_REPLICA_HOST': 'db1', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'PWD': '/application', 'USE_S3': 'TRUE', 'DB_USER': 'stockone', 'HOME': '/root', 'LANG': 'C.UTF-8', 'DB_REPLICA_NAME': 'stockone', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'TERM': 'xterm', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_HOST': 'db1', 'AWS_ID': '********************', 'SHLVL': '1', 'DB_REPORTS_NAME': 'stockone', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DB_NAME': 'stockone', 'APPROVAL_SERVICE_URL': 'http://************:8001/api/v1/', 'TASK_USAGE': '0', 'DB_REPORTS_PASS': 'stockone^123', 'DB_REPORTS_HOST': 'db1', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'DB_REPLICA_PASS': 'stockone^123', 'AWS_S3_REGION_NAME': 'ap-south-1', 'DEBUG': '1', 'DB_PASS': 'stockone^123', 'ENTRYPOINT_FLAG': 'true', 'CONN_MAX_AGE': '300', 'APPROVAL_ORIGIN_NAME': 'dev-approval-app', '_': '/usr/local/bin/python'})
[2025-05-29 06:34:09,343] INFO _appmap.env: appmap enabled: False
[2025-05-29 06:34:47,197] INFO _appmap.env: appmap enabled: False
[2025-05-29 06:34:48,468] INFO _appmap.env: appmap enabled: False
[2025-05-29 06:34:52,061] INFO _appmap.env: appmap enabled: False
[2025-05-29 06:35:07,709] INFO _appmap.env: appmap enabled: False
[2025-05-29 06:35:15,181] INFO _appmap.env: appmap enabled: False
[2025-05-29 06:35:18,175] INFO _appmap.env: appmap enabled: False
[2025-05-29 06:35:21,913] INFO _appmap.configuration: file: /application/appmap.yml
[2025-05-29 06:35:21,913] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-05-29 06:35:21,913] INFO _appmap.configuration: env: environ({'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'PYTHONUNBUFFERED': '1', 'DB_REPLICA_USER': 'stockone', 'HOSTNAME': '3939c9a8890f', 'PYTHON_VERSION': '3.12.10', 'DB_REPORTS_USER': 'stockone', 'DB_REPLICA_HOST': 'db1', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'PWD': '/application', 'USE_S3': 'TRUE', 'DB_USER': 'stockone', 'HOME': '/root', 'LANG': 'C.UTF-8', 'DB_REPLICA_NAME': 'stockone', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'TERM': 'xterm', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_HOST': 'db1', 'AWS_ID': '********************', 'SHLVL': '1', 'DB_REPORTS_NAME': 'stockone', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DB_NAME': 'stockone', 'APPROVAL_SERVICE_URL': 'http://************:8001/api/v1/', 'TASK_USAGE': '0', 'DB_REPORTS_PASS': 'stockone^123', 'DB_REPORTS_HOST': 'db1', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'DB_REPLICA_PASS': 'stockone^123', 'AWS_S3_REGION_NAME': 'ap-south-1', 'DEBUG': '1', 'DB_PASS': 'stockone^123', 'ENTRYPOINT_FLAG': 'true', 'CONN_MAX_AGE': '300', 'APPROVAL_ORIGIN_NAME': 'dev-approval-app', '_': '/usr/local/bin/python'})
[2025-05-29 06:35:21,918] INFO _appmap.env: appmap enabled: False
[2025-05-29 06:36:15,306] INFO _appmap.env: appmap enabled: False
[2025-05-29 06:36:23,603] INFO _appmap.env: appmap enabled: False
[2025-05-29 06:36:31,528] INFO _appmap.env: appmap enabled: False
[2025-05-29 06:36:45,991] INFO _appmap.env: appmap enabled: False
[2025-05-29 06:36:52,824] INFO _appmap.env: appmap enabled: False
[2025-05-29 06:37:28,635] INFO _appmap.configuration: file: /application/appmap.yml
[2025-05-29 06:37:28,636] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-05-29 06:37:28,636] INFO _appmap.configuration: env: environ({'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'PYTHONUNBUFFERED': '1', 'DB_REPLICA_USER': 'stockone', 'HOSTNAME': '3939c9a8890f', 'PYTHON_VERSION': '3.12.10', 'DB_REPORTS_USER': 'stockone', 'DB_REPLICA_HOST': 'db1', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'PWD': '/application', 'USE_S3': 'TRUE', 'DB_USER': 'stockone', 'HOME': '/root', 'LANG': 'C.UTF-8', 'DB_REPLICA_NAME': 'stockone', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'TERM': 'xterm', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_HOST': 'db1', 'AWS_ID': '********************', 'SHLVL': '1', 'DB_REPORTS_NAME': 'stockone', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DB_NAME': 'stockone', 'APPROVAL_SERVICE_URL': 'http://************:8001/api/v1/', 'TASK_USAGE': '0', 'DB_REPORTS_PASS': 'stockone^123', 'DB_REPORTS_HOST': 'db1', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'DB_REPLICA_PASS': 'stockone^123', 'AWS_S3_REGION_NAME': 'ap-south-1', 'DEBUG': '1', 'DB_PASS': 'stockone^123', 'ENTRYPOINT_FLAG': 'true', 'CONN_MAX_AGE': '300', 'APPROVAL_ORIGIN_NAME': 'dev-approval-app', '_': '/usr/local/bin/python', 'OLDPWD': '/application/logs'})
[2025-05-29 06:37:28,640] INFO _appmap.env: appmap enabled: False
[2025-05-29 06:38:22,651] INFO _appmap.configuration: file: /application/appmap.yml
[2025-05-29 06:38:22,651] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-05-29 06:38:22,651] INFO _appmap.configuration: env: environ({'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'PYTHONUNBUFFERED': '1', 'DB_REPLICA_USER': 'stockone', 'HOSTNAME': '3939c9a8890f', 'PYTHON_VERSION': '3.12.10', 'DB_REPORTS_USER': 'stockone', 'DB_REPLICA_HOST': 'db1', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'PWD': '/application', 'USE_S3': 'TRUE', 'DB_USER': 'stockone', 'HOME': '/root', 'LANG': 'C.UTF-8', 'DB_REPLICA_NAME': 'stockone', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'TERM': 'xterm', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_HOST': 'db1', 'AWS_ID': '********************', 'SHLVL': '1', 'DB_REPORTS_NAME': 'stockone', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DB_NAME': 'stockone', 'APPROVAL_SERVICE_URL': 'http://************:8001/api/v1/', 'TASK_USAGE': '0', 'DB_REPORTS_PASS': 'stockone^123', 'DB_REPORTS_HOST': 'db1', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'DB_REPLICA_PASS': 'stockone^123', 'AWS_S3_REGION_NAME': 'ap-south-1', 'DEBUG': '1', 'DB_PASS': 'stockone^123', 'ENTRYPOINT_FLAG': 'true', 'CONN_MAX_AGE': '300', 'APPROVAL_ORIGIN_NAME': 'dev-approval-app', '_': '/usr/local/bin/python', 'OLDPWD': '/application/logs'})
[2025-05-29 06:38:22,656] INFO _appmap.env: appmap enabled: False
[2025-05-29 06:39:24,336] INFO _appmap.env: appmap enabled: False
[2025-05-29 06:39:39,263] INFO _appmap.env: appmap enabled: False
[2025-05-29 06:39:50,436] INFO _appmap.env: appmap enabled: False
[2025-05-29 06:40:36,845] INFO _appmap.env: appmap enabled: False
[2025-05-29 06:41:40,320] INFO _appmap.env: appmap enabled: False
[2025-05-29 06:46:40,896] INFO _appmap.env: appmap enabled: False
[2025-05-29 06:46:53,891] INFO _appmap.env: appmap enabled: False
[2025-05-29 06:47:37,503] INFO _appmap.env: appmap enabled: False
[2025-05-29 06:48:14,842] INFO _appmap.env: appmap enabled: False
[2025-05-29 06:48:16,343] INFO _appmap.env: appmap enabled: False
[2025-05-29 06:48:24,611] INFO _appmap.env: appmap enabled: False
[2025-05-29 06:48:31,239] INFO _appmap.env: appmap enabled: False
[2025-05-29 06:48:53,591] INFO _appmap.env: appmap enabled: False
[2025-05-29 06:48:58,605] INFO _appmap.env: appmap enabled: False
[2025-05-29 06:49:00,331] INFO _appmap.env: appmap enabled: False
[2025-05-29 06:49:35,842] INFO _appmap.env: appmap enabled: False
[2025-05-29 06:50:25,609] INFO _appmap.env: appmap enabled: False
[2025-05-29 06:50:26,392] INFO _appmap.env: appmap enabled: False
[2025-05-29 06:50:56,049] INFO _appmap.env: appmap enabled: False
[2025-05-29 06:50:58,475] INFO _appmap.env: appmap enabled: False
[2025-05-29 06:51:18,596] INFO _appmap.env: appmap enabled: False
[2025-05-29 06:51:21,706] INFO _appmap.env: appmap enabled: False
[2025-05-29 06:51:24,270] INFO _appmap.env: appmap enabled: False
[2025-05-29 06:51:39,757] INFO _appmap.env: appmap enabled: False
[2025-05-29 06:51:48,056] INFO _appmap.env: appmap enabled: False
[2025-05-29 06:51:49,694] INFO _appmap.env: appmap enabled: False
[2025-05-29 06:51:51,137] INFO _appmap.env: appmap enabled: False
[2025-05-29 06:52:02,608] INFO _appmap.env: appmap enabled: False
[2025-05-29 06:52:05,550] INFO _appmap.env: appmap enabled: False
[2025-05-29 06:52:16,427] INFO _appmap.env: appmap enabled: False
[2025-05-29 06:52:33,798] INFO _appmap.env: appmap enabled: False
[2025-05-29 06:52:55,134] INFO _appmap.env: appmap enabled: False
[2025-05-29 06:53:06,341] INFO _appmap.env: appmap enabled: False
[2025-05-29 06:53:13,426] INFO _appmap.env: appmap enabled: False
[2025-05-29 06:53:30,853] INFO _appmap.env: appmap enabled: False
[2025-05-29 06:54:05,108] INFO _appmap.env: appmap enabled: False
[2025-05-29 06:54:09,368] INFO _appmap.env: appmap enabled: False
[2025-05-29 06:54:10,356] INFO _appmap.env: appmap enabled: False
[2025-05-29 06:54:11,723] INFO _appmap.env: appmap enabled: False
[2025-05-29 06:54:12,687] INFO _appmap.env: appmap enabled: False
[2025-05-29 06:54:41,386] INFO _appmap.env: appmap enabled: False
[2025-05-29 06:55:06,658] INFO _appmap.env: appmap enabled: False
[2025-05-29 06:55:20,901] INFO _appmap.env: appmap enabled: False
[2025-05-29 06:55:26,797] INFO _appmap.env: appmap enabled: False
[2025-05-29 06:55:39,161] INFO _appmap.configuration: file: /application/appmap.yml
[2025-05-29 06:55:39,161] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-05-29 06:55:39,161] INFO _appmap.configuration: env: environ({'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'PYTHONUNBUFFERED': '1', 'DB_REPLICA_USER': 'stockone', 'HOSTNAME': '3939c9a8890f', 'PYTHON_VERSION': '3.12.10', 'DB_REPORTS_USER': 'stockone', 'DB_REPLICA_HOST': 'db1', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'PWD': '/application', 'USE_S3': 'TRUE', 'DB_USER': 'stockone', 'HOME': '/root', 'LANG': 'C.UTF-8', 'DB_REPLICA_NAME': 'stockone', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'TERM': 'xterm', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_HOST': 'db1', 'AWS_ID': '********************', 'SHLVL': '1', 'DB_REPORTS_NAME': 'stockone', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DB_NAME': 'stockone', 'APPROVAL_SERVICE_URL': 'http://************:8001/api/v1/', 'TASK_USAGE': '0', 'DB_REPORTS_PASS': 'stockone^123', 'DB_REPORTS_HOST': 'db1', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'DB_REPLICA_PASS': 'stockone^123', 'AWS_S3_REGION_NAME': 'ap-south-1', 'DEBUG': '1', 'DB_PASS': 'stockone^123', 'ENTRYPOINT_FLAG': 'true', 'CONN_MAX_AGE': '300', 'APPROVAL_ORIGIN_NAME': 'dev-approval-app', '_': '/usr/local/bin/python', 'OLDPWD': '/application/logs'})
[2025-05-29 06:55:39,169] INFO _appmap.env: appmap enabled: False
[2025-05-29 06:55:51,879] INFO _appmap.configuration: file: /application/appmap.yml
[2025-05-29 06:55:51,880] INFO _appmap.configuration: config: [{"path": "application"}]
[2025-05-29 06:55:51,880] INFO _appmap.configuration: env: environ({'REDIS_URL': 'redis://:JguvU^FP4V@redis:6379', 'PYTHON_SHA256': '07ab697474595e06f06647417d3c7fa97ded07afc1a7e4454c5639919b46eaea', 'PYTHONUNBUFFERED': '1', 'DB_REPLICA_USER': 'stockone', 'HOSTNAME': '3939c9a8890f', 'PYTHON_VERSION': '3.12.10', 'DB_REPORTS_USER': 'stockone', 'DB_REPLICA_HOST': 'db1', 'AWS_STORAGE_BUCKET_NAME': 'mylocal-stockone-bucket', 'PWD': '/application', 'USE_S3': 'TRUE', 'DB_USER': 'stockone', 'HOME': '/root', 'LANG': 'C.UTF-8', 'DB_REPLICA_NAME': 'stockone', 'AWS_S3_SIGNATURE_VERSION': 's3v4', 'GPG_KEY': '7169605F62C751356D054A26A821E680E5FA6305', 'TERM': 'xterm', 'EMAIL_HOST_PASSWORD': 'API_KEY', 'DB_HOST': 'db1', 'AWS_ID': '********************', 'SHLVL': '1', 'DB_REPORTS_NAME': 'stockone', 'AWS_KEY': 'zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO', 'DB_NAME': 'stockone', 'APPROVAL_SERVICE_URL': 'http://************:8001/api/v1/', 'TASK_USAGE': '0', 'DB_REPORTS_PASS': 'stockone^123', 'DB_REPORTS_HOST': 'db1', 'PATH': '/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin', 'DB_REPLICA_PASS': 'stockone^123', 'AWS_S3_REGION_NAME': 'ap-south-1', 'DEBUG': '1', 'DB_PASS': 'stockone^123', 'ENTRYPOINT_FLAG': 'true', 'CONN_MAX_AGE': '300', 'APPROVAL_ORIGIN_NAME': 'dev-approval-app', '_': '/usr/local/bin/python', 'OLDPWD': '/application/logs'})
[2025-05-29 06:55:51,884] INFO _appmap.env: appmap enabled: False
[2025-05-29 06:57:06,523] INFO _appmap.env: appmap enabled: False
[2025-05-29 06:58:37,262] INFO _appmap.env: appmap enabled: False
[2025-05-29 06:59:08,643] INFO _appmap.env: appmap enabled: False
[2025-05-29 06:59:55,343] INFO _appmap.env: appmap enabled: False
[2025-05-29 07:00:28,841] INFO _appmap.env: appmap enabled: False
[2025-05-29 07:00:29,664] INFO _appmap.env: appmap enabled: False
